{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "digitory", "projects": {"digitory": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true, "standalone": true, "changeDetection": "OnPush"}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true, "standalone": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true, "standalone": true}, "@schematics/angular:resolver": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["moment"], "outputPath": "dist/digitory", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/bootstrap/scss/bootstrap.scss", "node_modules/@fortawesome/fontawesome-free/css/all.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "1mb", "maximumError": "1mb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "digitory:build:production"}, "development": {"browserTarget": "digitory:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "digitory:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/bootstrap/scss/bootstrap.scss", "node_modules/@fortawesome/fontawesome-free/css/all.css"], "scripts": []}}}}}, "cli": {"analytics": false}}