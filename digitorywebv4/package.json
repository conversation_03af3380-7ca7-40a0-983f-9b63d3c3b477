{"name": "digitory", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular-material-components/datetime-picker": "^16.0.1", "@angular/animations": "^16.0.0", "@angular/cdk": "^16.2.14", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/material": "^16.2.3", "@angular/material-moment-adapter": "^16.2.4", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@fortawesome/fontawesome-free": "^6.4.2", "@kolkov/angular-editor": "^3.0.0-beta.0", "@ng-idle/core": "^14.0.0", "@ng-idle/keepalive": "^14.0.0", "bootstrap": "^5.3.2", "chart.js": "^3.7.0", "date-fns": "^4.1.0", "luxon": "^3.5.0", "markdown-it": "13.0.1", "marked": "^4.3.0", "moment": "^2.30.1", "ng-gallery": "^11.0.0", "ng2-charts": "^5.0.3", "ngx-idle": "^1.0.0", "ngx-lightbox": "^3.0.0", "ngx-markdown": "^16.0.0", "ngx-mat-select-search": "^7.0.4", "ngx-material-timepicker": "^13.1.1", "ngx-skeleton-loader": "^8.1.0", "punycode": "^2.3.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.11", "@angular/cli": "~16.2.3", "@angular/compiler-cli": "^16.0.0", "@types/date-fns": "^2.6.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.0.2"}}