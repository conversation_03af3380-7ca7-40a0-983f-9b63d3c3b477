/**
 * Centralized Color Theme Configuration
 * Orange-based theme with mild professional colors
 */

export interface ColorTheme {
  primary: string;
  secondary: string;
  accent: string;
  neutral: string;
  info: string;
  success: string;
  warning: string;
  danger: string;
  light: string;
  dark: string;
}

export interface SemanticColors {
  positive: string;
  negative: string;
  neutral: string;
  warning: string;
  info: string;
  primary: string;
  inward: string;
  outward: string;
  purchase: string;
  inventory: string;
  sales: string;
  spoilage: string;
  transfer: string;
}

export interface ChartColorPalette {
  main: string[];
  backgrounds: string[];
  borders: string[];
  gradients: string[];
}

export interface GradientDefinitions {
  orangeLight: string;
  orangeWarm: string;
  sage: string;
  blueSoft: string;
  purpleMuted: string;
  grayWarm: string;
}

// Orange-based Color Palette - Main Theme
export const ORANGE_THEME: ColorTheme = {
  primary: '#ff8c42',
  secondary: '#ffb366',
  accent: '#87a96b',
  neutral: '#8d7b68',
  info: '#6b9bd2',
  success: '#87a96b',
  warning: '#f4a261',
  danger: '#d4a5a5',
  light: '#ffe0cc',
  dark: '#cc5a12'
};

// Semantic Colors for Data Visualization
export const SEMANTIC_COLORS: SemanticColors = {
  positive: '#87a96b',
  negative: '#d4a5a5',
  neutral: '#8d7b68',
  warning: '#f4a261',
  info: '#6b9bd2',
  primary: '#ff8c42',
  inward: '#6b9bd2',
  outward: '#d4a5a5',
  purchase: '#ff8c42',
  inventory: '#87a96b',
  sales: '#87a96b',
  spoilage: '#d4a5a5',
  transfer: '#8d7b68'
};

// Chart Color Palette with Orange-based mild professional colors
export const CHART_COLOR_PALETTE: ChartColorPalette = {
  main: [
    '#ff8c42',  // Orange primary
    '#ffb366',  // Orange light
    '#87a96b',  // Sage green
    '#6b9bd2',  // Soft blue
    '#9b7bb8',  // Muted purple
    '#8d7b68',  // Warm gray
    '#d4a5a5',  // Dusty rose
    '#ffc999',  // Orange lighter
    '#a4c085',  // Sage green light
    '#85aedb',  // Soft blue light
    '#af95c6',  // Muted purple light
    '#a69082',  // Warm gray light
    '#ddb8b8',  // Dusty rose light
    '#ffe0cc',  // Orange lightest
    '#f4a261'   // Warning orange
  ],
  backgrounds: [
    'rgba(255, 140, 66, 0.2)',   // Orange primary
    'rgba(255, 179, 102, 0.2)',  // Orange light
    'rgba(135, 169, 107, 0.2)',  // Sage green
    'rgba(107, 155, 210, 0.2)',  // Soft blue
    'rgba(155, 123, 184, 0.2)',  // Muted purple
    'rgba(141, 123, 104, 0.2)',  // Warm gray
    'rgba(212, 165, 165, 0.2)',  // Dusty rose
    'rgba(255, 201, 153, 0.2)',  // Orange lighter
    'rgba(164, 192, 133, 0.2)',  // Sage green light
    'rgba(133, 174, 219, 0.2)',  // Soft blue light
    'rgba(175, 149, 198, 0.2)',  // Muted purple light
    'rgba(166, 144, 130, 0.2)',  // Warm gray light
    'rgba(221, 184, 184, 0.2)',  // Dusty rose light
    'rgba(255, 224, 204, 0.2)',  // Orange lightest
    'rgba(244, 162, 97, 0.2)'    // Warning orange
  ],
  borders: [
    '#ff8c42',  // Orange primary
    '#ffb366',  // Orange light
    '#87a96b',  // Sage green
    '#6b9bd2',  // Soft blue
    '#9b7bb8',  // Muted purple
    '#8d7b68',  // Warm gray
    '#d4a5a5',  // Dusty rose
    '#ffc999',  // Orange lighter
    '#a4c085',  // Sage green light
    '#85aedb',  // Soft blue light
    '#af95c6',  // Muted purple light
    '#a69082',  // Warm gray light
    '#ddb8b8',  // Dusty rose light
    '#ffe0cc',  // Orange lightest
    '#f4a261'   // Warning orange
  ],
  gradients: [
    'linear-gradient(135deg, #ffe0cc 0%, #ffb366 50%, #ff8c42 100%)',
    'linear-gradient(135deg, #f0f4ec 0%, #a4c085 50%, #87a96b 100%)',
    'linear-gradient(135deg, #f0f6ff 0%, #85aedb 50%, #6b9bd2 100%)',
    'linear-gradient(135deg, #f5f0f8 0%, #af95c6 50%, #9b7bb8 100%)',
    'linear-gradient(135deg, #f8f6f4 0%, #a69082 50%, #8d7b68 100%)',
    'linear-gradient(135deg, #fff2f2 0%, #ddb8b8 50%, #d4a5a5 100%)'
  ]
};

// Gradient Definitions
export const GRADIENT_DEFINITIONS: GradientDefinitions = {
  orangeLight: 'linear-gradient(135deg, #ffe0cc 0%, #ffb366 50%, #ff8c42 100%)',
  orangeWarm: 'linear-gradient(135deg, #fff2e6 0%, #ffb366 50%, #ff8c42 100%)',
  sage: 'linear-gradient(135deg, #f0f4ec 0%, #a4c085 50%, #87a96b 100%)',
  blueSoft: 'linear-gradient(135deg, #f0f6ff 0%, #85aedb 50%, #6b9bd2 100%)',
  purpleMuted: 'linear-gradient(135deg, #f5f0f8 0%, #af95c6 50%, #9b7bb8 100%)',
  grayWarm: 'linear-gradient(135deg, #f8f6f4 0%, #a69082 50%, #8d7b68 100%)'
};

// Dark theme variants
export const DARK_THEME_COLORS = {
  primary: '#ff9d5c',
  secondary: '#ffcc80',
  accent: '#9ccc65',
  neutral: '#a1887f',
  info: '#81c784',
  background: '#2d2d2d',
  surface: '#3d3d3d',
  text: '#ffffff'
};

// Utility functions
export class ColorThemeUtils {
  /**
   * Get color by semantic meaning
   */
  static getSemanticColor(type: keyof SemanticColors): string {
    return SEMANTIC_COLORS[type];
  }

  /**
   * Get chart color by index
   */
  static getChartColor(index: number): string {
    return CHART_COLOR_PALETTE.main[index % CHART_COLOR_PALETTE.main.length];
  }

  /**
   * Get chart background color by index
   */
  static getChartBackgroundColor(index: number): string {
    return CHART_COLOR_PALETTE.backgrounds[index % CHART_COLOR_PALETTE.backgrounds.length];
  }

  /**
   * Get chart border color by index
   */
  static getChartBorderColor(index: number): string {
    return CHART_COLOR_PALETTE.borders[index % CHART_COLOR_PALETTE.borders.length];
  }

  /**
   * Get gradient by name
   */
  static getGradient(name: keyof GradientDefinitions): string {
    return GRADIENT_DEFINITIONS[name];
  }

  /**
   * Generate color array for charts
   */
  static generateChartColors(count: number): {
    backgroundColor: string[];
    borderColor: string[];
  } {
    const backgroundColor: string[] = [];
    const borderColor: string[] = [];

    for (let i = 0; i < count; i++) {
      backgroundColor.push(this.getChartBackgroundColor(i));
      borderColor.push(this.getChartBorderColor(i));
    }

    return { backgroundColor, borderColor };
  }

  /**
   * Get contextual color based on value and context
   */
  static getContextualColor(value: number, context: string = 'default'): string {
    switch (context) {
      case 'growth':
      case 'profit':
        return value >= 0 ? SEMANTIC_COLORS.positive : SEMANTIC_COLORS.negative;
      case 'loss':
      case 'spoilage':
        return SEMANTIC_COLORS.negative;
      case 'movement':
      case 'transfer':
        return SEMANTIC_COLORS.info;
      case 'warning':
        return SEMANTIC_COLORS.warning;
      default:
        return SEMANTIC_COLORS.primary;
    }
  }
}
