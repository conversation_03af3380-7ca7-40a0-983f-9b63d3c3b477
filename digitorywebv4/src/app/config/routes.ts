export const routesConfig = {
  dashboard: {
    routerPath: 'dashboard',
    label: 'Dashboard',
    routerLink: '/dashboard',
    childRoutes: {
      home: {
        routerPath: 'home',
        routerLink: '/dashboard/home',
        label: 'Dashboard',
      },
      setting: {
        routerPath: 'setting',
        routerLink: '/dashboard/setting',
        label: 'Dashboard',
      },
      recipe: {
        routerPath: 'recipe',
        routerLink:'/dashboard/recipe',
        label: 'Recipe Management'
      },
      inventory:{
        routerPath:'inventory',
        routerLink:'/dashboard/inventory',
        label:'Inventory Management',
      },
      user:{
        routerPath:'user',
        routerLink:'/dashboard/user',
        label:'User Management',
      },
      account:{
        routerPath:'account',
        routerLink:'/dashboard/account',
        label:'Crm Management',
      },
      accountSetup:{
        routerPath:'account-setup',
        routerLink:'/dashboard/account-setup',
        label:'Account Setup',
      },
      party: {
        routerPath: 'party',
        routerLink: '/dashboard/party',
        label: 'Party management',
      },
      smartDashboard: {
        routerPath: 'smart-dashboard',
        routerLink: '/dashboard/smart-dashboard',
        label: 'Smart Dashboard',
      }
    },
  },

  signup: {
    routerPath: 'signup',
    label: 'Signup',
    routerLink:''
  },

  signin: {
    routerPath: 'signin',
    label: 'Sign in',
    routerLink:'/signin'
  },

} ;
