.mat-list-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
}

span.mat-line {
    font-size: 16px;
}

.mat-list-item-hover:hover {
    background-color: #f2f2f2;
}

.custom-toggle {
    margin-left: auto;
    vertical-align: middle;
}

.custom-text {
    font-size: 16px;
    margin-right: 150px;
}
.bottomSheetIcons{
    float: right;
}

::ng-deep .mat-bottom-sheet-container-medium {
    min-width: 500px !important;
}

::ng-deep  mat-bottom-sheet-container{
    border-top-right-radius: 15px !important;
    border-top-left-radius: 15px !important;
}

.confromationText{
    font-size: medium;
    text-align: center;
    font-weight: bold;
}

.conformationText{
    font-size: 15px;
    text-align: center;
    font-weight: bold;
}

.disabled{
    opacity: 0.5;
}

.errorDialog{
    max-height: 75vh;
    overflow: auto;
}
  
.errorData{
    font-size: medium;
    margin: 15px;
}

.errorCheckIcon{
    margin-left: 8px;
}

.errormsg{
    color: crimson;
    font-size: 13px;
}
