import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatBottomSheetModule, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule, _MatSlideToggleRequiredValidatorModule } from '@angular/material/slide-toggle';
import { FormsModule } from '@angular/forms';
import { MasterDataService } from 'src/app/services/master-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { InventoryService } from 'src/app/services/inventory.service';
import { SyncDataComponent } from '../recipe-management/sync-data/sync-data.component';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import * as XLSX from 'xlsx';
import { NotificationService } from 'src/app/services/notification.service';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { first } from 'rxjs';
import { Router } from '@angular/router';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-bottom-sheet',
  standalone: true,
  imports: [
    CommonModule,
    MatProgressSpinnerModule,
    MatBottomSheetModule,
    MatListModule,
    MatIconModule,
    MatSlideToggleModule,
    _MatSlideToggleRequiredValidatorModule,
    MatTooltipModule,
    NgxSkeletonLoaderModule,
    FormsModule, MatButtonModule,
  ],
  templateUrl: './bottom-sheet.component.html',
  styleUrls: ['./bottom-sheet.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BottomSheetComponent {
  @ViewChild('fileInput') fileInput: ElementRef;
  @ViewChild('processing', { static: true }) processing: TemplateRef<any>;
  @ViewChild('openExcelError') openExcelError: TemplateRef<any>;
  isChecked = false;
  user: any;
  baseData: any;
  syncType: string;
  currentSession: any;
  masterDateConfig: any;
  clientName: any;
  closeInvRef: MatDialogRef<unknown, any>;
  closeExcelRef: MatDialogRef<unknown, any>;
  closeExportRef: MatDialogRef<unknown, any>;
  closeImportRef: MatDialogRef<unknown, any>;
  closeUploadExcelRef: MatDialogRef<unknown, any>;
  sessionType: string;
  hasModifiedEntry: boolean = true;
  isDataReady: boolean = false;
  excelData: XLSX.WorkBook;
  transformedData: any;
  enableExportAndImport: boolean = false;
  excelUploadDone = true;
  syncTypeName: string;
  hasPackageError: boolean;
  mandatory_columns: {
    'inventory master': string[];
    'packagingmasters': string[];
    'menu master': string[];
    'menu recipes': string[];
    'Subrecipe Master': string[];
    'Subrecipe Recipe': string[];
    'servingsize conversion': string[];
    'users': string[];
    'branches': string[];
    'Roles': string[];
    'vendors': string[];
    'menu-to-workArea-mapping': string[];
  };

  errorData: any[] = [];
  newArray: any;
  tempSheetNames: any[] = [];
  allowToUpload: boolean = false;
  navigateData: string;
  isButtonDisabled = false;
  checkSyncAvailable: boolean = false;
  isLoading : boolean = false;
  isImportDone: boolean = false;
  formattedDate: any;
  formattedTime: any;
  mappingData: any[] = [];

  constructor(
    private masterDataService: MasterDataService,
    private auth: AuthService,
    private api: InventoryService,
    public dialog: MatDialog,
    private _bottomSheetRef: MatBottomSheetRef,
    private notify: NotificationService,
    private sharedData: ShareDataService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.baseData = this.sharedData.getBaseData().value
    this.masterDataService.isChecked$.subscribe((isChecked) => {
      this.isChecked = isChecked;
    });
    this.user = this.auth.getCurrentUser();
    this.sharedData.checkUploadAvailable.subscribe(data => {
      this.enableExportAndImport = data
    });

    const currentUrl = window.location.href.split('/');
    const currentPage = currentUrl[currentUrl.length - 1];
    const pageType = currentPage.includes('?') ? currentPage.split('?')[0] : currentPage;
    switch (pageType) {
      case 'recipe':
      case 'Subrecipe Master':
        this.syncType = 'recipeManagement';
        this.syncTypeName = "Recipe"
        this.sessionType = "recipe"
        this.navigateData = 'menu master'
        break;
      case 'user':
        this.syncType = 'userManagement';
        this.syncTypeName = "User"
        this.sessionType = "user"
        this.navigateData = 'user'
        break;
      default:
        this.syncType = 'inventoryManagement';
        this.syncTypeName = "Inventory"
        this.sessionType = "inventory"
        this.navigateData = 'inventoryList'
        break;
    }
    this.getTenantData();
    this.getBaseData();
  }

  ngOnInit(): void {
    this.getMappingData();
    const currentDate = new Date();
    const options = { timeZone: 'Asia/Kolkata' };
    this.formattedDate = currentDate.toLocaleDateString('en-US', options);
    this.formattedTime = currentDate.toLocaleTimeString('en-US', { ...options, hour: '2-digit', minute: '2-digit', hour12: true });
  }

  preview() {
    this.masterDataService.setChecked(this.isChecked);
  }

  syncToInventory() {
    let scenario: string, requiredSheets: any;
    if (this.syncType === 'inventoryManagement') {
      scenario = 'UI-Data-Management';
    } else if (this.syncType === 'userManagement') {
      scenario = 'UI-User-Management';
    } else if (this.syncType === 'recipeManagement') {
      scenario = 'UI-Recipe-Management';
    }
    let requiredData = this.masterDateConfig['sheets'].find((el) => el.scenario == scenario);
    requiredSheets = requiredData ? requiredData['value'] : '';
    const obj = {
      selectedClientId: this.user.tenantId,
      tenantId: this.user.tenantId,
      email: this.user.email,
      syncEmail: this.user.email,
      type: this.sessionType,
      server: this.masterDateConfig['servers'],
      client: this.clientName,
      sheets: requiredSheets,
      dbName: this.masterDateConfig['dbName'],
      category: this.syncType ? this.syncType : '',
      session: this.currentSession ? this.currentSession : 'NA',
      status: 'pending',
    };
    this.api.syncToInventory(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          this.notify.snackBarShowSuccess(res['message']);
          this.syncHistory();
        } else {
          this.notify.snackBarShowError(res['message']);
        }
      },
      error: (err) => { console.log(err); },
    });
  }

  syncHistory() {
    const dialogRef = this.dialog.open(SyncDataComponent, {
      autoFocus: false,
      disableClose: true,
      maxHeight: '95vh',
      data: [],
    });
    dialogRef.afterClosed().subscribe((result) => { });
  }

  getBaseData() {
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['userEmail'] = this.user.email;
    obj['type'] = this.sessionType;
    obj['specific'] = 'NAN';
    this.api.getPresentData(obj).subscribe({
      next: (res) => {
        if (res['success']) {
          this.checkValidation(this.baseData);
          this.isDataReady = true;
          this.cd.detectChanges();
          this.currentSession = (res['sessionId'] && res['sessionId'] != 0) ? res['sessionId'] : (res['sessionId'] != 0 ? this.notify.snackBarShowError('Invalid sessionId Please contact support') : undefined)
        } else {
          this.baseData = [];
        }
      },
      error: (err) => { console.log(err); }
    });
  }

  openExcel(createSyncExcel) {
    let dialogRef = this.dialog.open(createSyncExcel, { maxHeight: '95vh', maxWidth: '500px' });
    this.closeExcelRef = dialogRef
  }

  closeSyncExcel() {
    this.closeExcelRef.close()
  }

  showUploadExcel(uploadSyncExcel) {
    let dialogRef = this.dialog.open(uploadSyncExcel, { maxHeight: '95vh', maxWidth: '500px' });
    this.closeUploadExcelRef = dialogRef
  }

  closeUploadExcel() {
    this.closeUploadExcelRef.close()
  }

  export(exportExcel) {
    let dialogRef = this.dialog.open(exportExcel, { maxHeight: '95vh', maxWidth: '500px' });
    this.closeExportRef = dialogRef
  }

  closeExportExcel() {
    this.closeExportRef.close()
  }

  import(importExcel) {
    let dialogRef = this.dialog.open(importExcel, { maxHeight: '95vh', maxWidth: '500px' });
    this.closeImportRef = dialogRef
  }

  closeImportExcel() {
    this.closeImportRef.close()
  }

  exportToExcel(): void {
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    const currentDate = new Date();
    const options = { timeZone: 'Asia/Kolkata' };
    const formattedDate = currentDate.toLocaleDateString('en-US', options);
    const formattedTime = currentDate.toLocaleTimeString('en-US', { ...options, hour: '2-digit', minute: '2-digit', hour12: true });
    const fileName = `${this.user.tenantId}_${this.sessionType}_${formattedDate}_${formattedTime}.xlsx`;

    const generateExcelAsync = async () => {
      const allData = [];
      for (const category in this.baseData) {
        if (this.baseData.hasOwnProperty(category)) {
          const sheetName = category.replace(/ /g, '_');
          const dataArray: any[] = this.baseData[category];
          const modifiedColumnExists = dataArray.length > 0 && 'modified' in dataArray[0];
          let currentData = this.isChecked
            ? dataArray
              .filter((el) => el.changed && el.changed == true)
              .map((el) => (modifiedColumnExists ? el : { ...el, modified: 'no' }))
            : dataArray.map((el) => (modifiedColumnExists ? el : { ...el, modified: 'no' }));
          currentData = currentData.map((el) => {
            const { tenantId, ...rest } = el;
            return { tenantId: this.user.tenantId, ...rest };
          });
          if (category === 'inventory master') {
            currentData = currentData.map((el) => {
              const { inventoryUom, ...rest } = el;
              return rest;
            });
          }
          
          const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(currentData);
          var desiredHeaderOrder
          if (sheetName === 'inventory_master') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['inventory master']
          } else if (sheetName === 'packagingmasters') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['packagingmasters']
          } else if (sheetName === 'vendors') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['vendors']
          } else if (sheetName === 'Roles') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['Roles']
          } else if (sheetName === 'users') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['users']
          } else if (sheetName === 'branches') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['branches']
          } else if (sheetName === 'menu_master') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['menu master']
          } else if (sheetName === 'menu_recipes') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['menu recipes']
          } else if (sheetName === 'Subrecipe_Master') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['Subrecipe Master']
          } else if (sheetName === 'Subrecipe_Recipe') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['Subrecipe Recipe']
          } else if (sheetName === 'Subrecipe_Master') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['Subrecipe Master']
          } else if (sheetName === 'servingsize_conversion') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['servingsize conversion']
          } else if (sheetName === 'menu-to-workArea-mapping') {
            desiredHeaderOrder = this.masterDataService.reArrangeColumns['menu-to-workArea-mapping']
          }
          const rearrangedData = currentData.map(row => {
            const newRow: Record<string, any> = {};
            for (const key of desiredHeaderOrder) {
              if (row.hasOwnProperty(key)) {
                newRow[key] = row[key];
              } else {
                newRow[key] = '';
              }
            }
            
            for (const key in row) {
              if (!newRow.hasOwnProperty(key)) {
                newRow[key] = row[key];
              }
            }
            return newRow;
          });
          

          const rearrangedWs: XLSX.WorkSheet = XLSX.utils.json_to_sheet(rearrangedData);
          XLSX.utils.book_append_sheet(wb, rearrangedWs, sheetName);
        }
      }

      XLSX.writeFile(wb, fileName);
      this.excelData = wb;
      this.cd.detectChanges();
      this.closeExcelRef.close();
      this.closeSyncExcel();
    };
    generateExcelAsync();
  }

  close(event) {
    this._bottomSheetRef.dismiss();
    event.preventDefault();
  }

  syncInventory(createSyncInventory) {
    if (this.hasModifiedEntry === false) {
      this.notify.snackBarShowWarning('Taking sync is not permitted, I cannot locate any modified rows');
    } else if (this.hasPackageError === true) {
      this.notify.snackBarShowWarning('Taking sync is not permitted, please resolve all exceptions');
    } else {
      this.closeInvRef = this.dialog.open(createSyncInventory, { maxHeight: '95vh', maxWidth: '500px' });
    }
  }

  closeSyncInventory() {
    this.closeInvRef.close()
  }

  create() {
    this.api.getConfig().subscribe({
      next: (res) => {
        if (res['success']) {
          this.masterDateConfig = res['data'];
          this.syncToInventory();
        } else {
          this.masterDateConfig = [];
          this.notify.snackBarShowWarning('MasterData configuration missing!');
        }
      },
      error: (err) => { console.log(err); }
    });
    this.closeSyncInventory();
  }

  getTenantData() {
    this.api.getTenantConfigDetails(this.user.tenantId).subscribe({
      next: (res) => {
        if (res['success']) {
          let tenantDetails = res['data'];
          this.clientName = tenantDetails['full'];
          this.getHistory(tenantDetails['full']);
        } else {
          this.notify.snackBarShowError('Tenant Configurations are missing');
          this.clientName = 'NA';
        }
      },
      error: (err) => { console.log(err); }
    });
  }

  getHistory(client) {
    let obj = this.user;
    obj['client'] = client;
    obj['type'] = this.syncType;
    this.api.retrieveHistory(obj).subscribe({
      next: (res) => {
        if (res['success'] && res['data'].length > 0) {
          this.checkSyncAvailable = res['data'].some(item => item.status === 'pending');
          this.cd.detectChanges();
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
  }

  checkUpload(uploadSyncExcel) {
    this.closeUploadExcelRef = this.dialog.open(uploadSyncExcel, { maxHeight: '95vh', maxWidth: '500px' });
  }

  public fileChangeListener($event: any): void {
    this.excelUploadDone = false;
    this.cd.detectChanges();
    const file = $event.target.files[0];
    this.readFile(file);
  }

  private readFile(file: File): void {
    var validationResult: any;
    this.notify.snackBarForSync('File upload in progress');
    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      const data: string = e.target.result;
      const workbook: XLSX.WorkBook = XLSX.read(data, { type: 'binary' });
      const jsonData: any[] = [];
      workbook.SheetNames.forEach(sheetName => {
        const worksheet: XLSX.WorkSheet = workbook.Sheets[sheetName];
        const sheetData: any[] = XLSX.utils.sheet_to_json(worksheet, { raw: true });
        const hasModifiedColumn = sheetData.length > 0 && 'modified' in sheetData[0];
        const excelHeaderJsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        let headers = excelHeaderJsonData[0];

        if (sheetName === 'inventory_master') {
          const renamedSheetName = sheetName.toLowerCase() === 'inventory_master' ? 'inventory master' : sheetName;
          sheetName = renamedSheetName
        } else if (sheetName === 'menu_master') {
          const renamedSheetName = sheetName.toLowerCase() === 'menu_master' ? 'menu master' : sheetName;
          sheetName = renamedSheetName
        } else if (sheetName === 'menu_recipes') {
          const renamedSheetName = sheetName.toLowerCase() === 'menu_recipes' ? 'menu recipes' : sheetName;
          sheetName = renamedSheetName
        } else if (sheetName === 'Subrecipe_Master') {
          const renamedSheetName = sheetName === 'Subrecipe_Master' ? 'Subrecipe Master' : sheetName;
          sheetName = renamedSheetName
        } else if (sheetName === 'Subrecipe_Recipe') {
          const renamedSheetName = sheetName === 'Subrecipe_Recipe' ? 'Subrecipe Recipe' : sheetName;
          sheetName = renamedSheetName
        } else if (sheetName === 'servingsize_conversion') {
          const renamedSheetName = sheetName === 'servingsize_conversion' ? 'servingsize conversion' : sheetName;
          sheetName = renamedSheetName
        }

        this.tempSheetNames.push(sheetName)
        this.mandatory_columns = this.masterDataService.mandatory_columns
        const expectedColumns = this.mandatory_columns[sheetName]
        let check: any;
        validationResult = this.validateSheetHeader(sheetName, headers, expectedColumns, check);
        this.errorData.push(validationResult);
        this.tempSheetNames.forEach(sheetName => {
          const foundObject = this.errorData.find(obj => obj.sheetName === sheetName);
          if (!foundObject) {
            this.errorData.push({ "sheetName": sheetName, "missingColumns": [], "check": true });
          }
        });

        const hasFalseCheck = this.errorData.some(item => item.check === false);
        this.allowToUpload = hasFalseCheck ? false : true;
        if (validationResult.missingColumns.length > 0) {
          this.openDialog(validationResult);
          this.snackBar.dismiss();
          this.notify.snackBarShowError('Please add the missing columns and re-upload the sheet');
        } else {
          if (hasModifiedColumn) {
            sheetData.forEach(row => {
              row['modified'] = ['y', 'yes'].includes(row['modified'].toLowerCase()) ? 'yes' : 'no';
              row['Discontinued'] = ['y', 'yes'].includes(row['Discontinued']) ? 'yes' : (row['Discontinued'] ? 'no' : 'no');
            });
          }
          jsonData.push({ sheetName, data: sheetData });
        }
      });

      if (this.allowToUpload === true) {
        this.baseData = jsonData.reduce((acc, item) => {
          acc[item.sheetName] = item.data;
          return acc;
        }, {});

        if (this.baseData.hasOwnProperty('inventory_master')) {
          this.baseData['inventory master'] = this.baseData['inventory_master'];
          delete this.baseData['inventory_master'];
        }

        if(this.baseData['users']){
          const uniqueData = this.baseData['users'].filter((user, index, self) =>
          index === self.findIndex((t) => (
            t.email === user.email 
          ))
        );
        this.baseData['users'] = uniqueData;
        }

        if(this.baseData['branches']){
          const uniqueData = this.baseData['branches'].filter((user, index, self) =>
          index === self.findIndex((t) => (
            t.restaurantId === user.restaurantId 
          ))
        );
        this.baseData['branches'] = uniqueData;
        }

        this.fileInput.nativeElement.value = '';
        if (Object.keys(this.baseData).length > 0) {
          let obj = {}
          obj['tenantId'] = this.user.tenantId
          obj['userEmail'] = this.user.email
          obj['data'] = this.baseData
          obj['type'] = this.sessionType
          obj['excelUpload'] = true;
          this.api.updateData(obj).pipe(first()).subscribe({
            next: (res) => {
              if (res['success']) {
                this.snackBar.dismiss();
                this.excelUploadDone = true;
                this.cd.detectChanges();
                this.notify.snackBarShowSuccess('uploaded successfully');
                this.sharedData.setBaseData([])
                this.masterDataService.setNavigation(this.navigateData);
                this.router.navigate(['/dashboard/home']);
                this._bottomSheetRef.dismiss();
              }
            },
            error: (err) => { console.log(err) }
          });
        }
        this.closeUploadExcel()
      }
    };
    reader.readAsBinaryString(file);
  }

  filterModifiedYes(data) {
    const modifiedYesArray = data?.filter(item => item.modified === 'yes');
    return modifiedYesArray.length > 0 ? modifiedYesArray : data;
  }

  openDialog(validationResult): void {
    const dialogRef = this.dialog.open(this.openExcelError, {
      maxWidth: '50vw',
      autoFocus: false,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe(result => {
      this.errorData = []
    });
  }

  closeDialog(): void {
    this.dialog.closeAll();
  }

  validateSheetHeader(sheetName: string, headerRow: any, expectedColumns: string[], check: boolean): { sheetName: string, missingColumns: string[], check: boolean } {
    if (!headerRow) {
      return { sheetName, missingColumns: [], check: true };
    }
    headerRow = headerRow.filter(value => value !== null && value !== undefined);
    const missingColumns = expectedColumns.filter(item => !headerRow.includes(item));
    check = missingColumns.length === 0 ? true : false;
    return { sheetName, missingColumns, check };
  }

  isMandatoryPackagePresent(item: any, pkgs: any) {
    return pkgs.some(pkg => pkg.InventoryCode === item.itemCode && pkg.Discontinued !== "yes");
  }

  isMandatoryMenuRecipePresent(item: any, menuRec: any) {
    // return menuRec.some(data => data.menuItemName === item.menuItemName && data.Discontinued !== "yes");
    return menuRec.length === 0 ? true : false;
  }

  checkValidation(data: any) {
    if (this.syncType === "inventoryManagement") {
      let unmatchedItemsWithError = data['inventory master'].filter(item => !this.isMandatoryPackagePresent(item, data['packagingmasters'].filter(pkg => pkg.InventoryCode == item['itemCode'])));
      unmatchedItemsWithError = unmatchedItemsWithError.filter(item => item.Discontinued === 'no');
      let modifiedInv = data['inventory master']?.some(invItem => invItem.modified === 'yes') ?? false;
      let modifiedVendors = data['vendors']?.some(invItem => invItem.modified === 'yes') ?? false;
      this.hasModifiedEntry = modifiedInv || modifiedVendors
      this.hasPackageError = unmatchedItemsWithError.length > 0 ? true : false;
    } else if (this.syncType === "userManagement") {
      let modifiedRoles = data['Roles']?.some(item => item.modified === 'yes') ?? false;
      let modifiedBranches = data['branches']?.some(item => item.modified === 'yes') ?? false;
      let modifiedUsers = data['users']?.some(item => item.modified === 'yes') ?? false;
      this.hasModifiedEntry = modifiedRoles || modifiedBranches || modifiedUsers
    } else {
      // let unmatchedItemsWithError = data['menu master'].filter(item => this.isMandatoryMenuRecipePresent(item, data['menu recipes'].filter(menuRec => menuRec.menuItemName == item['menuItemName'])));
      // unmatchedItemsWithError = unmatchedItemsWithError.filter(item => item.Discontinued === 'no');
      let modifiedMenuMaster = data['menu master']?.some(item => item.modified === 'yes') ?? false;
      let modifiedSubRecipeMaster = data['Subrecipe Master']?.some(item => item.modified === 'yes') ?? false;
      let modifiedSubRecipeRecipe = data['Subrecipe Recipe']?.some(item => item.modified === 'yes') ?? false;
      let modifiedServing = data['servingsize conversion']?.some(item => item.modified === 'yes') ?? false;
      this.hasModifiedEntry = modifiedMenuMaster || modifiedSubRecipeMaster || modifiedSubRecipeRecipe || modifiedServing
      // this.hasPackageError = unmatchedItemsWithError.length > 0 ? true : false;
    }
  }

  resetData() {
    this.sharedData.setPOSItems([]);
    this.sharedData.setRecipeNames({}, []);
    this.sharedData.setItemType( true);
    this.masterDataService.setNavigation('menu master');
    this.router.navigate(['/dashboard/home']);
    this.sharedData.setDefaultPriceTier(
      0
    );
  }

  getMappingData() {
    this.isLoading = true;
    let obj = {
      "tenantId" : this.user.tenantId,
      "export" : true,
    }
    this.api.getMenuMappingList(obj).pipe(first()).subscribe({
      next: (res) => {
        if (res['status']) {
          this.mappingData = res['data']
        } else {
          this.notify.snackBarShowError('Something Went Wrong!');
        }
        this.isLoading = false ;
        this.cd.detectChanges();
      },
      error: (err) => { console.log(err); }
    });
  }

  exportMenuMapping(){
    const fileName = `${this.user.tenantId}_Menu_Mapping_${this.formattedDate}_${this.formattedTime}.xlsx`;
    const wb: XLSX.WorkBook = XLSX.utils.book_new();      
    if (this.mappingData.length === 0) {
        const header = [
            'tenantId',
            'restaurantId',
            'storeId',
            'itemCode',
            'itemName',
            'floorNo',
            'section',
            'workArea'
        ];
        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([header]);
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    } else {
        const filteredData = this.mappingData.map(({ _id, ...rest }) => rest);
        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(filteredData);
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    }      
    XLSX.writeFile(wb, fileName);
    this.closeExportExcel();
  }

  public changeFileListener($event: any): void {
    this.closeImportExcel();
    this.notify.snackBarShowSuccess('File uploaded successfully!, please wait for results');

    const file = $event.target.files[0];
    const reader = new FileReader();
    
    reader.onload = (e: any) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        let dataArray: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        dataArray = dataArray.filter(row => row.some(cell => cell !== null && cell !== undefined && cell !== ""));
        const headers = dataArray[0];
        const objectsArray = dataArray.slice(1).map((row: any) => {
            const obj: any = {};
            headers.forEach((header, index) => {
                obj[header] = row[index];
            });            
            return obj;
        });

        const uniqueKeys = new Set<string>(); // Set to store unique keys
        const duplicateRowMap = new Map<number, { itemCodes: Set<string>; restaurantIds: Set<string>; sections: Set<string>; workAreas: Set<string> }>();
        objectsArray.forEach((rowObj, index) => {
            // Create a unique key for each row based on selected columns
            const uniqueKey = `${rowObj['tenantId']}_${rowObj['restaurantId']}_${rowObj['storeId']}_${rowObj['itemCode']}
                              _${rowObj['itemName']}_${rowObj['floorNo']}_${rowObj['section']}_${rowObj['workArea']}`;
            if (uniqueKeys.has(uniqueKey)) {
                // Add details to the map for duplicate rows
                if (!duplicateRowMap.has(index + 2)) {
                    duplicateRowMap.set(index + 2, {
                        itemCodes: new Set(),
                        restaurantIds: new Set(),
                        sections: new Set(),
                        workAreas: new Set()
                    });
                }
                duplicateRowMap.get(index + 2)!.itemCodes.add(rowObj['itemCode']);
                duplicateRowMap.get(index + 2)!.restaurantIds.add(rowObj['restaurantId']);
                duplicateRowMap.get(index + 2)!.sections.add(rowObj['section']);
                duplicateRowMap.get(index + 2)!.workAreas.add(rowObj['workArea']);
            } else {
                uniqueKeys.add(uniqueKey);
            }
        });

        if (duplicateRowMap.size > 0) {
            // Generate error message with section and workArea details
            const errorText = Array.from(duplicateRowMap.entries())
                .map(([row, details]) => {
                    return `Row ${row}: Duplicate rows detected. 'itemCode': ${Array.from(details.itemCodes).join(', ')}, 'restaurantId': ${Array.from(details.restaurantIds).join(', ')}, 'section': ${Array.from(details.sections).join(', ')}, 'workArea': ${Array.from(details.workAreas).join(', ')}`;
                })
                .join('\n');
            this.downloadErrorTxt(`duplicate-rows-error_${this.formattedDate}_${this.formattedTime}.txt`, errorText);
            this.notify.snackBarShowError('Duplicate rows found, check the error log from downloads');
            return; // Stop further processing
        }

        // Check if all required columns exist
        const requiredColumns = ['tenantId', 'restaurantId', 'storeId', 'itemCode','itemName', 'floorNo', 'section', 'workArea'];
        const headerRow = dataArray[0];
        const missingColumns = requiredColumns.filter(col => !headerRow.includes(col));        
        if (missingColumns.length > 0) {
          this.downloadErrorTxt(`missing-columns-error_${this.formattedDate}_${this.formattedTime}.txt`, `Missing columns: ${missingColumns.join(', ')}`);
          this.notify.snackBarShowError('Please fix the errors, check the error log from downloads');
          return; // Stop processing if any required column is missing
        }

        // Check if any of the required fields are empty in any row
        const rowsWithEmptyFields: { row: number, missingColumns: string[], missingFields: string[] }[] = [];
        dataArray.slice(1).forEach((row, index) => {
          const missingCols = requiredColumns.filter(col => !row[headerRow.indexOf(col)]);
          const emptyFields = requiredColumns.filter(col => {
            const cellValue = row[headerRow.indexOf(col)];
            return !cellValue || (typeof cellValue === 'string' && cellValue.trim() === '');
          });
          if (missingCols.length > 0 || emptyFields.length > 0) {
            rowsWithEmptyFields.push({ row: index + 2, missingColumns: missingCols, missingFields: emptyFields });
          }
        });

        if (rowsWithEmptyFields.length > 0) {
          const errorText = rowsWithEmptyFields.map(({ row, missingColumns, missingFields }) => {
            return `Row ${row}: Missing fields - ${missingFields.join(', ')}`;
          }).join('\n');
          this.downloadErrorTxt(`missing-fields-error_${this.formattedDate}_${this.formattedTime}.txt`, errorText);
          this.notify.snackBarShowError('Please fix the errors, check the error log from downloads');
          return; // Stop processing if any row has missing required fields
        }

        // If all checks pass, proceed with further processing
        this.createMappingForExport(objectsArray);
    };
    reader.readAsArrayBuffer(file);
  }

  private downloadErrorTxt(fileName: string, content: string): void {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }

  createMappingForExport(items) {
    let obj = {
      'tenantId' : this.user.tenantId,
      'data' : items
    }
    this.api.importData(obj).pipe(first()).subscribe({
      next: (res) => {
        if (res['status']) {
          this.notify.snackBarShowSuccess('Excel Imported Successfully') ;
          this.isImportDone = true ;
        } else {
          this.notify.snackBarShowError('Something Went Wrong!');
        }
        this.cd.detectChanges();
      },
      error: (err) => { console.log(err); }
    })
  }



}
