<!-- Full-screen loading spinner overlay -->
<div *ngIf="isLoading" class="loading-overlay">
  <div class="spinner-container">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="loading-text">Loading account...</div>
  </div>
</div>

<div class="account-setup-container">
  <!-- Compact header with breadcrumbs and actions -->
  <div class="compact-header">
    <div class="breadcrumbs">
      <a [routerLink]="['/dashboard/home']" class="breadcrumb-item">
        <mat-icon class="breadcrumb-icon">home</mat-icon>
      </a>
      <span class="breadcrumb-separator">›</span>
      <a [routerLink]="['/dashboard/account']" class="breadcrumb-item">
        <mat-icon class="breadcrumb-icon">business</mat-icon>
        <span>Accounts</span>
      </a>
      <span class="breadcrumb-separator">›</span>
      <span class="breadcrumb-item active">
        <mat-icon class="breadcrumb-icon">{{isEditMode ? 'edit' : 'add_circle'}}</mat-icon>
        <span>{{isEditMode ? 'Edit Account' : 'New Account'}}</span>
      </span>
    </div>

    <div class="header-actions">
      <button (click)="save()" mat-raised-button color="primary" class="save-button">
        <mat-icon>save</mat-icon>
        {{isEditMode ? 'Update' : 'Create'}}
      </button>
    </div>
  </div>

  <div class="content-section">
    <mat-card class="form-card">
      <mat-card-content>
        <form class="account-form" [formGroup]="registrationForm">
          <h3 class="form-section-title">Account Information</h3>
          <div class="compact-form-grid">
            <!-- First row -->
            <div class="form-row">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Tenant Name</mat-label>
                <input formControlName="tenantName" matInput placeholder="Enter tenant name" />
                <mat-icon matSuffix>business</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Tenant ID</mat-label>
                <input formControlName="tenantId" type="text" matInput placeholder="Enter tenant ID"
                  oninput="this.value = this.value.toUpperCase()" (keyup)="checkTenantId($event)">
                <mat-icon matSuffix>fingerprint</mat-icon>
                <mat-error *ngIf="registrationForm.get('tenantId').hasError('tenantIdExists')">
                  Tenant ID already exists
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Account Number</mat-label>
                <input formControlName="accountNo" type="text" matInput placeholder="Enter account number"
                  oninput="this.value = this.value.toUpperCase()" (keyup)="checkAccountNo($event)">
                <mat-icon matSuffix>account_balance</mat-icon>
                <mat-error *ngIf="registrationForm.get('accountNo').hasError('accountNoExists')">
                  Account number already exists
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Second row -->
            <div class="form-row">
              <mat-form-field appearance="outline" class="form-field">
                <mat-label>G-Sheet</mat-label>
                <input formControlName="gSheet" type="text" matInput placeholder="Enter G-Sheet ID"
                  (keyup)="checkGSheet($event)">
                <mat-icon matSuffix>table_chart</mat-icon>
                <mat-error *ngIf="registrationForm.get('gSheet').hasError('gSheetExists')">
                  G-Sheet number already exists
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Email</mat-label>
                <input formControlName="emailId" matInput placeholder="Enter email address" type="email" />
                <mat-icon matSuffix>email</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="form-field">
                <mat-label>Password</mat-label>
                <input formControlName="password" matInput placeholder="Enter password"
                  [type]="hidePassword ? 'password' : 'text'" />
                <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                  <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                </button>
              </mat-form-field>
            </div>

            <!-- Settings section with two-column layout -->
            <div class="settings-section">
              <div class="two-column-grid">
                <!-- Left column: Status options -->
                <div class="left-column">
                  <div class="status-header">
                    <h4 class="section-label">Account Status</h4>
                  </div>
                  <div class="status-options">
                    <div class="status-option">
                      <label class="status-label">Account</label>
                      <mat-radio-group formControlName="account" aria-labelledby="account-status-label"
                        class="status-radio-group">
                        <mat-radio-button value="yes" color="primary" class="compact-radio">Active</mat-radio-button>
                        <mat-radio-button value="no" color="primary" class="compact-radio">Inactive</mat-radio-button>
                      </mat-radio-group>
                    </div>

                    <div class="status-option">
                      <label class="status-label">Forecast</label>
                      <mat-radio-group formControlName="forecast" aria-labelledby="forecast-status-label"
                        class="status-radio-group">
                        <mat-radio-button value="yes" color="primary" class="compact-radio">Enabled</mat-radio-button>
                        <mat-radio-button value="no" color="primary" class="compact-radio">Disabled</mat-radio-button>
                      </mat-radio-group>
                    </div>

                    <div class="status-option">
                      <label class="status-label">Sales</label>
                      <mat-radio-group formControlName="sales" aria-labelledby="sales-status-label"
                        class="status-radio-group">
                        <mat-radio-button value="yes" color="primary" class="compact-radio">Enabled</mat-radio-button>
                        <mat-radio-button value="no" color="primary" class="compact-radio">Disabled</mat-radio-button>
                      </mat-radio-group>
                    </div>
                  </div>
                </div>

                <!-- Right column: Logo upload -->
                <div class="right-column">
                  <div class="logo-header">
                    <h4 class="section-label">Company Logo</h4>
                  </div>
                  <div class="logo-container">
                    <!-- Interactive logo upload area -->
                    <div class="logo-dropzone" (click)="fileInput.click()" [class.has-logo]="logoUrl">
                      <!-- Logo preview with change instruction overlay -->
                      <div class="logo-preview" *ngIf="logoUrl">
                        <img [src]="logoUrl" alt="Company Logo">
                        <div class="logo-overlay">
                          <div class="overlay-content">
                            <mat-icon>edit</mat-icon>
                            <div class="change-text">Click to change</div>
                          </div>
                        </div>
                      </div>

                      <!-- Placeholder with upload instruction -->
                      <div class="logo-placeholder" *ngIf="!logoUrl">
                        <mat-icon>cloud_upload</mat-icon>
                        <div class="upload-text">Click to upload logo</div>
                      </div>

                      <!-- Loading spinner -->
                      <div class="logo-loading" *ngIf="loadSpinnerForLogo">
                        <div class="spinner-border" role="status">
                          <span class="sr-only">Loading...</span>
                        </div>
                      </div>
                    </div>

                    <!-- Hidden file input -->
                    <input #fileInput type="file" (change)="onFileSelected($event)" accept="image/*"
                      style="display: none;">

                    <!-- Error message -->
                    <mat-error *ngIf="registrationForm.get('logo').invalid && registrationForm.get('logo').touched"
                      class="logo-error">
                      Please upload a logo
                    </mat-error>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- AI Data Download Section - Shows after tenant creation -->
    <mat-card *ngIf="showDataDownload" class="ai-data-section">
      <div class="section-header">
        <mat-icon class="section-icon">auto_awesome</mat-icon>
        <h3 class="section-title">Generate AI-Powered Datasets</h3>
      </div>

      <!-- AI Data Generation Section with Tabs -->
      <div class="chat-bot-section">
        <div class="ai-data-tabs">
          <mat-tab-group [(selectedIndex)]="selectedAITab" (selectedTabChange)="onTabChanged($event)"
            animationDuration="300ms" class="compact-tabs">
            <!-- Chat Agent Tab -->
            <mat-tab>
              <ng-template mat-tab-label>
                <mat-icon class="tab-icon">chat</mat-icon>
                <span class="tab-label">Chat Agent</span>
              </ng-template>

              <div class="tab-content">
                <app-chat-bot [tenantId]="registrationForm.value.tenantId"
                  [tenantName]="registrationForm.value.tenantName">
                </app-chat-bot>
              </div>
            </mat-tab>

            <!-- Dataset Tab -->
            <mat-tab>
              <ng-template mat-tab-label>
                <mat-icon class="tab-icon">dataset</mat-icon>
                <span class="tab-label">Generate Datasets</span>
              </ng-template>

              <div class="tab-content dataset-tab-content"
                *ngIf="showChatBot && !isDownloading && !downloadComplete && !downloadFailed">
                <div class="dataset-info">
                  <p>Our AI will analyze your restaurant information to create optimized inventory, packaging and vendor
                    datasets.</p>
                  <p><strong>Note:</strong> This process takes approximately 30-40 minutes to complete.</p>

                  <button mat-raised-button color="primary" (click)="startAIProcessing()" class="generate-btn">
                    <mat-icon>play_circle</mat-icon>
                    Access Now
                  </button>
                </div>
              </div>


              <!-- Processing state -->
              <div *ngIf="isDownloading" class="ai-processing-panel">
                <h3 class="processing-title">
                  <mat-icon class="processing-icon rotating">autorenew</mat-icon>
                  Processing Your Data
                </h3>

                <!-- Progress indicator -->
                <div class="progress-container">
                  <mat-progress-bar mode="determinate" [value]="downloadProgress" color="accent"></mat-progress-bar>
                  <div class="progress-label">{{downloadProgress}}% Complete</div>
                </div>

                <!-- Estimated time -->
                <div class="estimated-time">
                  <mat-icon class="icon">access_time</mat-icon>
                  <span *ngIf="estimatedTimeRemaining > 60">
                    Estimated time remaining: {{ (estimatedTimeRemaining * 0.0166667) | number:'1.0-0' }} minute
                  </span>
                  <span *ngIf="estimatedTimeRemaining > 0 && estimatedTimeRemaining <= 60">
                    Estimated time remaining: less than a minute
                  </span>
                  <span *ngIf="estimatedTimeRemaining === 0" class="calculating">
                    Calculating...
                  </span>
                </div>

                <!-- Processing steps -->
                <div class="processing-steps">
                  <div *ngFor="let step of downloadSteps; let i = index" class="step-row"
                    [ngClass]="{'completed-step': step.completed, 'active-step': activeStep === i, 'pending-step': !step.completed && activeStep !== i}">

                    <div class="step-status">
                      <mat-icon *ngIf="step.completed">check_circle</mat-icon>
                      <div *ngIf="!step.completed && activeStep === i" class="spinner-border spinner-border-sm"
                        role="status">
                        <span class="visually-hidden">Loading...</span>
                      </div>
                      <mat-icon *ngIf="!step.completed && activeStep !== i">radio_button_unchecked</mat-icon>
                    </div>

                    <div class="step-details">
                      <div class="step-name">{{step.name}}</div>
                      <div class="step-description">{{step.description}}</div>
                    </div>
                  </div>
                </div>

                <!-- Helpful tips section -->
                <div class="tips-section">
                  <div class="tip-header">
                    <mat-icon>lightbulb</mat-icon>
                    <span>Did You Know?</span>
                  </div>
                  <div class="tip-content">
                    AI-driven inventory onboarding can reduce manual effort by up to 70% by leveraging menu-based demand
                    insights
                  </div>
                </div>
              </div>

              <!-- Download complete state -->
              <div *ngIf="downloadComplete" class="ai-complete-panel">
                <div class="success-header">
                  <mat-icon class="success-icon">task_alt</mat-icon>
                  <h3>Processing Complete!</h3>
                </div>

                <p class="success-message">Your AI-powered datasets have been generated successfully and are ready for
                  download.
                </p>

                <!-- Download All Button -->
                <div class="download-all-container mb-3">
                  <mat-card class="download-all-card">
                    <mat-card-header>
                      <div mat-card-avatar class="download-all-icon">
                        <mat-icon>cloud_download</mat-icon>
                      </div>
                      <mat-card-title>Download All Datasets</mat-card-title>
                      <mat-card-subtitle>Get all files in a single ZIP archive</mat-card-subtitle>
                    </mat-card-header>
                    <mat-card-actions>
                      <button mat-raised-button color="primary" (click)="downloadAll()">
                        <mat-icon>archive</mat-icon> Download All Files
                      </button>
                    </mat-card-actions>
                  </mat-card>
                </div>
              </div>

              <!-- Error state -->
              <div *ngIf="downloadFailed" class="ai-error-panel">
                <div class="error-header">
                  <mat-icon class="error-icon">error_outline</mat-icon>
                  <h3>Processing Failed</h3>
                </div>
                <p class="error-message">We encountered an issue while generating your AI datasets. This could be due to
                  server
                  load or connection issues.</p>
                <div class="error-actions">
                  <button mat-raised-button color="warn" (click)="startAIProcessing()">
                    <mat-icon>refresh</mat-icon> Try Again
                  </button>
                </div>
              </div>

            </mat-tab>
          </mat-tab-group>
        </div>
      </div>
    </mat-card>
  </div>
</div>