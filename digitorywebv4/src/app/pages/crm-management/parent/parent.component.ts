import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { BackgroundImageCardComponent } from '../../../components/background-image-card/background-image-card.component';
import { BackgroundImageCardHeaderComponent } from '../../../components/background-image-card-header/background-image-card-header.component';
import { InventoryService } from 'src/app/services/inventory.service';
import { Router } from '@angular/router';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { MatCardModule } from '@angular/material/card';
import { GradientCardComponent } from 'src/app/components/shared/gradient-card/gradient-card.component';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MasterDataService } from 'src/app/services/master-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { Subject, Subscription, first } from 'rxjs';
import { NotificationService } from 'src/app/services/notification.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { HttpTableComponent } from 'src/app/components/http-table/http-table.component';

@Component({
  selector: 'app-parent',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    GradientCardComponent,
    MatTabsModule,
    BackgroundImageCardComponent,
    BackgroundImageCardHeaderComponent,
    HttpTableComponent,
    MatButtonModule,
    MatIconModule,
    NgxSkeletonLoaderModule,
    MatCardModule,
    MatBottomSheetModule
  ],
  templateUrl: './parent.component.html',
  styleUrls: ['./parent.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ParentComponent {
  public routeSubscription: Subscription;
  public selectedTabIndex: number = -1;
  public selectedTabPage: string = '';
  public baseData: any;
  public tabs: { label: string; page: string; index: number; icon: string }[] = [
    { label: 'Accounts', page: 'account', index: 1, icon: "add_to_photos" },
  ];
  locations: any;
  workArea: any;
  locationData: any[];
  selectedTimes: any;
  isChecked: boolean = false;
  isDataReady = false;
  selectedTabClass = 'selected-tab';
  user: any;
  entireData: any;
  @ViewChild('openResetDialog') openResetDialog: TemplateRef<any>;
  dialogRef: MatDialogRef<any>;
  constructor(
    private api: InventoryService,
    private router: Router,
    private sharedData: ShareDataService,
    private cd: ChangeDetectorRef,
    private _bottomSheet: MatBottomSheet,
    private auth: AuthService,
    private masterDataService: MasterDataService,
    private notify: NotificationService,
    public dialog: MatDialog,
  ) {
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit(): void {    
    this.masterDataService.refreshTable$.subscribe(() => {
      this.getBaseData();
    });  
    this.getBaseData();
  }

  getBaseData() {
    this.isDataReady = false;
    this.baseData = [];
    this.cd.detectChanges();
    this.baseData = this.sharedData.getBaseData().value;    
    let obj = {
      tenantId: this.user.tenantId
    };  
    this.masterDataService.route$.pipe(first()).subscribe({
      next: () => {
        this.api.getRoloposConfig(obj).pipe(first()).subscribe({
          next: (res) => {
            this.baseData = res.data;
            this.sharedData.setBaseData(this.baseData);
            this.isDataReady = true;
            this.cd.detectChanges();
          },
        });
      },
      error: (err) => {
        console.log(err);
      }
    });
  }
  
  tabClick(tab: any) {
    this.selectedTabPage = 'account';
  }

}
