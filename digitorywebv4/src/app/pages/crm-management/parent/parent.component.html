

  <mat-card>
    <mat-tab-group [(selectedIndex)]="selectedTabIndex" (selectedTabChange)="tabClick($event)">
      <mat-tab *ngFor="let tab of tabs">
        <ng-template mat-tab-label>
          {{tab.label}}
        </ng-template>
        <div *ngIf="isDataReady">
          <app-http-table [page]="tab.page" [data]="baseData"></app-http-table>
        </div>
        <div *ngIf="!isDataReady" class="my-3">
          <ngx-skeleton-loader count="50" animation="pulse" [theme]="{
            'border-radius': '4px',
            'height': '30px',
            'margin-bottom': '8px',
            'width': '19%',
            'margin-right': '1%',
            'display': 'inline-block',
            'opacity': '0.85'
          }"></ngx-skeleton-loader>
        </div>
      </mat-tab>
    </mat-tab-group>
  </mat-card>

  
    