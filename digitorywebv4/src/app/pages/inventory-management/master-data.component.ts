import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { BackgroundImageCardComponent } from '../../components/background-image-card/background-image-card.component';
import { BackgroundImageCardHeaderComponent } from '../../components/background-image-card-header/background-image-card-header.component';
import { HttpTableComponent } from "../../components/http-table/http-table.component";
import { InventoryService } from 'src/app/services/inventory.service';
import { Router } from '@angular/router';
import { ShareDataService } from 'src/app/services/share-data.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { MatCardModule } from '@angular/material/card';
import { GradientCardComponent } from 'src/app/components/shared/gradient-card/gradient-card.component';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { BottomSheetComponent } from '../bottom-sheet/bottom-sheet.component';
import { MasterDataService } from 'src/app/services/master-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { Subject, Subscription, first } from 'rxjs';
import { NotificationService } from 'src/app/services/notification.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
@Component({
  selector: 'app-master-data',
  standalone: true,
  templateUrl: './master-data.component.html',
  styleUrls: ['./master-data.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    GradientCardComponent,
    MatTabsModule,
    BackgroundImageCardComponent,
    BackgroundImageCardHeaderComponent,
    HttpTableComponent,
    MatButtonModule,
    MatIconModule,
    NgxSkeletonLoaderModule,
    MatIconModule,
    MatCardModule,
    MatBottomSheetModule
  ]
})
export class MasterDataComponent implements OnInit {
  public routeSubscription: Subscription;
  public selectedTabIndex: number = -1;
  public selectedTabPage: string = '';
  public baseData: any;
  public tabs: { label: string; page: string; index: number; icon: string }[] = [
    { label: 'Inventory', page: 'inventory master', index: 0, icon: "inventory" },
    { label: 'Vendor', page: 'vendors', index: 1, icon: "store" },
  ];
  locations: any;
  workArea: any;
  locationData: any[];
  selectedTimes: any;
  isChecked: boolean = false;
  isDataReady = false;
  selectedTabClass = 'selected-tab';
  user: any;
  entireData: any;
  @ViewChild('openResetDialog') openResetDialog: TemplateRef<any>;
  dialogRef: MatDialogRef<any>;
  constructor(
    private api: InventoryService,
    private router: Router,
    private sharedData: ShareDataService,
    private cd: ChangeDetectorRef,
    private _bottomSheet: MatBottomSheet,
    private auth: AuthService,
    private masterDataService: MasterDataService,
    private notify: NotificationService,
    public dialog: MatDialog,
  ) {
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit(): void {
    this.masterDataService.isChecked$.subscribe(isChecked => {
      this.isChecked = isChecked;
      if (this.isChecked) {
        let obj = {}
        for (const key in this.baseData) {
          (key != '_id' && key != 'tenantId' && key != 'createTs' && key != 'modTs') ? (obj[key] = this.baseData[key].filter((item) => (item.changed && item.changed == true))) : obj[key] = this.baseData[key];
        }
        this.baseData = obj;
      } else {
        this.user.tenantId === '100000' ? this.router.navigate(['/dashboard/account/']):  this.getBaseData();
      }
    });
    this.getCategories();
  }

  getBaseData() {
    this.baseData = this.sharedData.getBaseData().value;
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['userEmail'] = this.user.email
    obj['type'] = 'inventory'
    this.masterDataService.route$.pipe(first()).subscribe(tab => {
      if (tab && tab === 'inventoryList') {
        if (('inventory master' in this.sharedData.getBaseData().value) && ('packagingmasters' in this.sharedData.getBaseData().value)){
          obj['specific'] = 'inventory master'
        }
        this.selectedTabIndex = 0;
      } else if (tab && tab === 'vendorList') {
        if ('vendors' in this.sharedData.getBaseData().value) {
          obj['specific'] = 'vendors'
        }
        this.selectedTabIndex = 1;
      } else {
        this.selectedTabIndex = 0;
      }

      this.api.getPresentData(obj).pipe(first()).subscribe({
        next: (res) => {
          if (res['success'] && (res['data'].length > 0 || Object.keys(res['data']).length > 0)) {
            this.entireData = res
            if (obj['specific'] == 'inventory master') {
              let previousBaseData = this.sharedData.getBaseData().value['inventory master'];
              let currentBaseData = res['data'][0] ?? res['data']['inventory master'];
              currentBaseData.forEach(item => {
                const exist = previousBaseData.findIndex(el => el.itemCode == item['itemCode']);
                if (exist !== -1) {
                  previousBaseData[exist] = item;
                } else {
                  previousBaseData.push(item);
                }
              });
              this.baseData['inventory master'] = previousBaseData;
              this.baseData['packagingmasters'] = res['data'][0] ?? res['data']['packagingmasters'];
            } else if (obj['specific'] == 'vendors') {
              this.baseData['vendors'] = res['data'][0] ?? res['data']['vendors'];
            } else {
              this.baseData = res['data'][0] ?? res['data'];
            }
            this.sharedData.setBaseData(this.baseData)
            this.isDataReady = true;
            this.cd.detectChanges();
            this.filterDatas();
          }
        },
        error: (err) => { console.log(err) }
      })
    });
  }

  getLocationCall() {
    this.api.getLocations(this.user.tenantId).pipe(first()).subscribe({
      next: (res) => {
        if (res['result'] == 'success') {
          this.locationData = res['branches'][0]
          this.filterDatas();
        } else {
          res = []
        }
      },
      error: (err) => { console.log(err); }
    });
  }

  filterDatas() {
    if (this.baseData) {
      const itemNames = Array.from(
        new Set(
          this.baseData['inventory master']
            .filter(item => !item.Discontinued || item.Discontinued !== 'yes')
            .map(item => ({
              itemCode: item.itemCode,
              itemName: item.itemName
            }))
        )
      );
      const vendorObject = Array.from(
        new Set(
          this.baseData['vendors']
            .filter(item => !item.Discontinued || item.Discontinued !== 'yes')
            .map(item => ({
              vendorId: item.vendorTenantId,
              vendorName: item.vendorName
            }))
        )
      );
      const vendor = Array.from(
        new Set(
          this.baseData['vendors']
            .filter(item => !item.Discontinued || item.Discontinued !== 'yes')
            .map(item => item.vendorName)
        )
      );

      const packageNames = Array.from(
        new Set(
          this.baseData['packagingmasters']
            .filter(item => !item.Discontinued || item.Discontinued !== 'yes')
            .map(item => item.PackageName)
        )
      );

      const updatedInvData = [...this.baseData['inventory master']];
      let obj = {
        itemNames: itemNames,
        vendor: vendor,
        vendorObject: vendorObject,
        updatedInvData: updatedInvData,
        packageNames: packageNames
      }
      this.sharedData.setItemNames(obj, this.baseData)
      this.sharedData.setItemForRecipe(this.baseData)
      this.sharedData.setItemNamesForRecipe(obj)
    }
  }

  tabClick(tab: any) {
    this.selectedTabIndex = tab.index;
    this.selectedTabPage = this.tabs[tab.index].page;
    this.router.navigate(['/dashboard/inventory/'], { queryParams: { tab: this.selectedTabPage } });

    // Force change detection to ensure tabs render properly
    setTimeout(() => {
      this.cd.detectChanges();
    }, 0);
  }

  openBottomSheet(): void {
    this._bottomSheet.open(BottomSheetComponent);
  }

  resetData(){
    this.dialogRef = this.dialog.open(this.openResetDialog, {
      width: '500px',
    });

    this.dialogRef.afterClosed().subscribe(result => {
    });
  }

  resetUI(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['type'] = 'inventory'
    obj['sessionId'] = this.entireData.sessionId
    this.api.resetSession(obj).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.notify.snackBarShowSuccess('The session was successfully reset.');
          this.closeResetDialog();
          this.baseData = {}
          this.masterDataService.setNavigation('');
          this.router.navigate(['/dashboard/home']);
          setTimeout(() => {
            this.router.navigate(['/dashboard/inventory']);
          }, 1000);
        }
      },
      error: (err) => { console.log(err) }
    })
  }

  closeResetDialog(){
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  getCategories() {
    this.sharedData.getInvCategories.pipe(first()).subscribe((obj) => {
      if (Object.entries(obj).length === 0) {
        this.api.getCategories({ tenantId: this.user.tenantId, type: 'inventory' }).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.sharedData.sendInvCategories(res['categories'])
            }
          },
          error: (err) => { console.log(err); }
        });
      }
    })
  }

}