<div class="closeBtn" *ngIf="isDuplicate == true">
  <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
</div>
<div class="registration-form py-2 px-3">
  <div *ngIf="this.costDialogkey == true" class="spinner-border" role="status">
    <span class="sr-only">Loading...</span>
  </div>
  <div class="m-1" *ngIf="isDuplicate === null">
    <mat-form-field appearance="outline">
      <mat-label>Search</mat-label>
      <input matInput placeholder="Search" (keyup)="filterDialog($event)" aria-label="Search">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <div *ngIf="isDuplicate == true" class="mt-3 smallDialog">
    <div class="col-md-12">
      <div class="text-center my-2 p-2 bottomTitles">
        <span>SubRecipe Master Form</span>
      </div>
      <mat-form-field appearance="outline">
        <mat-label>Search SubRecipe ..</mat-label>
        <input matInput placeholder="SubRecipe.." aria-label="Inventory" [matAutocomplete]="auto1"
          (keyup)="checkItem($event)" [formControl]="itemNameControl" (keyup.enter)="addOption('Subrecipe Master')"
          oninput="this.value = this.value.toUpperCase()">
        <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected('Subrecipe Master', $event.option)">
          <mat-option *ngFor="let item of itemNameOptions | async" [value]="item">
            <span>{{ item | uppercase }}</span>
          </mat-option>
        </mat-autocomplete>
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      <div class="text-end">
        <button (click)="addOption('Subrecipe Master')" mat-raised-button color="accent"
          [disabled]="!itemNameControl.value">
          <div *ngIf="loadBtn" class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <mat-icon *ngIf="!updateBtnActive">library_add</mat-icon>
          <mat-icon *ngIf="updateBtnActive">update</mat-icon>
          <span *ngIf="updateBtnActive">Update</span>
          <span *ngIf="!updateBtnActive">Add</span>
        </button>
      </div>
    </div>
  </div>
  <div class="mb-2 topCreateAndUpdateBtn" style="float: right; padding-top: 1rem;">
    <button *ngIf="isUpdateActive && (isDuplicate == false && !showSRR)" mat-raised-button class="mappingBtn discButton" style="margin-right: 5px;" matTooltip="print" (click)="printOption()" type="button">
      <mat-icon>print</mat-icon>
      Print</button>
    <button *ngIf="isUpdateActive && (isDuplicate == false && !showSRR)" style="margin-right: 5px;" (click)="update()" mat-raised-button
      color="accent" matTooltip="update" [disabled]="loadSrmBtn || loadSpinnerForApi">
      <div *ngIf="loadSpinnerForApi" class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      Update
    </button>

    <button *ngIf="!isUpdateActive && (isDuplicate == false && !showSRR)" (click)="submit(); isButtonDisabled = true" style="margin-right: 5px;" mat-raised-button color="accent" matTooltip="Create"
    [disabled]="dataSource.data.length === 0 || this.registrationForm.invalid || loadSpinnerForApi">
      <div *ngIf="loadSpinnerForApi" class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <mat-icon>add_circle</mat-icon> Create
    </button>

    <button *ngIf="isDuplicate == false && !showSRR" mat-raised-button color="warn" (click)="close()" matTooltip="Close" style="margin-right: 7px;">
      <mat-icon>close</mat-icon>
      Close
    </button>
  </div>
  <div class="pb-2">
    <div class=" my-2 p-3 bottomTitles" *ngIf="isDuplicate == false">
      Sub-Recipe Master
    </div>
  </div>

  <div *ngIf="isDuplicate == false && !showSRR">
    <div class="d-flex justify-content-center">
        <form [formGroup]="registrationForm">
          <div class="row">
            <!-- Editable Fields -->
            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Menu Item code</mat-label>
                <input formControlName="menuItemCode" matInput placeholder="Menu Item Code" autocomplete="off"
                  [readonly]="isReadOnly" [ngClass]="{'readonly-field': isReadOnly}">
                <mat-icon matSuffix>receipt</mat-icon>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Menu Item Name</mat-label>
                <input formControlName="menuItemName" matInput placeholder="Menu Item Name" [readonly]="isReadOnly"
                  [ngClass]="{'readonly-field': isReadOnly}">
                <mat-icon matSuffix>restaurant_menu</mat-icon>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>UOM</mat-label>
                <mat-select formControlName="uom">
                  <mat-option *ngFor="let uom of ['GM','ML','NOS']" [value]="uom">
                    {{uom}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Closing UOM</mat-label>
                <mat-select formControlName="closingUOM">
                  <mat-option *ngFor="let uom of ['KG', 'LITRE', 'NOS']" [value]="uom">
                    {{uom}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Category</mat-label>
                <input matInput placeholder="Category Name" aria-label="Category" [matAutocomplete]="auto1"
                  formControlName="category" (keyup.enter)="addOptionCat()" oninput="this.value = this.value.toUpperCase()">
                <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelectedCat($event.option)">
                  <mat-option *ngFor="let cat of catBank | async" [value]="cat">
                    <span>{{ cat }}</span>
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Sub Category</mat-label>
                <input matInput placeholder="sub Category" aria-label="SubCategory" [matAutocomplete]="auto2"
                  formControlName="subCategory" (keyup.enter)="addOptionSubCat()"
                  oninput="this.value = this.value.toUpperCase()">
                <mat-autocomplete #auto2="matAutocomplete" (optionSelected)="optionSelectedSubCat($event.option)">
                  <mat-option *ngFor="let sub of subCatBank | async" [value]="sub">
                    <span>{{ sub }}</span>
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Prepared At</mat-label>
                <mat-select formControlName="preparedAt" multiple>
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="preparedFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option class="hide-checkbox" (click)="preparedToggleSelectAll()">
                    <mat-icon matSuffix>check_circle</mat-icon>
                    Select All / Deselect All
                  </mat-option>
                  <!-- <mat-option *ngFor="let loc of preparedLocationNames | async" [value]="loc">
                    {{loc}}
                  </mat-option> -->
                  <mat-option *ngFor="let loc of preparedLocationNames | async" [value]="loc" [disabled]="discontinuedPreLocData.includes(loc)"
                    [ngClass]="{'disabled-option': this.defaultPreLocData.includes(loc) || discontinuedPreLocData.includes(loc)}">
                    <span [ngClass]="{'disabledSelect': discontinuedPreLocData.includes(loc)}">{{ loc | uppercase }}</span>
                    <mat-icon *ngIf="this.defaultPreLocData.includes(loc) && !this.discontinuedPreLocData.includes(loc)"
                      class="deleteIconForMatSelect" matTooltip="discontinue"
                      (click)="onDelete(loc, $event, 'preparatoryLocation' , 'null')"
                      [ngClass]="{'clickable': discontinuedPreLocData.includes(loc)}">
                      delete
                    </mat-icon>
                    <mat-icon *ngIf="this.discontinuedPreLocData.includes(loc)"
                      class="deleteIconForMatSelect" matTooltip="restore"
                      (click)="onRestore(loc, $event, 'preparatoryLocation','null')">
                      settings_backup_restore</mat-icon>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Sales Outlet</mat-label>
                <mat-select formControlName="usedAtOutlet" (selectionChange)="locationChange($event.value)" multiple>
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="outletFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option class="hide-checkbox" (click)="usedOutLetToggleSelectAll()">
                    <mat-icon matSuffix>check_circle</mat-icon>
                    Select All / Deselect All
                  </mat-option>
                  <!-- <mat-option *ngFor="let loc of outletLocationNames | async" [value]="loc">
                    {{loc}}
                  </mat-option> -->
                  <mat-option *ngFor="let loc of outletLocationNames | async" [value]="loc" [disabled]="discontinuedOutletData.includes(loc)"
                    [ngClass]="{'disabled-option': this.defaultOutletData.includes(loc) || discontinuedOutletData.includes(loc)}">
                    <span [ngClass]="{'disabledSelect': discontinuedOutletData.includes(loc)}">{{ loc | uppercase }}</span>
                    <mat-icon *ngIf="this.defaultOutletData.includes(loc) && !this.discontinuedOutletData.includes(loc)"
                      class="deleteIconForMatSelect" matTooltip="discontinue"
                      (click)="onDelete(loc, $event, 'usedOutlet', 'null')"
                      [ngClass]="{'clickable': discontinuedOutletData.includes(loc)}">
                      delete
                    </mat-icon>
                    <mat-icon *ngIf="this.discontinuedOutletData.includes(loc)"
                      class="deleteIconForMatSelect" matTooltip="restore"
                      (click)="onRestore(loc, $event, 'usedOutlet','null')">
                      settings_backup_restore</mat-icon>
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Issued To</mat-label>
                <mat-select formControlName="usedInWorkArea" multiple>
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                      [formControl]="usedInWorkAreaFilterCtrl"></ngx-mat-select-search>
                  </mat-option>
                  <mat-optgroup *ngFor="let group of usedInWorkAreaNames | async"
                    [label]="group.restaurantIdOld.split('@')[1]" [disabled]="group.disabled">
                    <!-- <mat-option *ngFor="let data of group.workAreas" [value]="data">
                      {{data}}
                    </mat-option> -->
                    <mat-option *ngFor="let data of group.workAreas" [value]="data" [disabled]="isOptionDisabled(data , group)"
                    [ngClass]="{'disabled-option': isCheckOptionDisabled(data , group) || this.defaultIssuedToData.includes(data)}">
                    <span [ngClass]="{'disabledSelect': isOptionDisabled(data , group) || group.disabled}">{{ data | uppercase }}</span>
                    <mat-icon *ngIf="!discontinuedOutletData.includes(group.abbreviatedRestaurantId) && this.defaultIssuedToData.includes(data) && !isOptionDisabled(data , group)"
                        class="deleteIconForMatSelect" matTooltip="discontinue"
                        (click)="onDelete(data, $event, 'issuedTo' , group)"
                        [ngClass]="{'clickable': discontinuedIssuedToData.includes(data)}">
                        delete
                      </mat-icon>
                      <mat-icon *ngIf="isOptionDisabled(data , group) && !group.disabled"
                        class="deleteIconForMatSelect" matTooltip="restore" (click)="onRestore(data, $event, 'issuedTo',group)">
                        settings_backup_restore
                      </mat-icon>
                  </mat-option>

                  </mat-optgroup>
                </mat-select>
              </mat-form-field>
              <mat-error class="formError" *ngIf="showWorkAreaError">
                * select at least one workarea in every branch
              </mat-error>
            </div>

            <!-- Non-Editable Fields with Better Design -->
            <div class="col-md-3">
              <div class="non-editable-field">
                <label>Initial Weight</label>
                <span>{{ registrationForm.get('weightInUse').value }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <div class="non-editable-field">
                <label>Final Weight</label>
                <span>{{ registrationForm.get('recovery').value }}</span>
              </div>
            </div>

            <div class="col-md-3">
              <div class="non-editable-field">
                  <label>Preparation Cost </label>
                  <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span (click)="openUnitDialog()"> {{ this.notify.truncateAndFloor(registrationForm.get('rate').value,2) }}</span>
                  </div>
              </div>
            </div>

            <div class="col-md-3">
              <div class="non-editable-field">
                  <label>Usage Cost per UOM </label>
                  <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span > {{ this.notify.truncateAndFloor(((this.registrationForm.value.rate ) / this.registrationForm.value.recovery) * 1000,2)}}</span>
                  </div>
              </div>
            </div>

            <div class="col-md-3">
              <mat-form-field appearance="outline">
                <mat-label>Final Yield</mat-label>
                <input
                  formControlName="yield"
                  type="number"
                  matInput
                  placeholder="Yield"
                  (keyup)="setZeroSRM($event, 'yield')"
                  autocomplete="off"
                  (focus)="focusFunction('yield')"
                  (focusout)="focusOutFunction('yield')"
                >
                <mat-error *ngIf="registrationForm.get('yield').errors?.['yieldInvalid']">
                  Yield should be more than 0
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Batch Section -->
            <div class="col-md-3">
              <div class="d-flex align-items-center">
                <mat-form-field appearance="outline" class="flex-fill me-2">
                  <mat-label>Batch</mat-label>
                  <mat-select placeholder="Select Unit" formControlName="unit" (selectionChange)="onUnitChange($event.value)">
                    <mat-option *ngFor="let unit of units" [value]="unit">{{unit}}</mat-option>
                  </mat-select>
                </mat-form-field>
                <mat-form-field appearance="outline" class="flex-fill me-2">
                  <input formControlName="portion" type="number" matInput placeholder="Batch" (keyup)="setPortionData()"
                    autocomplete="off" (focus)="focusFunction('portion')" (focusout)="focusOutFunction('portion')">
                  <mat-error *ngIf="registrationForm.get('portion').errors?.['portionInvalid']">
                    Portion should be more than 0
                  </mat-error>
                  <mat-icon matSuffix (click)="openPortionData()" class="custom-outline-button"
                    matTooltip="Portion Details">info</mat-icon>
                </mat-form-field>
              </div>
            </div>

          <div class="col-md-3">
            <mat-form-field appearance="outline">
              <mat-label>Preparatory Location</mat-label>
              <mat-select
                [formControl]="rest"
                (selectionChange)="setRestaurant($event.value)">
                
                <!-- Search bar -->
                <mat-option>
                  <ngx-mat-select-search
                    [formControl]="restaurantFilterCtrl"
                    placeholderLabel="Search..."
                    noEntriesFoundLabel="'Not found'">
                  </ngx-mat-select-search>
                </mat-option>
          
                <!-- Filtered options -->
                <mat-option
                  *ngFor="let rest of filteredRestaurantOptions"
                  [value]="rest">
                  {{ rest.branchName }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>                    

            <!-- Additional Fields -->
            <div *ngIf="isUpdateActive">
              <div class="col-md-12">
                <label>Do you want to discontinue?</label>
                <mat-radio-group formControlName="discontinued">
                  <mat-radio-button value="yes">Yes</mat-radio-button>
                  <mat-radio-button value="no">No</mat-radio-button>
                </mat-radio-group>
              </div>
            </div>
          </div>
        </form>
    </div>

    <div>
      <div class="my-2 p-2 bottomTitles">
        Sub-Recipe Recipe
      </div>
      <!-- <br> -->
      <div class="searchInputParentClass my-2">
      <div>
        <div cdkTrapFocus>
          <form [formGroup]="subRecipeRecipeForm">
            <div class="d-flex gap-3">
              <div class="form-group customHeightfield">
                <label for="ingredientSelect">Ingredient Name</label>
                <input matInput placeholder="ingredient Name" [matAutocomplete]="autoIngredients" class="form-control"
                    formControlName="ingredientName" oninput="this.value = this.value.toUpperCase()">
                  <mat-autocomplete #autoIngredients="matAutocomplete"
                  (optionSelected)="selectIngredientsName(subRecipeRecipeForm.value.ingredientName)">
                    <mat-option *ngFor="let name of ingredientNamesOptions | async" [value]="name" [ngClass]="{'text-warning': subData.includes(name)}" [disabled]="(updateSRR && subRecipeRecipeForm.value.ingredientName) || name === 'No Item Found' ">
                      <span>{{ name | uppercase }}</span>
                    </mat-option>
                  </mat-autocomplete>
              </div>

              <div class="form-group customHeightfield">
                <label for="modifierSelect">UOM</label>
                <select class="form-select" id="modifierSelect" formControlName="uom" style="width: 80px !important;"
                  (change)="uomChange()">
                  <option *ngFor="let data of ingredientUOM" [value]="data" [disabled]="!isOptionAccessible(data)">
                    {{ data }}
                  </option>
                </select>
              </div>

              <div class="form-group customHeightfield" *ngIf="this.subRecipeRecipeForm.value.uom  === 'PORTION'">
                <label for="portionSelect">Portion</label>
                <input formControlName="portionCount" type="number" class = "highlighted-input form-control" (focus)="focusFunctionSRR('portionCount')"
                (focusout)="focusOutFunctionSRR('portionCount')" (keyup)=" convertPortionToUOM()" placeholder="Rate" autocomplete="off">
              </div>

              <div class="form-group customHeightfield">
                <label for="unitCostSelect">WAC(incl.tax,etc)/UOM</label>
                <input formControlName="rate" type="number" class = "highlighted-input form-control" (focus)="focusFunctionSRR('rate')"
                (focusout)="focusOutFunctionSRR('rate')" (keyup)=" sumForFinalRateSRR($event)" placeholder="Rate"
                autocomplete="off" (focus)="focusFunction('rate')" (focusout)="focusOutFunction('rate')" readonly>
              </div>

              <div class="form-group customHeightfield">
                <label for="portionCountInput">Weight in Use</label>
                <input formControlName="weightInUse" type="number" (focus)="focusFunctionSRR('weightInUse')"
                (focusout)="focusOutFunctionSRR('weightInUse')" (keyup)="sumForFinalRateSRR($event)"
                placeholder="Weight In Use" autocomplete="off" class="form-control" [readonly]="this.subRecipeRecipeForm.value.uom === 'PORTION'">
                <div class="formError" *ngIf="this.showWeightError">
                  * weight should be greater than 0
                </div>
              </div>

              <div class="form-group customHeightfield">
                <label for="yieldInput">Yield</label>
                <input formControlName="yield" type="number" (keyup)="setZeroSRR($event , 'yield')"
                placeholder="Yield" autocomplete="off" (focus)="focusFunctionSRR('yield')"
                (focusout)="focusOutFunctionSRR('yield')" class="form-control">
                <div *ngIf="subRecipeRecipeForm.get('yield').errors?.['yieldInvalid']" class="formError">
                  Yield should be more than 0
                </div>
              </div>

              <div class="form-group flex-shrink-0 d-flex align-items-end justify-content-end"
                style="margin-bottom: 0.1px;">
                <button type="submit" style="height: 2.3rem;" class="btn btn-secondary btn-sm px-3"
                  (click)="addNewSubRecipeRecipe()" matTooltip="Add">
                  <i class="material-icons align-middle">add</i> Add
                </button>

              </div>
            </div>
          </form>
        </div>

      </div>
    </div>

      <div>
        <mat-slide-toggle [(ngModel)]="showDeleteItems" class="mb-2 floatRightBtn"
        (change)="showItems()">Show Discontinued</mat-slide-toggle>
      </div>
      <div class="section" #section #widgetsContent>
        <div class="tableDiv" *ngIf="isSRRDataReady">
            <mat-table [dataSource]="dataSource" matSort>
              <ng-container matColumnDef="sNo">
                <mat-header-cell *matHeaderCellDef class="tableSnoCol"> S.No. </mat-header-cell>
                <mat-cell *matCellDef="let element; let i = index;"
                  class="tableSnoCol">{{i+1}}</mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef class="tableSnoCol">
                </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="action">
                <mat-header-cell *matHeaderCellDef class="tableActionCol"> Action </mat-header-cell>
                <mat-cell class="tableActionCol" *matCellDef="let element">
                  <button (click)="editFun(element,addSRR)" backgroundColor="primary" class="mx-2 editIconBtn"
                    matTooltip="Edit"><mat-icon class="mt-1">edit</mat-icon></button>
                  <button (click)="deleteSRR(element)" backgroundColor="primary" class="mx-2 editIconBtn"
                    matTooltip="Delete"><mat-icon class="mt-1">delete</mat-icon></button>
                </mat-cell>
                <mat-footer-cell class="tableActionCol" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="discontinued">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Status </mat-header-cell>
                <mat-cell class="custom-cell justify-content-start" *matCellDef="let element">
                  <div *ngIf="element.Discontinued == 'yes'" class="d-flex align-items-center">
                    <mat-icon class="cancelIcon">cancel</mat-icon> &nbsp; Discontinued
                  </div>
                  <div *ngIf="element.Discontinued == 'no'" class="d-flex align-items-center">
                    <mat-icon class="checkIcon">check_circle</mat-icon> &nbsp; Active
                  </div>
                  <div *ngIf="element.Discontinued != 'no' && element.Discontinued != 'yes'"
                    class="d-flex align-items-center">
                    <mat-icon class="checkIcon">check_circle</mat-icon> &nbsp; Active
                  </div>
                </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="modified">
                <mat-header-cell *matHeaderCellDef class="tableModCol"> Modified </mat-header-cell>
                <mat-cell *matCellDef="let element" class="tableModCol">
                  <div *ngIf="element.modified == 'yes'">
                    <mat-chip color="primary">NOT SYNCED</mat-chip>
                  </div>
                  <div *ngIf="element.modified == 'no' || element.modified == '-'">
                    -
                  </div>
                </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef class="tableModCol"> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="ingredientName">
                <mat-header-cell class="custom-header" *matHeaderCellDef style="min-width: 300px !important;"> Ingredient Name </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element" style="min-width: 300px !important;">
                  <div matTooltip="Edit" class="link mr-2" (click)="editFun(element,addSRR)"
                  [ngClass]="{'text-warning': subData.includes(element.ingredientName)}" style="width: 200px !important;">
                    {{element.ingredientName}}
                  </div>
                  <mat-icon *ngIf="element.Discontinued == 'no'" class="checkIcon tableIcons">check_circle</mat-icon>
                  <mat-icon *ngIf="element.Discontinued != 'no' && element.Discontinued != 'yes'" class="checkIcon tableIcons">check_circle</mat-icon>
                  <button (click)="deleteSRR(element)" backgroundColor="primary" class="mx-2 editIconBtn"
                    matTooltip="Delete"><mat-icon class="mt-1">delete</mat-icon></button>
                </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef style="min-width: 300px !important;"> Total </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="ingredientCode">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Ingredient Code </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.ingredientCode}} </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="subRecipeName">
                <mat-header-cell class="custom-header" *matHeaderCellDef> SubRecipe Name </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.subRecipeName}} </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="subRecipeCode">
                <mat-header-cell class="custom-header" *matHeaderCellDef> SubRecipe Code </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.subRecipeCode}} </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="uom">
                <mat-header-cell style="min-width: 125px !important;" *matHeaderCellDef> UOM </mat-header-cell>
                <mat-cell style="min-width: 125px !important;" *matCellDef="let element"> {{element.UOM}} </mat-cell>
                <mat-footer-cell style="min-width: 125px !important;" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="initialWeight">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Initial Weight </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.Initialweight || 0 }} </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> {{this.getTotal('Initialweight')}}
                </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="yield">
                <mat-header-cell style="min-width: 100px !important;" *matHeaderCellDef> Yield </mat-header-cell>
                <mat-cell style="min-width: 100px !important;" *matCellDef="let element"> {{element.yield || 0 }} </mat-cell>
                <mat-footer-cell style="min-width: 100px !important;" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="loss">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Loss </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{element.loss || 0 }} </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="weightInUse">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Weight in Use </mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{ this.notify.truncateAndFloor(element.weightInUse || 0) }} </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> {{ getTotal('weightInUse') }} </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="rate">
                <mat-header-cell style="min-width: 125px !important;"  *matHeaderCellDef> WAC(incl.tax,etc)</mat-header-cell>
                <mat-cell style="min-width: 125px !important;"  *matCellDef="let element"> {{this.notify.truncateAndFloor(element.rate || 0) }} </mat-cell>
                <mat-footer-cell style="min-width: 125px !important;"  *matFooterCellDef> </mat-footer-cell>
              </ng-container>

              <ng-container matColumnDef="finalRate">
                <mat-header-cell class="custom-header" *matHeaderCellDef> Final Rate</mat-header-cell>
                <mat-cell class="custom-cell" *matCellDef="let element"> {{this.notify.truncateAndFloor(element.finalRate,2 )|| 0 }} </mat-cell>
                <mat-footer-cell class="custom-footer" *matFooterCellDef> {{this.notify.truncateAndFloor(this.getSRRTotal('finalRate'),2)}}
                </mat-footer-cell>
              </ng-container>

              <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
              <mat-row *matRowDef="let row; columns: displayedColumns;"
                [ngClass]="{'highlighted-row': row.Discontinued === 'yes'}"></mat-row>
              <mat-footer-row *matFooterRowDef="displayedColumns"></mat-footer-row>
            </mat-table>
            <mat-paginator class="mat-paginator-sticky" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 50, 100]"></mat-paginator>
        </div>
        <div *ngIf="!isSRRDataReady">
          <ngx-skeleton-loader count="50" animation="pulse" [theme]="{
            'border-radius': '4px',
            'height': '30px',
            'margin-bottom': '8px',
            'width': '19%',
            'margin-right': '1%',
            'display': 'inline-block',
            'opacity': '0.85'
          }"></ngx-skeleton-loader>
        </div>
        <div *ngIf="dataSource.data.length == 0 && isSRRDataReady">
          <app-empty-state
            *ngIf="this.showDeleteItems == false"
            icon="soup_kitchen"
            title="No Subrecipe Recipes Found"
            message="Time to get creative! Add ingredients to create your subrecipe."
          ></app-empty-state>

          <app-empty-state
            *ngIf="this.showDeleteItems == true"
            icon="check_circle"
            title="No Discontinued Items"
            message="There are no discontinued ingredients to display."
          ></app-empty-state>
        </div>
      </div>
    </div>
  </div>

  <!-- ----------------------------------   SUBRECIPE RECIPE FORM    ---------------------------------- -->
  <ng-template #addSRR>
    <div class="closeBtn">
      <mat-icon (click)="closeSRRDialog()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
    </div>
    <div class="registration-form py-2 px-3">
      <div class="text-center my-2 p-2 bottomTitles">
        <span>SubRecipe Recipe Form</span>
      </div>
      <div class="d-flex justify-content-end flex-wrap mb-3">
        <button *ngIf="updateSRR" (click)="editExistingSubRecipeRecipe()" mat-raised-button color="accent"
          matTooltip="update">
          <div *ngIf="loadSpinnerForApiSRR" class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          Update
        </button>
      </div>

      <form [formGroup]="subRecipeRecipeForm">
        <div class="row">
          <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>SubRecipe Name</mat-label>
              <input formControlName="subRecipeName" matInput placeholder="SubRecipe Name" [readonly]="isReadOnly">
              <mat-icon matSuffix>restaurant_menu</mat-icon>
            </mat-form-field>
          </div>

          <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Ingredient Name</mat-label>
              <mat-select placeholder="Ingredient Name" formControlName="ingredientName"
                (selectionChange)="selectIngredientsName($event.value)" >
                <mat-option [disabled]="updateSRR">
                  <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                    [formControl]="IngredientFilterCtrl"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let name of ingredientNames | async" [value]="name" [disabled]="updateSRR">
                  {{ name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}" >
              <mat-label>UOM</mat-label>
              <mat-select formControlName="uom">
                <mat-option *ngFor="let data of ingredientUOM" [value]="data" [disabled]="updateSRR">
                  {{data}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div  *ngIf="this.subRecipeRecipeForm.value.uom  === 'PORTION'">
            <mat-form-field appearance="outline">
              <mat-label>Portion</mat-label>
              <input matInput type="number" placeholder="Portion" class="outline" formControlName="portionCount"
                (keyup)="convertPortionToUOM()" (focus)="focusFunctionSRR('portionCount')"
                (focusout)="focusOutFunction('portionCount')" />
            </mat-form-field>
          </div>

          <div >
            <mat-form-field appearance="outline">
              <mat-label>Weight In Use</mat-label>
              <input formControlName="weightInUse" type="number" matInput (focus)="focusFunctionSRR('weightInUse')"
                (focusout)="focusOutFunctionSRR('weightInUse')" (keyup)="sumForFinalRateSRR($event)"
                placeholder="Weight In Use" autocomplete="off" [readonly]="this.subRecipeRecipeForm.value.uom === 'PORTION'" >
            </mat-form-field>
          </div>

          <div>
            <mat-form-field appearance="outline" >
              <mat-label>Yield</mat-label>
              <input formControlName="yield" type="number" matInput (keyup)="setZeroSRR($event , 'yield')"
                placeholder="yield" autocomplete="off" (focus)="focusFunctionSRR('yield')"
                (focusout)="focusOutFunctionSRR('yield')" >
            </mat-form-field>
          </div>

          <div *ngIf="updateSRR">
            <div class="col">
              <label>Do you want to discontinue?</label>
              <mat-radio-group formControlName="discontinued" aria-labelledby="example-radio-group-label">
                <mat-radio-button value="yes">Yes</mat-radio-button>
                <mat-radio-button value="no">No</mat-radio-button>
              </mat-radio-group>
            </div>
          </div>
        </div>
      </form>
    </div>
  </ng-template>

  <div *ngIf="isDuplicate === null" class="mt-3 smallDialog dropDndDialog">
    <div *ngFor="let data of filteredData;let i = index" class="my-2">
      {{i + 1}}. {{data}}
    </div>
    <div *ngIf="filteredData.length == 0">
      <app-empty-state
        icon="search_off"
        title="No Results Found"
        message="No matching data found for your search criteria."
        customClass="dialog-empty-state"
      ></app-empty-state>
    </div>
  </div>

  <ng-template #openPortionDialog>
    <div class="dialog-container">
      <div class="close-btn">
        <button mat-icon-button class="close-btn-icon" aria-label="Close" matTooltip="close" (click)="closeInfoDialog()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="mx-1 py-2 px-3">
        <div class="text-center my-2 p-2 bottom-titles">
          <span>Detailed Info</span>
        </div>
        <div class="portion-info">
          <table class="info-table">
            <tbody>
              <tr>
                <td class="info-key"><strong>Gross Weight</strong></td>
                <td class="info-value">{{ this.notify.truncateAndFloor(this.registrationForm.value.weightInUse)  }}</td>
                <td class="info-unit">GM/ML</td>
              </tr>
              <tr>
                <td class="info-key"><strong>Portion Weight</strong></td>
                <td class="info-value">{{ this.notify.truncateAndFloor(getWeightPerPortion())  }}</td>
                <td class="info-unit">GM/ML</td>
              </tr>
              <br>
              <tr>
                <td class="info-key"><strong>Gross Cost</strong></td>
                <td class="info-value">{{this.notify.truncateAndFloor( this.registrationForm.value.rate) }}</td>
                <td class="info-unit">Rs</td>
              </tr>
              <tr>
                <td class="info-key"><strong>Portion Cost</strong></td>
                <td class="info-value">{{ this.notify.truncateAndFloor(getCostPerPortion()) }}</td>
                <td class="info-unit">Rs</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #openUnitCostDialog>
    <div class="dialog-container">
      <div class="close-btn">
        <button mat-icon-button class="close-btn-icon" aria-label="Close" matTooltip="close" (click)="closeInfoDialog()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="mx-1 py-2 px-3">
        <div class="text-center my-2 p-2 bottom-titles">
          <span>Detailed Info</span>
        </div>
        <div class="portion-info">
          <table class="info-table">
            <tbody>
              <tr>
                <td class="info-key"><strong>WAC Per KG</strong></td>
                <!-- <td class="info-value">{{ this.notify.truncateAndFloor((this.registrationForm.value.rate / this.registrationForm.value.weightInUse) * 1000) }}</td> -->
                <td class="info-value">{{ this.notify.truncateAndFloor(getPerKGCost(),2) }}</td>
                <td class="info-unit">Rs</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #openDeleteDialog>
    <div class="dialog-container">
      <div class="close-btn">
        <button mat-icon-button class="close-btn-icon" aria-label="Close" matTooltip="close"
        (click)="closeInfoDialog()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="registration-form py-2 px-3">
        <div class="text-center my-2 p-2 bottom-titles">
          <span>Confirm Ingredient Deletion</span>
        </div>
        <div class="portion-info1">

          <div class="mb-3" >
            Deleting this ingredient is permanent and will impact past records. Alternatively, you can choose to discontinue it
                   </div>
          <div class="d-flex justify-content-center gap-3 m-2">
            <button mat-raised-button class="deleteBtn" matTooltip="delete" (click)="deleteData()">
              Yes, Delete
            </button>
            <button mat-raised-button class="discBtn" matTooltip="discontinue" (click)="discontinueData()">
              Just, Discontinue
            </button>
          </div>

        </div>
      </div>
    </div>
  </ng-template>

  <ng-template #discontinuedSelectDialog>
    <div class="registration-form py-2 px-3">
      <div class="text-center my-2 p-2 bottomTitles">
        <span>Discontinued Location</span>
      </div>
      <div class="m-3 infoText text-center">
        Would you like to discontinue {{selectedData}} ?
      </div>
      <div class="text-end m-2">
        <button mat-raised-button (click)="discontinuedSelectData()" color="accent" matTooltip="Update" class="m-1">
          Yes</button>
        <button (click)="closeDiscontinuedDialog()" mat-raised-button matTooltip="close" class="m-1">
          No</button>
      </div>
    </div>
  </ng-template>

</div>

