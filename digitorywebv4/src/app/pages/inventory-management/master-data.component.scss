/* Tab styling moved to global styles */

/* Fix for container width */
:host {
  display: block;
  width: 100%;
}

mat-card {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

@media (max-width: 600px) {
  .dialog-container {
    padding: 10px;
  }

  .bottom-titles {
    font-size: 1.2em;
  }

  .info-content {
    font-size: 1em;
  }
}

.portion-info1{
  margin-top: 20px;
  padding: 10px;
}

