import { ChangeDetectionStrategy, Component, OnInit, Query<PERSON>ist, ViewChildren, Inject, ViewChild, ChangeDetectorRef, TemplateRef, HostListener } from '@angular/core';
import { ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Inventory } from 'src/app/models/user.model';
import { InventoryService } from 'src/app/services/inventory.service';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule, MatOption } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { Observable, ReplaySubject, Subject, debounceTime, distinctUntilChanged, first, map, startWith, takeUntil } from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { AutocompleteComponent } from "../../../../components/autocomplete/autocomplete.component";
import { ShareDataService } from 'src/app/services/share-data.service';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AuthService } from 'src/app/services/auth.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatToolbarModule } from '@angular/material/toolbar';
import { NotificationService } from 'src/app/services/notification.service';
import { MatSnackBarModule, } from '@angular/material/snack-bar';
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { MasterDataService } from 'src/app/services/master-data.service';
// import { MatStepper, MatStepperModule } from '@angular/material/stepper';
// import { STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { MatChipsModule } from '@angular/material/chips';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { CheckDataService } from 'src/app/services/check-data.service';
import { EmptyStateComponent } from 'src/app/components/empty-state/empty-state.component';

@Component({
  selector: 'app-action',
  standalone: true,
  templateUrl: './action.component.html',
  styleUrls: ['./action.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    MatChipsModule,
    MatDialogModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatDividerModule,
    AutocompleteComponent,
    MatTableModule,
    NgxMatSelectSearchModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatToolbarModule,
    MatSnackBarModule,
    NgxSkeletonLoaderModule,
    MatSlideToggleModule,
    EmptyStateComponent
    // MatStepperModule
  ],
  // providers: [
  //   {
  //     provide: STEPPER_GLOBAL_OPTIONS,
  //     useValue: { showError: true },
  //   },
  // ],
})
export class ActionComponent implements OnInit {
  minWidth: string = '83vw';
  minHeight: string = '87vh';
  checkWidth: any = 1200;
  packageNames: any[];
  selectedData: string;
  defaultProcuredAtData: any = [];
  defaultIssuedToData: any = [];
  discontinuedProcuredAtData: any = [];
  discontinuedIssuedToData: any = [];
  selectedDropDown: any;
  groupData: any;
  discontinuedLocations: any;
  checkDataValidation: any[];
  ingredientName: any;
  showIngredientName: boolean = false;
  inventoryMatch: any;
  packagingMatch: any;
  code: any;
  noMatchFound: boolean;
  msg: any;
  aiSearch: boolean = false;
  existingItems: number;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    const width = event.target.innerWidth;
    this.checkWidth = event.target.innerWidth
    if (width <= 480) {
      this.minWidth = '100vw';
      this.minHeight = '100vh';
    } else if (width <= 768) {
      this.minWidth = '95vw';
      this.minHeight = '95vh';
    } else if (width <= 1200) {
      this.minWidth = '90vw';
      this.minHeight = '90vh';
    } else {
      this.minWidth = '83vw';
      this.minHeight = '87vh';
    }
  }
  @ViewChildren(MatOption) matOptions: QueryList<MatOption>;
  @ViewChild('widgetsContent', { read: ElementRef }) public widgetsContent;
  // @ViewChild('stepper', { static: false }) private stepper: MatStepper;
  question = 'Would you like to add "';
  itemNameOptions: Observable<string[]>;
  packNameOptions: Observable<string[]>;
  catBank: Observable<string[]>;
  subCatBank: Observable<string[]>;
  itemNameControl = new FormControl('');
  vendorList: any;
  registrationForm!: FormGroup;
  packagingForm!: FormGroup;
  userIdToUpdate!: number;
  isUpdateActive: boolean = false;
  categories: any[];
  subCategories: any[];
  isDuplicate: boolean;
  displayedColumns = ['position', 'packageName','brand','quantityPerUnit' ,'unitUOM', 'packagePrice']
  dataSource = new MatTableDataSource<any>([]);
  isPackaging: boolean = false;
  updatePackaging: boolean = false;
  isReadOnly: boolean = true;
  isInvFormCheck: boolean = true;
  dropDownData: any[];
  loadBtn: boolean = false;
  filteredData: any[];
  user: any;
  vendorObject: any;
  public vendorBank: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  public vendor: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public issuedToBank: any[] = [];
  public issuedToFilterCtrl: FormControl = new FormControl();
  public workAreas: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public procuredAtBank: any[] = [];
  public procuredAtFilterCtrl: FormControl = new FormControl();
  public procuredAtLocation: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public itemsBank: any[] = [];
  public itemsFilterCtrl: FormControl = new FormControl();
  public items: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  baseData: object = {};
  isCreated: boolean = false;
  locationData: any[] = [];
  locationList: any[] = [];
  updateBtnActive: boolean = false;
  loadSpinnerForApi: boolean = false;
  isCreateButtonDisabled = false;
  isUpdateButtonDisabled = false;
  loadSpinnerForApiPack: boolean = false;
  isLinear = false;
  isEditable = false;
  newCategory: any;
  newSubCategory: any;
  catAndsubCat = {};
  isPackageDataReady: boolean = false;
  packageDialog : any
  @ViewChild('scrollContainer', { static: true }) scrollContainer: ElementRef;
  loadInvBtn : boolean = true;
  loadPackBtn : boolean = true;
  ivnItem: any;
  @ViewChild('openDraftChangeDialog') openDraftChangeDialog: TemplateRef<any>;
  @ViewChild('deleteItemDialog') deleteItemDialog: TemplateRef<any>;
  @ViewChild('discontinuedSelectDialog') discontinuedSelectDialog: TemplateRef<any>;
  @ViewChild('invalidDataDialog') invalidDataDialog: TemplateRef<any>;

  dialogRef: MatDialogRef<any>;
  invData: any;
  packData = [];
  isChecked: boolean = false;
  isExpiryChecked: boolean = false;
  inputValue: string = '';
  packNames: any[];
  selectedUOM: string = '';
  @ViewChild('addPackaging') addPackaging!: TemplateRef<any>;
  isButtonFocused: boolean = false;
  constructor(
    private fb: FormBuilder,
    private masterDataService: MasterDataService,
    private api: InventoryService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public dialog: MatDialog,
    private sharedData: ShareDataService,
    private checkDataService: CheckDataService,
    private auth: AuthService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
    public notify: NotificationService,
    private el: ElementRef,
    private cd: ChangeDetectorRef
  ) {
    this.user = this.auth.getCurrentUser();
    this.baseData = this.sharedData.getBaseData().value;

    let tenantId = this.user.tenantId
      this.api.getRolesListDiscontinuedLocations(tenantId)
      .subscribe((res) => {
        if(res['result'] == 'success' && res['discontinuedLocations']){
          this.discontinuedLocations = res['discontinuedLocations'];
        }
        this.cd.detectChanges();
      });

    this.dataSource.data = [];
    this.newCategory = this.baseData['inventory master'].map(cat => cat.category.toUpperCase());
    this.getLocationCall();

    this.registrationForm = this.fb.group({
      // itemName: new FormControl<string>('', [Validators.required, Validators.maxLength(100)]),
      itemName: new FormControl<string>('', [Validators.required, this.noStartingSpaceValidator()]),
      itemCode: new FormControl<string>('', Validators.required),
      category: new FormControl<string[]>(null, [Validators.required]),
      subCategory: new FormControl<string[]>(null, Validators.required),
      classification: new FormControl<string>('Stockable', Validators.required),
      vendor: new FormControl<string[]>(null, Validators.required),
      inventoryUom: new FormControl<string>('', Validators.required),
      closingUOM: new FormControl<string>('', Validators.required),
      procuredAt: new FormControl<string[]>(null, Validators.required),
      issuedTo: new FormControl<string[]>(null, Validators.required),
      taxRate: new FormControl<number>(0, Validators.required),
      weight: new FormControl<number>(0, [Validators.required, Validators.min(1)]),
      yield: new FormControl<number>(1, [Validators.required]),
      rate: new FormControl<number>(1, [Validators.required]),
      finalRate: new FormControl<number>(1),
      leadTime: new FormControl<number>(1, Validators.required),
      discontinued: new FormControl<string>('no', Validators.required),
      stockConversion: new FormControl<string>('no', Validators.required),
      childItemCode: new FormControl<string[]>(null),
      ledger: new FormControl<string>('', Validators.required),
      itemType: new FormControl<string>('Inventory'),
      modified: new FormControl<string>(''),
      row_uuid: new FormControl<string>(''),
      recovery: new FormControl<string>(''),
      hsnCode: new FormControl<string>(''),
    }) as FormGroup;

    this.packagingForm = this.fb.group({
      inventoryCode: new FormControl<string>('', Validators.required),
      itemName: new FormControl<string>('', Validators.required),
      category: new FormControl<string[]>(null),
      subCategory: new FormControl<string[]>(null),
      packageName: new FormControl<string>('', [Validators.required,Validators.minLength(1) ,Validators.maxLength(100),  Validators.pattern(/^(\s+\S+\s*)*(?!\s).*$/)]),
      brand: new FormControl<string>('N/A', Validators.required),
      package: new FormControl<number>(1, [Validators.required, Validators.min(1)]),
      quantityPerUnit: new FormControl<number>(1, [Validators.required, Validators.min(0.001)]),
      totalQtyOfPackage: new FormControl<number>(0, Validators.required),
      unitUOM: new FormControl<string>('', Validators.required),
      emptyBottleWeight: new FormControl<number>(0),
      parLevel: new FormControl<number>(0),
      fullBottleWeight: new FormControl<number>(0),
      packagePrice: new FormControl<number>(1, [Validators.required, Validators.min(0.1)]),
      // expiryDate: new FormControl<string>('no', Validators.required),
      discontinued: new FormControl<string>('no', Validators.required),
      TenantId: new FormControl<string>(this.user.tenantId),
      modified: new FormControl<string>(''),
      row_uuid: new FormControl<string>(''),
    }) as FormGroup;

    this.isDuplicate = this.dialogData.key;
    if (this.dialogData.key == false) {
      this.isUpdateActive = true;
    }
    this.sharedData.getItemNames.pipe(first()).subscribe(obj => {
      let names = obj.itemNames.map(item => item.itemName)
      this.packNames = obj.packageNames.filter((value, index, self) => self.indexOf(value) === index);

      this.vendorObject = obj.vendorObject
      this.itemNameOptions = this.itemNameControl.valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), names)));
      this.packNameOptions = this.packagingForm.get('packageName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.packNames)));

      this.vendorList = obj.vendor;
      this.vendorBank = obj.vendor;
      let uniqueArray = [...new Set(this.vendorBank)];
      this.vendor.next(uniqueArray.slice());
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.Filter(this.vendorBank, this.vendorFilterCtrl, this.vendor);
      });
      this.itemsBank = obj.itemNames;
      this.items.next(this.itemsBank.slice());
      this.itemsFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.itemFilter(this.itemsBank, this.itemsFilterCtrl, this.items);
      });
    });
  }

  ngOnInit(): void {
    // Implement debounce search with AI search endpoint
    this.itemNameControl.valueChanges.pipe(
      debounceTime(500), // Wait for 500ms after the last keystroke (increased for API call)
      distinctUntilChanged(), // Only emit when the value has changed
      takeUntil(this._onDestroy)
    ).subscribe(value => {
      if (value && value.trim()) {
        this.search(value); // Call the AI search endpoint
      } else {
        // Reset search-related states when input is empty
        this.showIngredientName = false;
        this.noMatchFound = false;
      }
    });

    this.activatedRoute.params.pipe(first()).subscribe(val => {
      this.userIdToUpdate = val['id'];
      if (this.userIdToUpdate) {
        this.isUpdateActive = true;
        this.api.getRegisteredUserId(this.userIdToUpdate).pipe(first()).subscribe({
          next: (user:Inventory) => {
            // this.registrationForm.setValue({
            //   itemName: user.itemName,
            //   itemCode: user.itemCode,
            //   category: user.category,
            //   subCategory: user.subCategory,
            //   classification: user.classification,
            //   vendor: user.vendor,
            //   inventoryUom: user.inventoryUom,
            //   closingUOM: user.closingUOM,
            //   procuredAt: user.procuredAt,
            //   issuedTo: user.issuedTo,
            //   taxRate: user.taxRate,
            //   weight: user.weight,
            //   yield: user.yield,
            //   rate: user.rate,
            //   finalRate: user.finalRate,
            //   leadTime: user.leadTime,
            //   discontinued: user.discontinued,
            //   row_uuid: user.row_uuid
            // })
          },
          error: (err) => { console.log(err) }
        })
      }
    })
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }


//########################## PREFILL ################################

  preFillInventoryForm(invItem :any) {
    this.ivnItem = invItem;
    this.dataSource.data = this.baseData['packagingmasters'] ? this.baseData['packagingmasters'].filter(item => item.InventoryCode == invItem['itemCode']) : [];
    this.dataSource.data = this.removeDuplicatePackages(this.dataSource.data);
    this.packageNames = this.dataSource.data.map(item => item.PackageName)
    this.isPackageDataReady = true
    this.cd.detectChanges();
    if (!Array.isArray(invItem['vendor']) && !Array.isArray(invItem['issuedTo']) && !Array.isArray(invItem['procuredAt'])) {
      invItem['vendor'] = invItem['vendor'] ? invItem['vendor'].split(',') : [];
      invItem['issuedTo'] = invItem['issuedTo'] ? invItem['issuedTo'].split(',') : [];
      invItem['procuredAt'] = invItem['procuredAt'] ? invItem['procuredAt'].split(',') : [];
    }
    if (invItem['category']) {
      this.getSubCategories(invItem['category'])
    }
    this.defaultProcuredAtData = invItem['procuredAt']
    this.defaultIssuedToData = invItem['issuedTo']
    // this.discontinuedProcuredAtData.push(...(invItem['procuredAtDiscontinued'] ? invItem['procuredAtDiscontinued'].split(',') : ''))
    // this.discontinuedIssuedToData.push(...(invItem['issuedToDiscontinued'] ? invItem['issuedToDiscontinued'].split(',') : ''))
    if((this.discontinuedLocations && this.discontinuedLocations != undefined ) && this.discontinuedLocations.inventoryLocations){
      this.discontinuedProcuredAtData.push(...this.discontinuedLocations.inventoryLocations.procuredAtDiscontinued)
      this.discontinuedIssuedToData.push(...this.discontinuedLocations.inventoryLocations.issuedToDiscontinued)
    }

    this.registrationForm.patchValue({
      itemName: invItem['itemName'],
      itemCode: invItem['itemCode'],
      ledger: invItem['Ledger'] ?  invItem['Ledger'] : invItem['category'],
      category: invItem['category'],
      subCategory: invItem['subCategory'],
      classification: invItem['classification'],
      inventoryUom: invItem['Inventory UOM'] || invItem['inventoryUom'],
      closingUOM: invItem['closingUOM'],
      procuredAt: invItem['procuredAt'],
      issuedTo: invItem['issuedTo'],
      taxRate: this.notify.truncateAndFloor(invItem['taxRate']),
      weight: invItem['weight'],
      yield: this.notify.truncateAndFloor(invItem['yield']) ,
      rate: this.notify.truncateAndFloor(invItem['rate']) ,
      finalRate: this.notify.truncateAndFloor(invItem['finalRate']),
      stockConversion: ['no', 'No', 'N', null,''].includes(invItem['Stock Conversion']) ? 'no' : 'yes',
      // leadTime:  invItem['leadTime(days)'] ?  invItem['leadTime(days)'] : 0, && invItem['leadTime(days)'].trim()
      discontinued: ['no','NO' ,'No', 'N', null,''].includes(invItem['Discontinued']) ? 'no' : 'yes',
      itemType: invItem['itemType'],
      childItemCode: invItem['Child ItemCode'] ? invItem['Child ItemCode'].split(',') : undefined,
      row_uuid: invItem['row_uuid'],
      hsnCode: invItem['HSN_SAC'],
    });
    this.selectedUOM = invItem['Inventory UOM'] || invItem['inventoryUom'];
    if (!invItem['classification']) {
      this.registrationForm.patchValue({ classification: 'Stockable' });
    }
    if (!invItem['leadTime(days)'] || invItem['leadTime(days)'] === null || invItem['leadTime(days)'] === '') {
      this.registrationForm.patchValue({ leadTime: 0 });
      invItem['leadTime(days)'] = 0;
    }
    let findVendors = invItem['vendor'].map(item => {
      const foundVendor = this.vendorObject.find(v => v.vendorId === item);
      return foundVendor ? foundVendor.vendorName : null;
    });
    const notValid = findVendors.every(element => element === null);
    if (notValid) {
      this.registrationForm.patchValue({ vendor: invItem['vendor'] });
    } else {
      invItem['vendor'] = findVendors
      this.registrationForm.patchValue({ vendor: invItem['vendor'] });
    }

    this.vendorBank = this.vendorBank.filter(item => !invItem['vendor'].includes(item));
    this.vendorBank.unshift(...invItem['vendor']);
    let uniqueArray = [...new Set(this.vendorBank)];
    this.vendor.next(uniqueArray.slice());
    this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this.vendorBank, this.vendorFilterCtrl, this.vendor);
    });
    this.loadInvBtn = false;
    this.locationChange(this.registrationForm.value.procuredAt);

  }

  removeDuplicatePackages(items) {
    const uniquePackages = new Set();
    const uniqueItems = [];
    items.forEach(item => {
        const packageName = item.PackageName;
        if (!uniquePackages.has(packageName)) {
            uniquePackages.add(packageName);
            uniqueItems.push(item);
        }
    });
    return uniqueItems;
}

  preFillPackageForm(element :any, addPackaging) {
    const parLevel = element['ParLevel'] ? element['ParLevel'] : 0
    // element['ExpiryDate'] = element.hasOwnProperty('ExpiryDate') && element['ExpiryDate'] === 'yes' ? 'yes' : 'no';
    this.cd.detectChanges();
    this.packagingForm.patchValue({
      packageName: element['PackageName'],
      tenantId: element['TenantId'],
      category: element['category'],
      subCategory: element['subCategory'],
      inventoryCode: element['InventoryCode'] || this.code,
      itemName: element['ItemName'],
      brand: 'N/A',
      package: element['Units/ package'] ? element['Units/ package'] : 1,
      quantityPerUnit: this.notify.truncateAndFloor(element['Quantity per unit']),
      totalQtyOfPackage: element['Total qty of package'],
      unitUOM: element['UnitUOM'],
      emptyBottleWeight: element['Empty bottle weight'],
      parLevel: parLevel,
      fullBottleWeight: element['Full bottle weight'],
      packagePrice: element['PackagePrice'],
      // expiryDate: element['ExpiryDate'],
      discontinued: element.hasOwnProperty('Discontinued')
                    ? (['no', 'NO', 'No', 'N', null, ''].includes(element['Discontinued']) ? 'no' : 'yes')
                    : element['Discontinued'] || 'no',

      row_uuid: element['row_uuid'],
    });
    this.loadPackBtn = false;
    if (addPackaging) {
      this.updatePackaging = true;
      this. packageDialog = this.dialog.open(addPackaging, { maxHeight: '95vh', maxWidth: '50vw' });
    }
  }

  newPackageTemplate(addPackaging) {
    this.updatePackaging = false;
    this.packagingForm.reset()
    this.packagingForm.patchValue({
      category: this.registrationForm.value['category'],
      subCategory: this.registrationForm.value['subCategory'],
      inventoryCode: this.registrationForm.value['itemCode'],
      itemName: this.registrationForm.value['itemName'],
      unitUOM: this.registrationForm.value['inventoryUom'],
      package: 1,
      brand: 'N/A',
      quantityPerUnit: 0,
      totalQtyOfPackage: 0,
      emptyBottleWeight: 0,
      parLevel: 0,
      fullBottleWeight: 0,
      packagePrice: 1,
      row_uuid: '',
      // expiryDate: 'no',
      discontinued: 'no',
    });
    this.packageDialog = this.dialog.open(addPackaging, { maxHeight: '95vh', maxWidth: '50vw'});
  }

  closePackage(){
    this.packageDialog.close()
    this.packagingForm.reset();
    this.packagingForm.patchValue({
      category: this.registrationForm.value['category'],
      subCategory: this.registrationForm.value['subCategory'],
      inventoryCode: this.registrationForm.value['itemCode'],
      itemName: this.registrationForm.value['itemName'],
      unitUOM: this.registrationForm.value['inventoryUom'],
      package: 1,
      brand: 'N/A',
      quantityPerUnit: 0,
      totalQtyOfPackage: 0,
      emptyBottleWeight: 0,
      parLevel: 0,
      fullBottleWeight: 0,
      packagePrice: 1,
      // expiryDate: 'no',
      discontinued: 'no',
    });
    this.packageDialog.afterClosed().subscribe(_ => {
      this.isPackageDataReady = true;
      this.cd.detectChanges();
    });
  }

//######################## INVENTORY ##############################


checkInventoryFormValidation(){
  if (this.registrationForm.invalid) {
    const invalidControls: { [key: string]: { value: any; errors: any } } = {};
    Object.keys(this.registrationForm.controls).forEach((key) => {
    const control = this.registrationForm.get(key);
      if (control && control.invalid) {
        invalidControls[key] = {
          value: control.value,
          errors: control.errors,
        };
      }
    });
    this.registrationForm.markAllAsTouched();
    // console.log('Invalid Controls:', invalidControls);
    this.cd.detectChanges();
    return true
  }
  return false
}

checkPackageValid(updated){
  const discontinuedPackage = this.dataSource.data.filter(item => {
    const discontinuedValue = (item.Discontinued || '').toString().trim().toLowerCase();
    return ['no', 'n', ''].includes(discontinuedValue);
  });
  if(updated['Discontinued'] != 'yes' && discontinuedPackage.length == 0){
    return true
  }
  return false
}


  createInventory() {
    this.loadSpinnerForApi = true;
    this.baseData = this.sharedData.getBaseData().value
    let updated = this.convertInventoryKeys();
    this.checkDataValidation = this.checkDataService.checkSheet(updated , 'inventory')

    if(this.checkInventoryFormValidation()){
      this.notify.snackBarShowError('Please fill out all required fields')

    }else if(this.checkPackageValid(updated)){
      this.notify.snackBarShowWarning('Inventory item requires at least one active package')

    }else if(this.checkDataValidation.length > 0){
      // this.notify.snackBarShowError('Give the valid value')
      this.dialogRef = this.dialog.open(this.invalidDataDialog, {
        width: '500px',
      });
      this.dialogRef.afterClosed().subscribe(result => {
      });
    }else{
      // let updated = this.convertInventoryKeys();
      let current = {}
      updated['vendor'] = updated['vendor'].join(',')
      updated['issuedTo'] = updated['issuedTo'].join(',')
      updated['procuredAt'] = updated['procuredAt'].join(',')
      updated['modified'] = "yes";
      updated['recovery'] = updated['weight'] * updated['yield']
      current['inventory master'] = this.baseData['inventory master']
      current['inventory master'].unshift(updated);
      current['inventory master'] = current['inventory master'].filter(item => item.modified === "yes");
      this.dataSource.data.forEach(item => {
        if (item.hasOwnProperty("ItemName")) {
          item.ItemName = this.registrationForm.value.itemName;
        }
        if (!item.hasOwnProperty("row_uuid")) {
          item.row_uuid = current['inventory master'][0]['row_uuid']
        }
        if (!item.hasOwnProperty("InventoryCode")) {
          item.InventoryCode = current['inventory master'][0]['itemCode']
        }
        if (!item.hasOwnProperty("category")) {
          item.category = current['inventory master'][0]['category']
        }
        if (!item.hasOwnProperty("subCategory")) {
          item.subCategory = current['inventory master'][0]['subCategory']
        }
      });
      current['packagingmasters'] = this.dataSource.data
      this.api.updateData({
        'tenantId' : this.user.tenantId,
        'userEmail' : this.user.email,
        'type' : 'inventory',
        'data' : current
      }).pipe(first()).subscribe({
        next: (res) => {
          if (res['success']) {
            this.isCreated = true;
            this.cd.detectChanges();
            this.notify.snackBarShowSuccess('Inventory created successfully');
            this.closeInventory();
          }
        },
        error: (err) => { console.log(err)}
      });
    }

    setTimeout(() => {
      this.loadSpinnerForApi = false;
      this.cd.detectChanges();
    }, 2000);

    // this.isCreateButtonDisabled = true;
    // this.loadSpinnerForApi = true
    // if (this.registrationForm.invalid) {
    //   const invalidControls: { [key: string]: { value: any; errors: any } } = {};
    //   Object.keys(this.registrationForm.controls).forEach((key) => {
    //   const control = this.registrationForm.get(key);
    //     if (control && control.invalid) {
    //       invalidControls[key] = {
    //         value: control.value,
    //         errors: control.errors,
    //       };
    //     }
    //   });
    //   this.registrationForm.markAllAsTouched();
    //   // console.log('Invalid Controls:', invalidControls);
    //   this.notify.snackBarShowError('Please fill out all required fields')
    //   this.loadSpinnerForApi = false;
    //   this.isCreateButtonDisabled = false;
    //   this.cd.detectChanges();
    // } else {
    //   let updated = this.convertInventoryKeys();
    //   let current = {}
    //   updated['vendor'] = updated['vendor'].join(',')
    //   updated['issuedTo'] = updated['issuedTo'].join(',')
    //   updated['procuredAt'] = updated['procuredAt'].join(',')
    //   updated['modified'] = "yes";
    //   updated['recovery'] = updated['weight'] * updated['yield']
    //   current['inventory master'] = this.baseData['inventory master']
    //   current['inventory master'].unshift(updated);
    //   current['inventory master'] = current['inventory master'].filter(item => item.modified === "yes");
    //   this.dataSource.data.forEach(item => {
    //     if (item.hasOwnProperty("ItemName")) {
    //       item.ItemName = this.registrationForm.value.itemName;
    //     }
    //   });
    //   current['packagingmasters'] = this.dataSource.data
    //   this.api.updateData({
    //     'tenantId' : this.user.tenantId,
    //     'userEmail' : this.user.email,
    //     'type' : 'inventory',
    //     'data' : current
    //   }).pipe(first()).subscribe({
    //     next: (res) => {
    //       if (res['success']) {
    //         this.isCreated = true;
    //         this.loadSpinnerForApi = false;
    //         this.cd.detectChanges();
    //         this.notify.snackBarShowSuccess('Inventory created successfully');
    //         this.closeInventory();
    //       }
    //     },
    //     error: (err) => { console.log(err)}
    //   });
    // }
  }

  restrictKeys(event: KeyboardEvent): void {
    const disallowedKeys = ['@', '#', '$', '%', '^', '*', '(', ')', '_', '-', '=', '+', '{', '}', '[', ']', '|', '\\', ':', ';', '"', '\'', '<', '>', ',', '.', '?', '/', '~', '`'];
    if (disallowedKeys.includes(event.key)) {
      event.preventDefault();
    }
  }

  updateInventory() {
    this.loadSpinnerForApi = true;
    this.baseData = this.sharedData.getBaseData().value
    let updated = this.convertInventoryKeys();
    this.checkDataValidation = this.checkDataService.checkSheet(updated , 'inventory')
    let current = {}

    if(this.checkInventoryFormValidation()){
      this.notify.snackBarShowError('Please fill out all required fields')

    }else if(this.checkPackageValid(updated)){
      this.notify.snackBarShowWarning('Inventory item requires at least one active package')

    }else if(this.checkDataValidation.length > 0){

      this.dialogRef = this.dialog.open(this.invalidDataDialog, {
        width: '500px',
      });
      this.dialogRef.afterClosed().subscribe(result => {
      });

    }else{

      this.setDiscontinuedDataInRolopos();
        let currentObj = this.baseData['inventory master'].find((el) => el.itemCode == updated['itemCode'])
        let index = this.baseData['inventory master'].indexOf(currentObj)
        updated['modified'] = "yes";
        updated['recovery'] = updated['weight'] * updated['yield']
        updated['vendor'] = updated['vendor'].join(',')
        updated['issuedTo'] = updated['issuedTo'].join(',')
        updated['procuredAt'] = updated['procuredAt'].join(',')
        if(this.discontinuedIssuedToData.length > 0){
          const workAreas = this.discontinuedIssuedToData.flatMap(item => item.workAreas);
          updated['issuedToDiscontinued'] = workAreas.length > 0 ? workAreas.join(',') : '';
        }
        updated['procuredAtDiscontinued'] = this.discontinuedProcuredAtData.length > 0 ? this.discontinuedProcuredAtData.join(',') : '';
        current['inventory master'] = this.baseData['inventory master']
        current['inventory master'][index] = updated;
        current['inventory master'] = current['inventory master'].filter(item => item.modified === "yes");
        this.dataSource.data.forEach(item => {
          if (item.hasOwnProperty("ItemName")) {
            item.ItemName = this.registrationForm.value.itemName;
            item.UnitUOM = this.registrationForm.value.inventoryUom;
            item.packageUOM = this.registrationForm.value.inventoryUom;
            if(updated['Discontinued'] == 'yes'){
              item.Discontinued = 'yes';
            }
          }
        });
        this.sharedData.setPackages(this.dataSource.data)
        let packages
        this.sharedData.getPackage.pipe(first()).subscribe(obj => {
            packages = obj
        })
        current['packagingmasters'] = packages;
        this.api.updateData({
          'tenantId' : this.user.tenantId,
          'userEmail' : this.user.email,
          'type' : 'inventory',
          'data' : current
        }).pipe(first()).subscribe({
          next: (res) => {
            if (res['success']) {
              this.cd.detectChanges();
              this.notify.snackBarShowSuccess('Inventory updated successfully');
              this.closeInventory();
            }
          },
          error: (err) => { console.log(err) }
        });
    }


    setTimeout(() => {
      this.loadSpinnerForApi = false;
      this.cd.detectChanges();
    }, 2000);

    // // this.isUpdateButtonDisabled = true;
    // this.baseData = this.sharedData.getBaseData().value
    // this.loadSpinnerForApi = true;
    // const discontinuedPackage = this.dataSource.data.filter(item => {
    //   const discontinuedValue = (item.Discontinued || '').toString().trim().toLowerCase();
    //   return ['no', 'n', ''].includes(discontinuedValue);
    // });

    // if (this.registrationForm.invalid) {
    //   const invalidControls: { [key: string]: { value: any; errors: any } } = {};
    //   Object.keys(this.registrationForm.controls).forEach((key) => {
    //   const control = this.registrationForm.get(key);
    //     if (control && control.invalid) {
    //       invalidControls[key] = {
    //         value: control.value,
    //         errors: control.errors,
    //       };
    //     }
    //   });
    //   this.registrationForm.markAllAsTouched();
    //   // console.log('Invalid Controls:', invalidControls);
    //   this.notify.snackBarShowError('Please fill out all required fields')
    //   this.loadSpinnerForApi = false;
    //   this.isUpdateButtonDisabled = false;
    //   this.cd.detectChanges();
    // } else {
      // let updated = this.convertInventoryKeys();
    //   let checkValidation = this.checkDataService.checkSheet(updated , 'inventory')
    //   console.log('checkValidation' , checkValidation);

    //   let current = {}
    //   // const isItemNameNotEmpty = updated['itemName'].trim() !== "";
    //   // const isItemCodeNotEmpty = updated['itemCode'].trim() !== ""
    //   // if (!isItemNameNotEmpty || !isItemCodeNotEmpty) {
    //   //   this.isInvFormCheck = false;
    //   //   this.loadSpinnerForApi = false;
    //   //   this.registrationForm.markAllAsTouched();
    //   //   this.notify.snackBarShowError('Please fill out all required fields')
    //   //   // this.isUpdateButtonDisabled = false;
    //   //   this.cd.detectChanges();
    //   // } else

    //   if((updated['Discontinued'] != 'yes' && discontinuedPackage.length == 0)){
    //     this.isInvFormCheck = false;
    //     this.loadSpinnerForApi = false;
    //     this.registrationForm.markAllAsTouched();
    //     this.notify.snackBarShowWarning('Inventory item requires at least one active package')
    //     // this.isUpdateButtonDisabled = false;
    //     this.cd.detectChanges();
    //   } else {
    //     this.setDiscontinuedDataInRolopos();
    //     let currentObj = this.baseData['inventory master'].find((el) => el.itemCode == updated['itemCode'])
    //     let index = this.baseData['inventory master'].indexOf(currentObj)
    //     updated['modified'] = "yes";
    //     updated['recovery'] = updated['weight'] * updated['yield']
    //     updated['vendor'] = updated['vendor'].join(',')
    //     updated['issuedTo'] = updated['issuedTo'].join(',')
    //     updated['procuredAt'] = updated['procuredAt'].join(',')
    //     if(this.discontinuedIssuedToData.length > 0){
    //       const workAreas = this.discontinuedIssuedToData.flatMap(item => item.workAreas);
    //       updated['issuedToDiscontinued'] = workAreas.length > 0 ? workAreas.join(',') : '';
    //     }
    //     updated['procuredAtDiscontinued'] = this.discontinuedProcuredAtData.length > 0 ? this.discontinuedProcuredAtData.join(',') : '';
    //     current['inventory master'] = this.baseData['inventory master']
    //     current['inventory master'][index] = updated;
    //     current['inventory master'] = current['inventory master'].filter(item => item.modified === "yes");
    //     this.dataSource.data.forEach(item => {
    //       if (item.hasOwnProperty("ItemName")) {
    //         item.ItemName = this.registrationForm.value.itemName;
    //         item.UnitUOM = this.registrationForm.value.inventoryUom;
    //         item.packageUOM = this.registrationForm.value.inventoryUom;
    //         if(updated['Discontinued'] == 'yes'){
    //           item.Discontinued = 'yes';
    //         }
    //       }
    //     });
    //     current['packagingmasters'] = this.dataSource.data;
    //     // this.api.updateData({
    //     //   'tenantId' : this.user.tenantId,
    //     //   'userEmail' : this.user.email,
    //     //   'type' : 'inventory',
    //     //   'data' : current
    //     // }).pipe(first()).subscribe({
    //     //   next: (res) => {
    //     //     if (res['success']) {
    //     //       this.loadSpinnerForApi = false;
    //     //       this.cd.detectChanges();
    //     //       this.notify.snackBarShowSuccess('Inventory updated successfully');
    //     //       this.closeInventory();
    //     //     }
    //     //   },
    //     //   error: (err) => { console.log(err) }
    //     // });
    //   }
    // }
  }


  checkPackageFormValidation(){
    if (this.packagingForm.invalid) {
      const invalidControls: { [key: string]: { value: any; errors: any } } = {};
      Object.keys(this.packagingForm.controls).forEach((key) => {
      const control = this.packagingForm.get(key);
        if (control && control.invalid) {
          invalidControls[key] = {
            value: control.value,
            errors: control.errors,
          };
        }
      });
      // console.log('Invalid Controls:', invalidControls);
      this.packagingForm.markAllAsTouched();
      this.cd.detectChanges();
      return true
    }
    return false
  }

  addNewPackage() {

    let update = this.convertPackagingKeys();
    this.checkDataValidation = this.checkDataService.checkSheet(update , 'package')

    if(this.checkPackageFormValidation()){
      this.notify.snackBarShowError('Please fill out all required fields')

    }else if(this.checkDataValidation.length > 0){
      this.dialogRef = this.dialog.open(this.invalidDataDialog, {
        width: '500px',
      });
      this.dialogRef.afterClosed().subscribe(result => {
      });
    }
    else{
      this.isPackageDataReady = false;
      // let update = this.convertPackagingKeys();
      update['modified'] = "yes";
      update['TenantId'] = this.user.tenantId
      update['category'] = this.registrationForm.value.category
      update['subCategory'] = this.registrationForm.value.subCategory
      Object.entries(update).forEach(([key, value]) => {
        if (value === null || value === undefined || value === '') {
            return;
        }
        if (typeof value === 'number') {
          update[key] = this.notify.truncateAndFloor(value);
        }
      });
      this.dataSource.data.push(update)
      this.packData = this.dataSource.data;
      this.dataSource.data = this.packData;
      this.packageNames = this.dataSource.data.map(item => item.PackageName)
      this.packNames = Array.from(new Set(this.packNames.concat(this.packageNames)));
      this.packNames = this.packNames.filter((value, index, self) => self.indexOf(value) === index);
      this.packNameOptions = this.packagingForm.get('packageName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.packNames)));
      this.notify.snackBarShowSuccess('Package created successfully');
      this.packagingForm.reset();
      this.packagingForm.patchValue({
        category: this.registrationForm.value['category'],
        subCategory: this.registrationForm.value['subCategory'],
        inventoryCode: this.registrationForm.value['itemCode'],
        itemName: this.registrationForm.value['itemName'],
        unitUOM: this.registrationForm.value['inventoryUom'],
        package: 1,
        brand: 'N/A',
        quantityPerUnit: 0,
        totalQtyOfPackage: 0,
        emptyBottleWeight: 0,
        parLevel: 0,
        fullBottleWeight: 0,
        packagePrice: 1,
        // expiryDate: 'no',
        discontinued: 'no',
      });
      this.clearForm()
      this.isPackageDataReady = true;
    }


    // this.packagingForm.get('totalQtyOfPackage').setValue(this.packagingForm.value.quantityPerUnit)
    // if (this.packagingForm.invalid || !this.packagingForm.value.quantityPerUnit) {
    //   // this.packagingForm.markAllAsTouched();
    //   // this.notify.snackBarShowError('Please fill out all required fields')
    //   // this.loadSpinnerForApiPack = false;
    //   // this.cd.detectChanges();

    //   // const invalidControls: { [key: string]: { value: any; errors: any } } = {};
    //   // Object.keys(this.packagingForm.controls).forEach((key) => {
    //   // const control = this.packagingForm.get(key);
    //   //   if (control && control.invalid) {
    //   //     invalidControls[key] = {
    //   //       value: control.value,
    //   //       errors: control.errors,
    //   //     };
    //   //   }
    //   // });
    //   // this.packagingForm.markAllAsTouched();
    //   // console.log('Invalid Controls:', invalidControls);
    //   // this.notify.snackBarShowError('Please fill out all required fields')
    //   // this.loadSpinnerForApiPack = false;
    //   // this.cd.detectChanges();

    // } else {
    //   this.isPackageDataReady = false;
    //   let update = this.convertPackagingKeys();
    //   update['modified'] = "yes";
    //   update['TenantId'] = this.user.tenantId
    //   update['category'] = this.registrationForm.value.category
    //   update['subCategory'] = this.registrationForm.value.subCategory
    //   // update['newItem'] = true;
    //   Object.entries(update).forEach(([key, value]) => {
    //     if (value === null || value === undefined || value === '') {
    //         return;
    //     }
    //     if (typeof value === 'number') {
    //       update[key] = this.notify.truncateAndFloor(value);
    //     }
    //   });
    //   this.dataSource.data.push(update)
    //   this.packData = this.dataSource.data;
    //   this.dataSource.data = this.packData;
    //   this.packageNames = this.dataSource.data.map(item => item.PackageName)
    //   this.packNames = Array.from(new Set(this.packNames.concat(this.packageNames)));
    //   this.packNames = this.packNames.filter((value, index, self) => self.indexOf(value) === index);
    //   this.packNameOptions = this.packagingForm.get('packageName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.packNames)));
    //   this.notify.snackBarShowSuccess('Package created successfully');
    //   this.packagingForm.reset();
    //   this.packagingForm.patchValue({
    //     category: this.registrationForm.value['category'],
    //     subCategory: this.registrationForm.value['subCategory'],
    //     inventoryCode: this.registrationForm.value['itemCode'],
    //     itemName: this.registrationForm.value['itemName'],
    //     unitUOM: this.registrationForm.value['inventoryUom'],
    //     package: 1,
    //     brand: 'N/A',
    //     quantityPerUnit: 0,
    //     totalQtyOfPackage: 0,
    //     emptyBottleWeight: 0,
    //     fullBottleWeight: 0,
    //     packagePrice: 1,
    //     discontinued: 'no',
    //   });
    //   this.clearForm()
    //   this.isPackageDataReady = true;
    // }
  }

  clearForm() {
    Object.keys(this.packagingForm.controls).forEach(key => {
      this.packagingForm.get(key)?.setErrors(null);
    });
  }

  editExistingPackage() {
    let update = this.convertPackagingKeys();
    this.checkDataValidation = this.checkDataService.checkSheet(update , 'package')

    if(this.checkPackageFormValidation()){
      this.notify.snackBarShowError('Please fill out all required fields')

    }else if(this.checkDataValidation.length > 0){
      this.dialogRef = this.dialog.open(this.invalidDataDialog, {
        width: '500px',
      });
      this.dialogRef.afterClosed().subscribe(result => {
      });
    }
    else{
      update['modified'] = "yes";
      update['TenantId'] = this.user.tenantId
      update['category'] = this.registrationForm.value.category
      update['subCategory'] = this.registrationForm.value.subCategory
      let current = this.dataSource.data.find((el) => el.PackageName == update['PackageName'] && el.InventoryCode || this.code == update['InventoryCode'])
      if (current) {
        let index = this.dataSource.data.indexOf(current)
        this.dataSource.data[index] = update
        this.dataSource.data = [...this.dataSource.data];
        this.notify.snackBarShowSuccess('Package updated successfully');
        this.cd.detectChanges();
        this.closePackage();
      }
    }

  //   if (this.packagingForm.invalid) {
  //     this.packagingForm.markAllAsTouched();
  //     this.notify.snackBarShowError('Please fill out all required fields')
  //     this.loadSpinnerForApiPack = false;
  //     this.cd.detectChanges();
  //   } else {
  //     this.isPackageDataReady = false;
  //     let update = this.convertPackagingKeys();
  //     const isPackageNameNotEmpty = update['PackageName'].trim() !== "";
  //     if (!isPackageNameNotEmpty) {
  //       this.loadSpinnerForApi = false;
  //       this.registrationForm.markAllAsTouched();
  //       this.notify.snackBarShowError('Please fill out all required fields')
  //       this.cd.detectChanges();
  //     } else {
        // update['modified'] = "yes";
        // update['TenantId'] = this.user.tenantId
        // update['category'] = this.registrationForm.value.category
        // update['subCategory'] = this.registrationForm.value.subCategory
        // let current = this.dataSource.data.find((el) => el.PackageName == update['PackageName'] && el.InventoryCode == update['InventoryCode'])
        // if (current) {
        //   let index = this.dataSource.data.indexOf(current)
        //   this.dataSource.data[index] = update
        //   this.notify.snackBarShowSuccess('Package updated successfully');
        //   this.cd.detectChanges();
        //   this.closePackage();
        // }
  //     }
  // }
}

//########################## HELPER ################################

  protected Filter(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.toLowerCase().indexOf(search) > -1)
    );
  }

  protected FilterIssued(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    const filteredBank = bank.map(item => {
      const filteredWorkAreas = item.workAreas.filter(workArea => workArea.toLowerCase().indexOf(search) > -1);
      return { ...item, workAreas: filteredWorkAreas };
    });
    data.next(filteredBank);
  }

  protected itemFilter(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.itemName.toLowerCase().indexOf(search) > -1)
    );
  }

  protected _filter(value: string, input: string[]): string[] {
    const filterValue = value.trim().toLowerCase(); // Trim and convert to lowercase for consistent comparison
    let filtered = input.filter(option => option.toLowerCase().includes(filterValue));
    this.existingItems = filtered.length
    if (filtered.length === 0) {
      filtered = [`${this.question}${value}"`];
    }
    return filtered.slice(0,500);
  }

  get rate() {
    return this.registrationForm.get('rate');
  }

  get weight() {
    return this.registrationForm.get('weight');
  }

  get yield() {
    return this.registrationForm.get('yield');
  }

  get leadTime() {
    return this.registrationForm.get('leadTime');
  }

  get package() {
    return this.packagingForm.get('package');
  }

  get quantityPerUnit() {
    return this.packagingForm.get('quantityPerUnit');
  }

  get packagePrice() {
    return this.packagingForm.get('packagePrice');
  }

  get parLevel() {
    return this.packagingForm.get('parLevel');
  }

  toggleSelectAllVendor() {
    const control = this.registrationForm.controls['vendor'];
    if (control.value.length - 1 === this.vendorBank.length) {
      control.setValue([]);
    } else {
      control.setValue(this.vendorBank);
    }
  }

  toggleSelectAllIssuedTo() {
    const control = this.registrationForm.controls['issuedTo'];
    let data = [...this.issuedToBank.map(location => location.workAreas)];
    let flattenedArray = [].concat(...data);
    if (control.value.length - 1 === flattenedArray.length) {
      control.setValue(this.defaultIssuedToData);
    } else {
      control.setValue(flattenedArray);
    }
  }

  getChildItemCode(value) {
    this.registrationForm.value['childItemCode'] = value;
  }

  toggleSelectAllItems() {
    const control = this.registrationForm.controls['childItemCode'];
    if (control.value.length - 1 === this.itemsBank.length) {
      control.setValue([]);
      this.getChildItemCode(this.registrationForm.value.childItemCode)
    } else {
      control.setValue(this.itemsBank);
      this.getChildItemCode(this.registrationForm.value.childItemCode)
    }
  }

  toggleSelectAllProcuredAt() {
    const control = this.registrationForm.controls['procuredAt'];
    if (control.value.length - 1 === this.procuredAtBank.length) {
      control.setValue(this.defaultProcuredAtData);
      this.locationChange(this.registrationForm.value.procuredAt);
    } else {
      control.setValue(this.procuredAtBank);
      this.locationChange(this.registrationForm.value.procuredAt);
    }
  }

  applyFilter(filterValue: any) {
    this.dataSource.filter = filterValue.target.value.trim().toLowerCase();
  }

  filterDialog(filterValue) {
    this.filteredData = this.dropDownData.filter(item => item.toLowerCase().includes(filterValue.target.value.trim().toLowerCase()));
  }

  setTax(formData) {
    if (this.registrationForm.value.taxRate > 100) {
      this.registrationForm.get(formData).setValue(null);
      this.notify.snackBarShowInfo("Tax should be below 100%")
      this.focusFunction(formData);
    }
    if (this.registrationForm.value.yield > 1) {
      this.registrationForm.get(formData).setValue(null);
      // this.notify.snackBarShowInfo("Yield should be below 1")
    }
  }

  locationChange(event) {
    const selectedWorkAreasArray = this.locationList.filter(branch => event.includes(branch.abbreviatedRestaurantId))
    this.issuedToBank = selectedWorkAreasArray;
    if(this.discontinuedProcuredAtData.length > 0){
      this.discontinuedProcuredAtData.forEach(val => {
        this.issuedToBank = this.issuedToBank.map(item => {
          if (item.abbreviatedRestaurantId === val) {
            item.disabled = true;
          }
          return item;
        });
      })
    }
    this.workAreas.next(this.issuedToBank.slice());
    this.issuedToFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.FilterIssued(this.issuedToBank, this.issuedToFilterCtrl, this.workAreas);
    });
  }

  checkInventory(){
    if (this.isAnyModified(this.dataSource.data)) {
      this.dialogRef = this.dialog.open(this.openDraftChangeDialog, {
        width: '500px',
      });

      this.dialogRef.afterClosed().subscribe(result => {
      });

    } else {
      this.closeInventory();
    }
  }

  closeInfoDialog(){
    if (this.dialogRef) {
      this.dialogRef.close();
      this.closeInventory();
    }
  }

  isAnyModified(array) {
    for (let i = 0; i < array.length; i++) {
      if (array[i].modified === 'yes') {
        return true;
      }
    }
    return false;
  }

  closeInventory() {
    this.dataSource.data = [];
    this.masterDataService.setNavigation('inventoryList');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  nextTab() {
    if (this.registrationForm.invalid) {
      this.registrationForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.loadSpinnerForApi = false;
      this.cd.detectChanges();
    } else {
      // this.stepper.next();
    }
  }

checkInvItemName(filterValue) {
    filterValue = (filterValue.target.value).trim();
    let data = this.sharedData.getBaseData().value;
    const foundItemIndex = data['inventory master'].findIndex(item => item.itemCode === this.ivnItem.itemCode);
    if (foundItemIndex !== -1) {
      for (let i = 0; i < data['inventory master'].length; i++) {
        if (i === foundItemIndex) {
          continue;
        }
        if (data['inventory master'][i].itemName.toLowerCase() === filterValue.toLowerCase()) {
          this.registrationForm.get('itemName').setErrors({ 'itemExists': true });
          return;
        }
      }
      // this.registrationForm.get('itemName').setErrors(null);
    } else {
      this.registrationForm.get('itemName').setErrors({ 'itemNotFound': true });
    }
}

  checkPackItemName(filterValue){
    const packageName = this.packagingForm.get('packageName').value;
    let data = this.sharedData.getBaseData().value;
    data['packagingmasters'].push(...this.dataSource.data);
    const isItemAvailable = data['packagingmasters'].some(item =>
      item.InventoryCode === this.registrationForm.value.itemCode && item.PackageName.toLowerCase() === filterValue.target.value.toLowerCase()
    );
    if (isItemAvailable) {
      this.packagingForm.get('packageName').setErrors({ 'packItemExists': true });
    } else if(packageName.startsWith(' ')) {
      this.packagingForm.get('packageName').setErrors({ 'startsWithSpace' : true });
    } else {
      this.packagingForm.get('packageName').setErrors(null);
    }
    let element = this.dataSource.data.find(item => item.PackageName === filterValue.target.value)
    if (filterValue.key === 'Enter' && element) {
      this.updatePackaging = true;
    }else{
      this.updatePackaging = false;
    }
  }

  generateCode(code){
    let obj = {}
    let data
    obj['tenantId'] = this.user.tenantId
    obj['code'] = code
    this.api.getCode(obj).pipe(first()).subscribe({
      next: async (res) => {
        if (res['success']) {
          this.code =  res['data']
          this.registrationForm.get('itemCode').setValue(this.code)
          // this.dataSource.data = []
          this.isPackageDataReady = true;
        }
      },
    })
  }

  optionSelected(type: string, option: any) {
    let invItem = this.sharedData.getDataForFillTheForm(option.value, 'inventory master')
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
    if (option.value.indexOf(this.question) === 0) {
      this.addOption(type);
    }
  }

  search(value) {
    // Don't search if already searching or if value is empty
    if (this.aiSearch || !value?.trim()) return;

    this.aiSearch = true;
    this.showIngredientName = false;
    this.noMatchFound = false;
    this.ingredientName = '';

    const trimmedValue = value.trim();

    // Also update the regular search results while we wait for AI
    this.checkItem({ target: { value: trimmedValue } });

    let obj = {
      tenantId: this.user.tenantId,
      ingredient_name: trimmedValue,
    };

    this.api.itemNameSearch(obj).subscribe({
      next: (res: any) => {
        if (res) {
          if (res.inventory_match) {
            this.inventoryMatch = res.inventory_match;
            this.ingredientName = res.inventory_match.item_name;
            this.showIngredientName = true;
          }
          if (res.packaging_matches && res.packaging_matches.length > 0) {
            const packagingMatch = res.packaging_matches;
            this.packagingMatch = packagingMatch.map(({
              empty_bottle_weight,
              full_bottle_weight,
              package_name,
              quantity_per_unit,
              unit_per_package,
              unit_uom,
              package_price,
              item_name,
              ...rest
            }) => ({
              "Empty bottle weight": empty_bottle_weight,
              "Full bottle weight": full_bottle_weight,
              "PackageName": package_name,
              "Quantity per unit": quantity_per_unit,
              // "Total qty of package": unit_per_package,
              "Total qty of package": quantity_per_unit,
              "UnitUOM": unit_uom,
              "PackagePrice": package_price,
              "ItemName": item_name,
              ...rest
            }));
            this.showIngredientName = true;
          } else if (!res.inventory_match) {
            this.noMatchFound = true;
            this.msg = res.search_term;
          }
        } else {
          this.noMatchFound = true;
          this.msg = trimmedValue;
        }
      },
      error: (err) => {
        console.error('Error in AI search:', err);
        this.noMatchFound = true;
        this.msg = trimmedValue;
      },
      complete: () => {
        this.aiSearch = false;
        this.cd.detectChanges();
      }
    });
  }

  checkItem(event) {
    const trimmedValue = event.target.value.replace(/\s+$/, ''); // Remove trailing spaces
    const invItem = this.sharedData.getDataForFillTheForm(trimmedValue, 'inventory master');
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
    this.showIngredientName = false;
    this.noMatchFound = false;
  }

  setItemName(name: string, type: string) {
    if (name && this.ingredientName) {
      this.itemNameControl.setValue(name);
    }
    if(!this.updateBtnActive){
      this.addOption(type);
    }
  }

  addOption(type: string) {
    this.loadBtn = true;
    if (type === "package") {
      this.itemNameControl.reset();
    } else if (type === "inventory master") {
      this.processInventoryMaster(this.itemNameControl.value);
    }
    this.loadBtn = false;
  }

  updateItem(item: string) {
    this.loadBtn = true;
    this.processInventoryMaster(item);
    this.loadBtn = false;
  }

  processInventoryMaster(value: string) {
    const trimmedValue = value.trim();
    const invItem = this.sharedData.getDataForFillTheForm(trimmedValue, 'inventory master');
    if (invItem) {
      this.isUpdateActive = true;
      this.preFillInventoryForm(invItem);
    } else {
      this.generateCode('invCode');
      this.setInventoryPackageValues();
    }
    this.registrationForm.controls['itemName'].patchValue(this.removePromptFromOption(trimmedValue));
    this.itemNameControl.reset();
    this.isDuplicate = false;
  }

  setInventoryPackageValues(){
    if(this.itemNameControl.value == this.ingredientName){
      setTimeout(() => {
        if (this.inventoryMatch) {
          this.registrationForm.patchValue({
            ledger: this.inventoryMatch.ledger,
            category: this.inventoryMatch.category,
            subCategory: this.inventoryMatch.sub_category,
            hsnCode: this.inventoryMatch.hsn_sac || '-',
            inventoryUom: this.inventoryMatch.inventory_uom,
            weight: this.inventoryMatch.weight,
            yield: this.inventoryMatch.yield,
            taxRate: this.inventoryMatch.tax_rate,
            leadTime: this.inventoryMatch.lead_time_days,
            closingUOM: this.inventoryMatch.closing_uom,
            itemCode: this.code,
            itemName: this.ingredientName,
          });
        }
        if (this.packagingMatch) {
          const data = this.packagingMatch;
          this.dataSource.data = data;
        }
      }, 1500);
    }
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  enterPackage(){
    this.packagingForm.patchValue({
      category: this.registrationForm.value['category'],
      subCategory: this.registrationForm.value['subCategory'],
      inventoryCode: this.registrationForm.value['itemCode'],
      itemName: this.registrationForm.value['itemName'],
      unitUOM: this.registrationForm.value['inventoryUom'],
      discontinued: 'no',
      totalQtyOfPackage : this.packagingForm.value.quantityPerUnit
    });
  }

  optionSelectedPack(option: any) {

    this.packagingForm.patchValue({
      category: this.registrationForm.value['category'],
      subCategory: this.registrationForm.value['subCategory'],
      inventoryCode: this.registrationForm.value['itemCode'],
      itemName: this.registrationForm.value['itemName'],
      unitUOM: this.registrationForm.value['inventoryUom'],
      discontinued: 'no',
      totalQtyOfPackage : this.packagingForm.value.quantityPerUnit
    });

    if (option.value.indexOf(this.question) === 0) {
      this.addOptionPack();
    }else{
      let element = this.dataSource.data.find(item => item.PackageName === option)
      if(element){
        this.preFillPackageForm(element,this.addPackaging)
      }else{
        this.updatePackaging = false;
      }
    }
    this.packNameOptions = this.packagingForm.get('packageName').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.packNames)));
  }

  addOptionPack() {
    this.packagingForm.controls['packageName'].patchValue(this.removePromptFromOption(this.packagingForm.value.packageName));
  }

  optionSelectedCat(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOptionCat();
    } else {
      this.getSubCategories(this.registrationForm.value.category);
    }
    this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.categories)));
    this.registrationForm.get('ledger').setValue(this.registrationForm.value.category)
  }

  addOptionCat() {
    this.registrationForm.controls['category'].patchValue(this.removePromptFromOption(this.registrationForm.value.category));
    this.getSubCategories(this.registrationForm.value.category);
    this.registrationForm.get('ledger').setValue(this.registrationForm.value.category)
  }

  optionSelectedSubCat(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOptionSubCat();
    }
    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.subCategories)));
  }

  addOptionSubCat() {
    this.registrationForm.controls['subCategory'].patchValue(this.removePromptFromOption(this.registrationForm.value.subCategory));
  }


  optionSelectedPackage(option: any) {
    if (option.value.indexOf(this.question) === 0) {
      this.addOptionPackage();
    }
  }

  addOptionPackage() {
    this.packagingForm.controls['packageName'].patchValue(this.removePromptFromOption(this.packagingForm.value.packageName));
  }

  isClosingUomDisabled(): boolean {
    const inventoryUom = this.registrationForm.get('inventoryUom').value;
    return inventoryUom === 'NOS' || inventoryUom === 'MTR' || inventoryUom === 'LITRE';
  }

  isWeightDisabled(): boolean {
    const inventoryUom = this.registrationForm.get('inventoryUom').value;
    return inventoryUom == 'NOS';
  }

  closingUomOptions(): string[] {
    const inventoryUom = this.registrationForm.get('inventoryUom').value;
    if (inventoryUom === 'KG') {
      return ['KG', 'Open/KG'];
    }
    return [inventoryUom];
  }

  getSumOfPackage() {
    let sumOfValue = this.packagingForm.value.package * this.packagingForm.value.quantityPerUnit
    this.packagingForm.get('totalQtyOfPackage').setValue(sumOfValue);
  }

  checkChildItems(value) {
    if (value === 'no') {
      this.registrationForm.get('childItemCode').setValue([])
    }
  }

  checkUnitPackage() {
    if (this.packagingForm.value.package === null || this.packagingForm.value.package === undefined || this.packagingForm.value.package <= 0) {
      return true
    } else {
      return false
    }
  }

  sumForFinalRate(val) {
    if (val.target.value) {
      let sum = (this.registrationForm.value.rate / this.registrationForm.value.yield);
      let totalSum = this.notify.truncateAndFloor((sum + (this.registrationForm.value.taxRate / 100) * this.registrationForm.value.rate))
      this.registrationForm.get('finalRate').setValue(totalSum)
    }
  }

  setYieldValue(val) {
    this.registrationForm.get('yield').setValue(val.target.value)
  }

  selectValueForClosing(value: any) {
    const closingUOMControl = this.registrationForm.get('closingUOM');
    this.packagingForm.patchValue({ unitUOM: value });
    this.packagingForm.patchValue({ modified: "yes" });
    this.selectedUOM = value;
    switch (value) {
      case 'NOS':
        closingUOMControl.setValue('NOS');
        this.registrationForm.patchValue({ weight: 1 });
        break;
      case 'KG':
        closingUOMControl.setValue('KG');
        break;
      case 'MTR':
        closingUOMControl.setValue('MTR');
        break;
      case 'LITRE':
        closingUOMControl.setValue('LITRE');
        break;
      default:
        closingUOMControl.setValue(null);
        break;
    }
    this.cd.detectChanges();
  }

  focusOutFunction(formKey: string) {
    if (this.registrationForm.get(formKey)) {
      if (formKey === 'rate') {
        if (this.registrationForm.get(formKey).value === null) {
          this.registrationForm.get(formKey).setValue(1)
        }
      } else {
        if (this.registrationForm.get(formKey).value === null) {
          this.registrationForm.get(formKey).setValue(0)
        }
      }
    }
  }

  focusOutFunctionPackage(formKey: string) {
    if (this.packagingForm.get(formKey).value === null) {
      this.packagingForm.get(formKey).setValue(0)
    }
  }

  focusOutFunctionPackageName(formKey: string) {
    if (this.packagingForm.get(formKey).value && this.packagingForm.get(formKey).value.trim() == '') {
      this.packagingForm.get(formKey).setValue(null)
    }else if(! this.packagingForm.get(formKey).value){
      this.packagingForm.get(formKey).setValue(null)
    }
  }

  focusFunction(formKey: string) {
    if (this.registrationForm.get(formKey)) {
      if (this.notify.truncateAndFloor(this.registrationForm.get(formKey).value) === 0) {
        this.registrationForm.get(formKey).setValue(null)
      }
    } else {
      if (this.notify.truncateAndFloor(this.packagingForm.get(formKey).value) === 0) {
        this.packagingForm.get(formKey).setValue(null)
      }
    }
  }

  getLocationCall() {
    this.api.getLocations(this.user.tenantId).pipe(first()).subscribe({
      next: (res) => {
        if (res['result'] == 'success') {
          this.locationList = res['branches'];
          this.procuredAtBank = this.locationList.map(area => area.abbreviatedRestaurantId);
          this.procuredAtLocation.next(this.procuredAtBank.slice());
          this.procuredAtFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.Filter(this.procuredAtBank, this.procuredAtFilterCtrl, this.procuredAtLocation);
          });
          this.sharedData.getItemNames.pipe(first()).subscribe(obj => {
            if (this.dialogData.key == false) {
              this.isUpdateActive = true;
              this.preFillInventoryForm(this.dialogData.elements);
            } else if (this.dialogData.key == null) {
              this.dropDownData = this.dialogData.dropDownData;
              this.filteredData = [...this.dropDownData];
              this.cd.detectChanges();
            }
            this.getCategories();
          })
        }else {
          this.notify.snackBarShowError('Please add branches!');
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  getCategories() {
    this.sharedData.getInvCategories.pipe(first()).subscribe((obj) => {
      this.catAndsubCat = obj;
      let categoryData = Object.keys(obj).map(category => category.toUpperCase());
      let newCat = [...this.newCategory, ...categoryData];
      this.categories = [...new Set(newCat)];
      this.catBank = this.registrationForm.get('category').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.categories)));
    });
  }

  getSubCategories(val) {
    this.registrationForm.get('subCategory').setValue('');
    let data = this.baseData['inventory master'].filter(item => item.category === val);
    this.newSubCategory = data.map(subCat => subCat.subCategory)
    if (!(val in this.catAndsubCat)) {
      this.catAndsubCat[val] = []
    }
    let newSubCat = [...this.newSubCategory, ...this.catAndsubCat[val]]
    this.subCategories = [...new Set(newSubCat)];
    this.subCatBank = this.registrationForm.get('subCategory').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.subCategories)));
  }

  convertPackagingKeys() {
    const keyData = [
      ['InventoryCode', "inventoryCode"],
      ['ItemName', 'itemName'],
      ['PackageName', "packageName"],
      ['brand', 'brand'],
      ['Units/ package', 'package'],
      ['Quantity per unit', 'quantityPerUnit'],
      ['Total qty of package', 'totalQtyOfPackage'],
      ['UnitUOM', 'unitUOM'],
      ['Empty bottle weight', 'emptyBottleWeight'],
      ['ParLevel', 'parLevel'],
      ['Full bottle weight', 'fullBottleWeight'],
      ['PackagePrice', 'packagePrice'],
      // ['ExpiryDate', 'expiryDate'],
      ['Discontinued', 'discontinued'],
      ['TenantId', 'TenantId'],
      ['row_uuid', 'row_uuid']
    ];
    this.convertPackDataTypes(this.packagingForm.value);

    const temp = {};
    keyData.forEach((key) => {
      let value = this.packagingForm.value[key[1]];
      temp[key[0]] = value || '';
    });
    return temp
  }


  convertInventoryKeys() {
    const keyData = [
      ['itemName', 'itemName'],
      ['itemCode', 'itemCode'],
      ['category', 'category'],
      ['subCategory', 'subCategory'],
      ['classification', 'classification'],
      ['vendor', 'vendor'],
      ['inventoryUom', 'inventoryUom'],
      ['Inventory UOM', 'inventoryUom'],
      ['closingUOM', 'closingUOM'],
      ['procuredAt', 'procuredAt'],
      ['issuedTo', 'issuedTo'],
      ['taxRate', 'taxRate'],
      ['weight', 'weight'],
      ['yield', 'yield'],
      ['rate', 'rate'],
      ['finalRate', 'finalRate'],
      ['leadTime(days)', 'leadTime'],
      ['Discontinued', 'discontinued'],
      ['Stock Conversion', 'stockConversion'],
      ['Child ItemCode', 'childItemCode'],
      ['Ledger', 'ledger'],
      ['itemType', 'itemType'],
      ['modified', 'modified'],
      ['row_uuid', 'row_uuid'],
      ['HSN_SAC','hsnCode'],
    ];
    this.convertInvDataTypes(this.registrationForm.value);
    let temp = {};
    keyData.forEach((key) => {
      let value = this.registrationForm.value[key[1]];
      if (key[0] == "taxRate") {
        temp[key[0]] = value || 0;
      } else if (key[0] == "leadTime(days)"){
        temp[key[0]] = value || 0;
      }else if (key[0] == "Child ItemCode"){
        temp[key[0]] = value ? value.join(',') : null ;
      } else if (key[0] == "itemName"){
        temp[key[0]] = value.trim() ;
      } else {
        temp[key[0]] = value || '';
      }
    });
    return temp
  }

  convertPackDataTypes(jsonData){
    this.packagingForm.patchValue({
      itemName: jsonData.itemName,
      itemCode: jsonData.itemCode,
      category: jsonData.category,
      subCategory: jsonData.subCategory,
      classification: jsonData.classification,
      vendor: jsonData.vendor,
      inventoryUom: jsonData.inventoryUom,
      closingUOM: jsonData.closingUOM,
      procuredAt: jsonData.procuredAt,
      issuedTo: jsonData.issuedTo,
      taxRate: this.notify.truncateAndFloor(jsonData.taxRate),
      weight: this.notify.truncateAndFloor(jsonData.weight),
      yield: this.notify.truncateAndFloor(jsonData.yield),
      rate: this.notify.truncateAndFloor(jsonData.rate),
      finalRate: this.notify.truncateAndFloor(jsonData.finalRate),
      leadTime: this.notify.truncateAndFloor(jsonData.leadTime),
      discontinued: jsonData.discontinued,
      stockConversion: jsonData.stockConversion,
      ledger: jsonData.ledger,
      itemType: jsonData.itemType,
      modified: jsonData.modified,
      row_uuid: jsonData.row_uuid,
      recovery: jsonData.recovery,
      hsnCode: jsonData.hsnCode
    })
  }

  convertInvDataTypes(jsonData){
    this.registrationForm.patchValue({
      itemName: jsonData.itemName,
      itemCode: jsonData.itemCode,
      category: jsonData.category,
      subCategory: jsonData.subCategory,
      classification: jsonData.classification,
      vendor: jsonData.vendor,
      inventoryUom: jsonData.inventoryUom,
      closingUOM: jsonData.closingUOM,
      procuredAt: jsonData.procuredAt,
      issuedTo: jsonData.issuedTo,
      taxRate: this.notify.truncateAndFloor(jsonData.taxRate),
      weight: this.notify.truncateAndFloor(jsonData.weight),
      yield: this.notify.truncateAndFloor(jsonData.yield),
      rate: this.notify.truncateAndFloor(jsonData.rate),
      finalRate: this.notify.truncateAndFloor(jsonData.finalRate),
      leadTime: this.notify.truncateAndFloor(jsonData.leadTime),
      discontinued: jsonData.discontinued,
      stockConversion: jsonData.stockConversion,
      ledger: jsonData.ledger,
      itemType: jsonData.itemType,
      modified: jsonData.modified,
      row_uuid: jsonData.row_uuid,
      recovery: jsonData.recovery,
      hsnCode: jsonData.hsnCode
    });

  }

  openDeleteDialog(element){
      this.invData =  element
      this.dialogRef = this.dialog.open(this.deleteItemDialog, {
        width: '500px',
      });
      this.dialogRef.afterClosed().subscribe(result => {
      });
  }


  closeDialog(){
    this.dialogRef.close();
  }

  deleteFun(){
    if(this.invData.row_uuid){
      let temp = {}
      temp['packagingmasters'] = this.invData
      this.api.deleteData({
        'tenantId' :  this.user.tenantId,
        'userEmail' : this.user.email,
        'data' : temp,
        'type' : 'inventory'
      }).pipe(first()).subscribe({
        next: (res) => {
          if (res['success']) {
            let updatedData = this.dataSource.data.filter(item => item.PackageName !== this.invData['PackageName']);
            this.dataSource.data = updatedData
            this.packageNames = this.dataSource.data.map(item => item.PackageName)
            this.dialogRef.close();
            this.cd.detectChanges();
          }
        },
        error: (err) => { console.log(err) }
      });
    }else{
      let updatedData = this.dataSource.data.filter(item => item.PackageName !== this.invData['PackageName']);
      this.dataSource.data = updatedData
      this.packageNames = this.dataSource.data.map(item => item.PackageName)
      this.dialogRef.close();
      this.cd.detectChanges();
    }
  }

  checkPkgAvailability() {
    let activePkg = this.dataSource.data.filter((el) => el['Discontinued'] != 'yes') ;
    return activePkg.length <= 1 ? true : false
  }

  onToggleChange(val){
    this.checkWidth = 1210
  }

  // toggleChange(val){
  //   if (!val.checked){
  //     this.packagingForm.get('expiryDate')?.setValue('no');
  //   } else {
  //     this.packagingForm.get('expiryDate')?.setValue('yes');
  //   }
  // }

  onDelete(location: string, event: Event, select , group : any) {
    event.stopPropagation();
    this.selectedDropDown = select
    this.selectedData = location
    this.groupData = group
    this.dialogRef = this.dialog.open(this.discontinuedSelectDialog, {
      width: '500px',
    });
    this.dialogRef.afterClosed().subscribe(result => {
    });
  }

  onRestore(location: string, event: Event, select ,group){
    event.stopPropagation();
    if(select === 'procuredAt'){

      this.discontinuedProcuredAtData = this.discontinuedProcuredAtData.filter(item => item !== location);
      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item =>
        item.abbreviatedRestaurantId !== location
      );
      this.issuedToBank =  this.issuedToBank.map(item => {
        if (item.abbreviatedRestaurantId === location && item.hasOwnProperty('disabled')) {
          delete item.disabled;
        }
        return item;
      });

      this.workAreas.next(this.issuedToBank.slice());
      this.issuedToFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.FilterIssued(this.issuedToBank, this.issuedToFilterCtrl, this.workAreas);
      });

    }else if(select === 'issuedTo'){
      this.discontinuedIssuedToData.forEach(item => {
        if (item.abbreviatedRestaurantId === group.abbreviatedRestaurantId) {
          item.workAreas = item.workAreas.filter(workArea => workArea !== location);
        }
      });
      this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item => item.workAreas.length > 0);
      let issuedAreas = this.registrationForm.value.issuedTo ;
      issuedAreas.includes(location) ? undefined : issuedAreas.push(location)
      this.registrationForm.get('issuedTo').setValue(issuedAreas);
      // this.discontinuedIssuedToData = this.discontinuedIssuedToData.filter(item => item !== location);
    }
    this.cd.detectChanges();
  }

  discontinuedSelectData(){
    if(this.selectedDropDown === 'procuredAt'){
      this.discontinuedProcuredAtData.push(this.selectedData)
      const selectedWorkAreasArray = this.locationList.filter(branch => this.selectedData.includes(branch.abbreviatedRestaurantId))
      this.discontinuedIssuedToData.push(selectedWorkAreasArray[0])
      this.issuedToBank = this.issuedToBank.map(item => {
        if (item.abbreviatedRestaurantId === this.selectedData) {
          item.disabled = true;
        }
        return item;
      });
      this.workAreas.next(this.issuedToBank.slice());
      this.issuedToFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.FilterIssued(this.issuedToBank, this.issuedToFilterCtrl, this.workAreas);
      });
    }else if(this.selectedDropDown === 'issuedTo'){
      [this.groupData].forEach(item => {
        const matchingIssued = this.discontinuedIssuedToData.find(issuedItem => issuedItem.abbreviatedRestaurantId === item.abbreviatedRestaurantId);
        if (matchingIssued) {
            matchingIssued.workAreas.push(this.selectedData);
        } else {
            const newObject = {
                abbreviatedRestaurantId: item.abbreviatedRestaurantId,
                workAreas: [this.selectedData]
            };
            this.discontinuedIssuedToData.push(newObject);
        }
      });

      const newArray = [this.groupData].map(item => {
          return {
              abbreviatedRestaurantId: item.abbreviatedRestaurantId,
              workAreas: item.workAreas.filter(workArea => workArea === this.selectedData)
          };
      });
      this.discontinuedIssuedToData.push(...newArray);
      let issuedAreas = this.registrationForm.value.issuedTo ;
      let indexToRemove = issuedAreas.indexOf(this.selectedData);
      if (indexToRemove !== -1) {
        issuedAreas.splice(indexToRemove, 1);
      }
      this.registrationForm.get('issuedTo').setValue(issuedAreas);
    }
    this.closeDialog();
    this.cd.detectChanges();
  }

  isOptionDisabled(data: string , group): boolean {
    return this.discontinuedIssuedToData.some(item =>
      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&
      item.workAreas.includes(data)
    );
  }

  isCheckOptionDisabled(data: string , group): boolean {
    return this.discontinuedIssuedToData.some(item =>
      item.abbreviatedRestaurantId === group.abbreviatedRestaurantId &&
      item.workAreas.includes(data)
    );
  }

  setDiscontinuedDataInRolopos(){
    this.api.dicontinuedData({
      'tenantId' : this.user.tenantId,
      'userEmail' : this.user.email,
      'type' : 'inventoryLocations',
      'discontinuedLocations' : {
        'inventoryLocations' : {
          'issuedToDiscontinued' : this.discontinuedIssuedToData.length > 0 ? this.discontinuedIssuedToData : [],
          'procuredAtDiscontinued' : this.discontinuedProcuredAtData.length > 0 ? this.discontinuedProcuredAtData : []
        }
      }
    }).pipe(first()).subscribe({
      next: (res) => {
        if (res['success']) {
          this.cd.detectChanges();
        }
      },
      error: (err) => { console.log(err) }
    });
  }

  noStartingSpaceValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const isInvalid = control.value?.startsWith(' ');
      return isInvalid ? { invStartsWithSpace: true } : null;
    };
  }

}

