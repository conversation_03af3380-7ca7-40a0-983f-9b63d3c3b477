import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardCardComponent } from 'src/app/components/dashboard-card/dashboard-card.component';
import { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';
import { MatIconModule } from '@angular/material/icon';

import { Router } from '@angular/router';
import { MasterDataService } from 'src/app/services/master-data.service';
import { first } from 'rxjs';
import { AuthService } from 'src/app/services/auth.service';
import { InventoryService } from 'src/app/services/inventory.service';

@Component({
  selector: 'app-dashboard-home',
  standalone: true,
  imports: [CommonModule, DashboardCardComponent, BackgroundImageCardComponent, MatIconModule],
  templateUrl: './dashboard-home.component.html',
  styleUrls: ['./dashboard-home.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardHomeComponent {
  constructor(
    private masterDataService: MasterDataService,
    private router: Router,
    private auth: AuthService,
    private api: InventoryService
    ) {
      this.masterDataService.route$.pipe(first()).subscribe(tab => {
        if ((tab && tab === 'user') || (tab && tab === 'Roles') || (tab && tab === 'branches')){
          this.router.navigate(['/dashboard/user']) ;
        }else if ((tab && tab === 'menu master') || (tab && tab === 'Subrecipe Master') || (tab && tab === 'servingsize conversion')){
            this.router.navigate(['/dashboard/recipe']) ;
        }else if (tab && tab === 'party'){
          this.router.navigate(['/dashboard/party']) ;
        }else{
          // Instead of hardcoding inventory, navigate to first available menu item
          this.navigateToFirstAvailableRoute();
        }
      })
  }

  private navigateToFirstAvailableRoute() {
    const user = this.auth.getCurrentUser();

    if (!user) {
      this.router.navigate(['/signin']);
      return;
    }

    const ROUTES = [
      { path: '/dashboard/inventory', dbAccess: "inventory" },
      { path: '/dashboard/user', dbAccess: "user" },
      { path: '/dashboard/recipe', dbAccess: "recipe" },
      { path: '/dashboard/party', dbAccess: "party" },
      { path: '/dashboard/smart-dashboard', dbAccess: "dashboard" },
      { path: '/dashboard/account', dbAccess: "accountSetup" },
    ];

    if (user.tenantId === '100000') {
      // For admin users, go to account setup
      this.router.navigate(['/dashboard/account-setup']);
      return;
    }

    // Get user access and navigate to first available route
    this.api.getUIAccess(user.tenantId).subscribe({
      next: (res) => {
        if (res['success']) {
          const access = res['access'];

          // Find first route user has access to
          for (const route of ROUTES) {
            if (access.hasOwnProperty(route.dbAccess)) {
              const hasAccess = access[route.dbAccess]['status'] === true &&
                               access[route.dbAccess]['access']
                                 .map((role: string) => role.toLowerCase())
                                 .includes(user.role.toLowerCase());

              if (hasAccess) {
                this.router.navigate([route.path]);
                return;
              }
            }
          }

          // If no access found, user will be handled by dashboard component's access check
          this.router.navigate(['/dashboard/inventory']); // Fallback
        } else {
          // Fallback to inventory if API fails
          this.router.navigate(['/dashboard/inventory']);
        }
      },
      error: (err) => {
        console.error('Error checking access:', err);
        // Fallback to inventory if API fails
        this.router.navigate(['/dashboard/inventory']);
      }
    });
  }
}
