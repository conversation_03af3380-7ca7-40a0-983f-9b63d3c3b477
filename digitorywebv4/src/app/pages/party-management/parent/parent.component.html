<mat-card>
  <!-- Always render the tab group to maintain tab selection state -->
  <mat-tab-group [(selectedIndex)]="selectedTabIndex" (selectedTabChange)="tabClick($event)" class="m-1">
    <mat-tab *ngFor="let tab of childTabs">
      <ng-template mat-tab-label>
        {{ tab.label }}
        <!-- <span *ngIf="tab.label != 'Draft'">{{ this.getPartyCount(tab.label) }}</span> -->
      </ng-template>

      <!-- Show content when data is ready -->
      <div *ngIf="isDataReady">
        <app-http-table [page]="tab.page" [data]="[this.baseData, tab.label]" *ngIf="selectedTabIndex == tab.index"></app-http-table>
      </div>

      <!-- Show loading state when data is not ready -->
      <div *ngIf="!isDataReady" class="my-3">
        <ngx-skeleton-loader count="50" animation="pulse" [theme]="{
          'border-radius': '4px',
          'height': '30px',
          'margin-bottom': '8px',
          'width': '19%',
          'margin-right': '1%',
          'display': 'inline-block',
          'opacity': '0.85'
        }"></ngx-skeleton-loader>
      </div>
    </mat-tab>
  </mat-tab-group>
</mat-card>
