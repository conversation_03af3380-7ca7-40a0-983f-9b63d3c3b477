.custom-timepicker-overlay {
  position: absolute !important;
  top: 0 !important;
  z-index: 1000 !important;
  width: calc(100% + 16px) !important;
  margin-left: -8px !important;
}

::ng-deep .timepicker-overlay {
  z-index: 1000 !important;
}

::ng-deep .timepicker-dial__container {
  justify-content: center !important;
}

::ng-deep .timepicker-dial__hint[_ngcontent-ng-c3167402989] {
  display: none !important;
}

.time-picker-icon {
  top: 0% !important;
}

// mat-icon.mat-icon.notranslate.material-icons.mat-ligature-font.mat-icon-no-color {
//   margin-right: 2px !important;
// }

.mdc-data-table__header-cell {
  padding: 8px 12px !important;
}

::ng-deep span.mat-expansion-indicator {
  display: none !important;
}

.smallDialogInput {
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }
}

.restIcons{
  margin-right: 10px !important;
}

//  Feature DON'T REMOVE
.themes {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 30px;
  padding-right: 32px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.scroll-container {
  display: flex;
  overflow-x: auto;
  gap: 8px;
  scroll-behavior: smooth;
  padding: 8px;
  overflow: hidden;
}

.themeCard {
  padding: 15px;
  min-width: 200px;
  height: 70px;
  cursor: pointer;
}

.scroll-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}


.scroll-btn.left {
  position: absolute;
  left: 0;
  z-index: 1;
}

.scroll-btn.right {
  position: absolute;
  right: 0;
  z-index: 1;
}
//  Feature DON'T REMOVE

.customHeightfield {
  margin-bottom: 10px !important;
}

.date-input-container {
  position: relative;
}

.with-icon {
  padding-right: 30px;
}

.date-picker-icon {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.time-input-container {
  position: relative;
}

.with-icon {
  padding-right: 30px;
}

.time-picker-icon {
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin: 5px;
  gap: 10px;
}

.sticky-header {
  position: sticky;
  top: 0; /* Adjust based on the desired offset */
  z-index: 10; /* Ensure it stays above other content */
  // background-color: white; /* Prevent transparency issues */
  // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1); /* Optional shadow for visibility */
  // padding: 10px; /* Adjust padding as necessary */
  padding-bottom: 10px;
  background-color: white;
  padding-top: 2px;
}


.topTitleName {
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  font-weight: bold;
}

.topTitleName .mat-icon {
  margin-right: 8px;
  vertical-align: middle;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  gap: 10px;
}

.button-group button {
  min-width: 100px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-group .mat-icon {
  margin-right: 5px;
}

.topNameClass {
  display: flex;
  align-items: center;
  gap: 10px;
}

.hi-badge {
  // background: linear-gradient(45deg, #e7cd0d, #d1c647);
  background: linear-gradient(45deg, rgba(0, 101, 129, 0.8), #2a9ac5);
  color: rgb(255, 255, 255);
  font-size: 0.8rem;
  padding: 3px 10px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  line-height: 1.55;
}

// .extraSuppliesClass {
//   // table {
//   //   border: none;
//   // }td 
//   th
//   {
//     // border: none;
//     background-color: #e5e5e5
//   }
// }
.table{
  th{
    background-color: #e5e5e5;
  }

  .subHeading{
    background-color: rgb(0 101 129 / 7%);
  }
}


.customSelect {
  .form-select {
    padding: 0.375rem 0.75rem 0.375rem 0.75rem !important;
    background-image: none;
  }
}

.mdc-data-table__cell,
.mdc-data-table__header-cell {
  padding: 10px !important;
  min-width: 100px !important
}

.partTable {
  .custom-tableSmallWidth {
    min-width: 50px !important
  }

  .custom-tableLargeWidth {
    min-width: 200px !important
  }
}

.detialsHeading {
  font-size: medium;
  font-weight: bold;
  color: grey;
  margin: 15px 0px;
}

.partyClosedText {
  font-size: 10px;
  margin-left: 10px;
  color: red;
}

.groupTotalClass {
  font-size: large;
  font-weight: bold;
}

.partyDiscountClass{
  width: 200px !important;
  position: relative;
  // .input{
  //   padding-left: 20px;
  // }
  .radio{
    position: absolute;
    top: 20px;
    right: -1px;
  }
  .perIcon{
    position: absolute;
    top: 13px;
    left: 6px;
    font-size: 13px;
  }
  .amountIcon{
    position: absolute;
    top: 11px;
    left: 6px;
  }
}

.itemDiscountClass{
  position: relative;
  // .input{
  //   padding-left: 20px !important;
  // }
  .perIcon{
    position: absolute;
    top: 32px;
    left: 6px;
    font-size: 13px;
  }
  .amountIcon{
    position: absolute;
    top: 30px;
    left: 6px;
  }
  .radio {
    position: absolute;
    top: 20px;
    right: 0px;
  }
}

.groupDiscountClass{
  // position: relative;
  // .input{
  //   padding-left: 20px !important;
  // }
  // .perIcon{
  //   position: absolute;
  //   top: 12px;
  //   left: 6px;
  //   font-size: 13px;
  // }
  // .amountIcon{
  //   position: absolute;
  //   top: 10px;
  //   left: 6px;
  // }
  // .radio {
  //   position: absolute;
  //   top: 0px;
  //   right: -5px;
  // }
}

.infoClass{
  position: relative;
  .infoIcon{
    position: absolute;
    right: 0px;
    top: 28px;
    font-size: 19px;
  }
}

.tableInfoIcon{
  margin-left: 5px;
  margin-top: 3px;
  font-size: 17px;
}

::ng-deep .mat-select-search-clear {
  display: none;
}

.sessionInput {
  height: 40px;
  width: 80%;
  padding: 5px 10px;
}

.addSessionIcon {
  float: right;
  margin-top: 7px;
  cursor: pointer;
}

.disabled-icon {
  color: #c7c7c7;
  pointer-events: none;
  cursor: not-allowed;
}

.groupInput {
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

.groupInputs{
  display: flex; 
  flex-wrap: wrap; 
  gap: 17px;
  align-items: center;
}

@media screen and (max-width: 600px) {
  .itemNameClass{
    width: 100% !important;
  }

  .servingSizeClass{
    width: 100% !important;
  }

  .qtyClass{
    width: 100% !important;
  }

  .copClass{
    width: 100% !important;
  }

  .sellingPriceClass{
    width: 100% !important;
  }

  .costClass{
    width: 100% !important;
  }

  .discountClass{
    width: 100% !important;
  }

  .tolPriceClass{
    width: 100% !important;
  }

}

.form-group{
  .itemNameClass{
    width: 210px !important;
  }

  .servingSizeClass{
    width: 130px !important;
  }

  .qtyClass{
    width: 100px !important;
  }

  .copClass{
    width: 100px !important;
  }

  .sellingPriceClass{
    width: 100px !important;
  }

  .costClass{
    width: 100px !important;
  }

  .discountClass{
    width: 130px !important;
  }

  .tolPriceClass{
    width: 100px !important;
  }
}

mat-button-toggle-group {
  // margin-right: 4px !important;
  ::ng-deep .mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
    padding: 0 12px !important;
    line-height: 36px !important;
  }
}

.groupDataLabel{
  display: inline-block;
  width: 250px;
  // text-align: right;
  font-weight: bold;
}

th.mat-mdc-header-cell.mdc-data-table__header-cell{
  z-index: 0 !important;
}

.partyTitles{
  // color: rgba(0, 0, 0, 0.6);
  color:rgba(0, 101, 129, 0.8);
  font-size: larger;
  font-weight: bolder;
  // background-color: #e5e5e5;
}

.float-right{
  float: right;
}

.groupDataValue{
  width: 180px !important;
  display: inline-block;
}

.disInput{
  display: inline !important;
  width: 150px;
  padding-top: 5px;
  padding-bottom: 7px;
}
