<app-background-image-card [backgroundSrc]="">
  <div class="headingBtns">
    <button (click)="resetData()" mat-raised-button color="warn" class="reset my-1" matTooltip="reset" [disabled]="!isDataReady || this.entireData.sessionId === 0">
      <mat-icon> clear_all</mat-icon>Reset</button>
      <button (click)="openBottomSheet()" mat-raised-button color="warn" class="sync my-1" matTooltip="sync option" [disabled]="!isDataReady">
        <mat-icon>sync</mat-icon>Sync Options
      </button>
  </div>
</app-background-image-card>

<mat-card>
  <mat-tab-group [(selectedIndex)]="selectedTabIndex" (selectedTabChange)="tabClick($event)">
    <mat-tab *ngFor="let tab of tabs">
      <ng-template mat-tab-label>
        {{tab.label}}
      </ng-template>
      <div *ngIf="isDataReady">
        <app-http-table [page]="tab.page" [data]="baseData[tab.page]" *ngIf="selectedTabIndex == tab.index"></app-http-table>
      </div>
      <div *ngIf="!isDataReady" class="my-3">
        <ngx-skeleton-loader count="50" animation="pulse" [theme]="{
          'border-radius': '4px',
          'height': '30px',
          'margin-bottom': '8px',
          'width': '19%',
          'margin-right': '1%',
          'display': 'inline-block',
          'opacity': '0.85'
        }"></ngx-skeleton-loader>
      </div>
    </mat-tab>
  </mat-tab-group>
</mat-card>

<ng-template #openResetDialog>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeResetDialog()">close</mat-icon>
  </div>
  <div class="registration-form m-1 py-2 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Reset Session</span>
    </div>
    <div class="m-3 infoText">
      Would you like to reset the session?
    </div>
    <div class="text-end m-2">
      <button (click)="resetUI()" mat-raised-button color="accent" matTooltip="Update" class="m-1">
        Yes</button>
        <button (click)="closeResetDialog()" mat-raised-button matTooltip="close" class="m-1">
        No</button>
    </div>
  </div>
</ng-template>