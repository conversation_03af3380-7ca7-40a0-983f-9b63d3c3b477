<div class="closeBtn" *ngIf="isDuplicate == true">
  <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
</div>

<div class="registration-form py-2 px-3">
  <div class="mt-3 smallDialog" *ngIf="isDuplicate == true">
    <div class="col-md-12">
      <div class="text-center my-2 p-2 bottomTitles">
        <span>Branch Form</span>
      </div>
      <mat-form-field appearance="outline">
        <mat-label>Search Branch Location</mat-label>
        <input matInput placeholder="Branch Location" aria-label="Branch Location" [matAutocomplete]="auto1"
          [formControl]="searchControl" (keyup.enter)="addOption('branches')">
        <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected('branches', $event.option)">
          <mat-option *ngFor="let item of searchOptions | async" [value]="item">
            <span>{{ item | uppercase }}</span>
          </mat-option>
        </mat-autocomplete>
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>
      <div class="text-end">
        <button (click)="addOption('branches')" mat-raised-button color="accent" [disabled]="searchControl.invalid">
          <div *ngIf="loadBtn" class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <mat-icon *ngIf="!updateBtnActive">library_add</mat-icon>
          <mat-icon *ngIf="updateBtnActive">update</mat-icon>
          <span *ngIf="updateBtnActive">Update</span>
          <span *ngIf="!updateBtnActive">Add</span>
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="isDuplicate == false">
    <div class="mb-2 topCreateAndUpdateBtn" style="float: right; padding-top: 0.5rem;">
      <button class="stepperBtns" (click)="submitBranch()" style="margin-right: 5px;" mat-raised-button *ngIf="!showUpdateBtn"
       color="accent" matTooltip="create" [disabled]="isCreateButtonDisabled">
        <mat-icon>add_circle</mat-icon>Create
      </button>
      <button mat-raised-button *ngIf="showUpdateBtn" color="accent" style="margin-right: 5px;" (click)="updateBranch()" matTooltip="update"
      [disabled]="loadBranchBtn || isUpdateButtonDisabled">
        <div *ngIf="isLoad" class="spinner-border" role="status">
          <span class="sr-only">Loading...</span>
        </div>
        Update
      </button>
      <button *ngIf="isDuplicate == false" mat-raised-button color="warn" style="margin-right: 5px;" (click)="close()" matTooltip="Close">
        <mat-icon>close</mat-icon>
        Close
      </button>
    </div>

    <div class="my-2 p-3 bottomTitles"> Branch Form </div>

    <div class="my-2">
      <form [formGroup]="branchForm">
        <div class="row">
          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Tenant</mat-label>
              <input formControlName="tenantName" matInput placeholder="Tenant"
                oninput="this.value = this.value.toUpperCase()">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Branch Name</mat-label>
              <input formControlName="branchName" matInput placeholder="Branch Name">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Branch Location</mat-label>
              <input formControlName="branchLocation" matInput placeholder="Branch Location">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Branch ID</mat-label>
              <input formControlName="restaurantId" matInput placeholder="Branch ID">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label> Branch Type </mat-label>
              <mat-select formControlName="branchType" matInput placeholder="Branch Type">
                <mat-option *ngFor="let option of ['outlet', 'central store', 'central kitchen']" [value]="option">
                  {{option | uppercase}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>


          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Add WorkArea</mat-label>
              <input matInput placeholder="WorkAreas" aria-label="WorkAreas" [matAutocomplete]="auto1"
                formControlName="workArea" oninput="this.value = this.value.toUpperCase()">
              <mat-icon matSuffix (click)="instantAdd()" style="cursor: pointer;">add </mat-icon>
              <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelectedWorkArea( $event.option)">
                <mat-option *ngFor="let area of workAreaBank | async" [value]="area">
                  <span>{{ area | uppercase }}</span>
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Abbreviated ID</mat-label>
              <input formControlName="abbreviatedRestaurantId" matInput placeholder="Abbreviated ID"
                oninput="this.value = this.value.toUpperCase()">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Closing Time (24 Hour Format)</mat-label>
              <input type="time" formControlName="restaurantClosingTime" matInput
                placeholder="Closing Time (24 Hour Format)">
              <mat-icon matSuffix>access_time</mat-icon>
            </mat-form-field>
          </div>


          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Store ID (POS)</mat-label>
              <input formControlName="storeId" matInput placeholder="Store ID (POS)">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Default Floor ID</mat-label>
              <input formControlName="defaultSalesFloorNo" matInput placeholder="Default Floor ID">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Is store available ?</mat-label>
              <mat-select formControlName="storeAvailable" matInput placeholder="Is store available ?">
                <mat-option *ngFor="let option of ['Yes', 'No']" [value]="option">
                  {{option}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Billing Address</mat-label>
              <textarea formControlName="billTo" matInput placeholder="Billing Address">
              </textarea>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>Shipping Address</mat-label>
              <textarea formControlName="shipTo" matInput placeholder="Shipping Address">
              </textarea>
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>PAN Number</mat-label>
              <input formControlName="panNo" matInput placeholder="PAN Number">
            </mat-form-field>
          </div>

          <div class="col-md-6">
            <mat-form-field appearance="outline">
              <mat-label>GST Number</mat-label>
              <input formControlName="gstNo" matInput placeholder="GST Number">
            </mat-form-field>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>