import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { MatCardModule } from '@angular/material/card';
import { AbstractControl, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, ValidatorFn, Validators } from '@angular/forms';
import { InventoryService } from 'src/app/services/inventory.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ShareDataService } from 'src/app/services/share-data.service';
import { AuthService } from 'src/app/services/auth.service';
import { NotificationService } from 'src/app/services/notification.service';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSliderModule } from '@angular/material/slider';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import { AutocompleteComponent } from 'src/app/components/autocomplete/autocomplete.component';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatStepperModule } from '@angular/material/stepper';
import { Observable, ReplaySubject, Subject, first, map, startWith, takeUntil } from 'rxjs';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MasterDataService } from 'src/app/services/master-data.service';
import { Router } from '@angular/router';
@Component({
  selector: 'app-user',
  standalone: true,
  imports: [
    FormsModule,
    MatDialogModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatInputModule,
    MatSliderModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatRadioModule,
    MatAutocompleteModule,
    MatDividerModule,
    AutocompleteComponent,
    MatTableModule,
    NgxMatSelectSearchModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatToolbarModule,
    MatSnackBarModule,
    NgxSkeletonLoaderModule,
    MatStepperModule
  ],
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserComponent {
  userForm!: FormGroup;
  user: any;
  emailControl = new FormControl('', [Validators.required, Validators.email]);
  isUpdateActive: boolean = false;
  searchOptions: Observable<string[]>;
  question = 'Would you like to add "';
  isDuplicate: boolean;
  isReadOnly: boolean = true;
  dataSource = new MatTableDataSource<any>([]);
  baseData: any;
  updateBtnActive: boolean = false;
  loadBtn: boolean = false;
  showUpdateBtn: boolean = false;
  isLoad: boolean = false;
  public branchIds = [];
  public roles =[];
  public workAreaBank: any[] = [];
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workAreas: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public branchIdBank: any[] = [];
  public branchIdFilterCtrl: FormControl = new FormControl();
  public branchId: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  public allLocations: any;
  userFormBtn : boolean = true;
  isCreateButtonDisabled = false;
  isUpdateButtonDisabled = false;
  showError: boolean;
  showWorkAreaError: boolean;
  constructor(
    private fb: FormBuilder,
    private api: InventoryService,
    public dialog: MatDialog,
    private router: Router,
    private masterDataService: MasterDataService,
    private sharedData: ShareDataService,
    private auth: AuthService,
    private notify: NotificationService,
    private cd: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public dialogData: any,
  ) {
    this.user = this.auth.getCurrentUser();
    this.isDuplicate = this.dialogData.key;
    this.baseData = this.sharedData.getBaseData().value;

    this.sharedData.getUserItem.subscribe(obj =>{
      if(obj.users){
        let names = obj.users.map(obj => obj.email)
        this.searchOptions = this.emailControl.valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), names)));
      }
    });

    this.userForm = this.fb.group({
      firstName: new FormControl<string>('', Validators.required),
      lastName: new FormControl<string>('', Validators.required),
      tenantId: new FormControl<string>(''),
      branchId: new FormControl<string[]>(null, Validators.required),
      email: new FormControl<string>('', Validators.required),
      password: new FormControl<string>('', Validators.required),
      roles: new FormControl<string>('', Validators.required),
      mobile: new FormControl<number>(null),
      multiBranchUser: new FormControl<string>(''),
      workAreas: new FormControl<string[]>(null, Validators.required),
      modified: new FormControl<string>(''),
      row_uuid: new FormControl<string>(''),
      Discontinued: new FormControl<string>('no'),
    }) as FormGroup;
  }

  ngOnInit(){
    this.getLocationCall().then(() => {
      this.sharedData.getItemNames.pipe(first()).subscribe(obj => {
        if (this.dialogData.key == false) {
          this.isUpdateActive = true;            
          this.setValuesForForm(this.dialogData.elements);
        }
      })
      this.sharedData.getRole.pipe(first()).subscribe(obj =>{
        this.roles = obj;
      })
    }).catch((error) => {
      console.error('Error fetching locations:', error);
    });
  }

  close() {
    this.dataSource.data = [];
    this.masterDataService.setNavigation('user');
    this.router.navigate(['/dashboard/home']);
    this.dialog.closeAll();
  }

  optionSelected(type: string, option: any) {
    let invItem = this.sharedData.getDataForFillTheForm(option.value, 'users')
    if (invItem) {
      this.updateBtnActive = true;
    } else {
      this.updateBtnActive = false;
    }
    if (option.value.indexOf(this.question) === 0) {
      this.addOption(type);
    }
  }

  addOption(type: string) {
    this.loadBtn = true;
    if (type == "users") {
      let invItem = this.sharedData.getDataForFillTheForm(this.emailControl.value, 'users')
      if (invItem) {
        this.isUpdateActive = true;
        this.setValuesForForm(invItem);
      }
      this.userForm.controls['email'].patchValue(this.removePromptFromOption(this.emailControl.value));
      this.emailControl.reset();
      this.isDuplicate = false
    }
    this.dataSource.data = [];
    this.loadBtn = false;
  }


  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }


  getLocationCall() {
    return new Promise((resolve, reject) => {
      this.api.getLocations(this.user.tenantId).pipe(first()).subscribe({
        next: (res) => {
          if (res['result'] === 'success') {
            this.allLocations = res['branches'];
            this.branchIds = [...new Set(this.allLocations.map(item => item.restaurantIdOld))];
            this.branchIdBank = this.branchIds;
            this.branchId.next(this.branchIdBank.slice());
            this.branchIdFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
              this.branchIdsFilter(this.branchIdBank, this.branchIdFilterCtrl, this.branchId);
            });
            resolve("success");
          } else {
            reject(new Error('Failed to fetch locations'));
          }
        },
        error: (err) => { reject(err); }
      });
    });
  }
  
  setValuesForForm(form){    
    this.showUpdateBtn = true;
    let data = []    
    if(form.branchId){      
      this.getWorkAreas(form.branchId);
    }
    let workArea
    if (typeof form.workAreas === "string") {
      workArea = form.workAreas.split(',')
    } else {
      workArea = []
    }
    let branchId =  Array.isArray(form.branchId) ? form.branchId :  (form.branchId ? form.branchId.split(',') : [])
    this.userForm.setValue({
      firstName: form.firstName ? form.firstName : '',
      lastName: form.lastName ? form.lastName : '',
      tenantId: form.tenantId,
      branchId: branchId,
      email: form.email ? form.email : '',
      password: form.password ? form.password : '',
      roles: form.roles ? form.roles : '',
      // mobile: form.mobile ? form.mobile.split('.')[0] : '',
      mobile: form.mobile && String(form.mobile).includes(',') ? form.mobile.split(',')[0] : form.mobile ? form.mobile : '',
      multiBranchUser: form.multiBranchUser,
      workAreas: workArea,
      modified: form.modified,
      row_uuid: form.row_uuid,
      Discontinued: ['no','NO' ,'No', 'N', null,''].includes(form['Discontinued']) ? 'no' : 'yes'
    });
    this.userFormBtn = false;
  }

  _filter(value: string, input: string[]): string[] {
    const filteredArray = input.filter(value => value !== null && value !== undefined);
    let filterValue = value.toLowerCase();
    let filtered = filteredArray.filter(option => option.toLowerCase().includes(filterValue));
    if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
    return filtered
  }


  convertUserKeys() {
    const keyData = [
      ['tenantId' , 'tenantId' ], 
      ['branchId' , 'branchId' ], 
      ['workAreas' , 'workAreas' ], 
      ['multiBranchUser' , 'multiBranchUser' ],
      ['email' , 'email' ], 
      ['password' , 'password' ], 
      ['roles' , 'roles' ], 
      ['mobile' , 'mobile' ],
      ['firstName' , 'firstName' ], 
      ['lastName' , 'lastName' ],  
      ['Discontinued' , 'Discontinued' ],
      ['modified' , 'modified' ], 
      ['row_uuid' , 'row_uuid' ],
    ];
    this.convertUserDataType(this.userForm.value);
    const temp = {};
    keyData.forEach((key) => {
      let value = this.userForm.value[key[1]];
      temp[key[0]] = value || '';
    });
    return temp
  }

  convertUserDataType(jsonData){
    this.userForm.setValue({
      firstName: jsonData.firstName,
      lastName: jsonData.lastName,
      tenantId: jsonData.tenantId,
      branchId: jsonData.branchId,
      email: jsonData.email,
      password: jsonData.password,
      roles: jsonData.roles,
      mobile: this.notify.truncateAndFloor(jsonData.mobile),
      multiBranchUser: jsonData.multiBranchUser,
      workAreas: jsonData.workAreas,
      modified: jsonData.modified,
      row_uuid: jsonData.row_uuid,
      Discontinued: jsonData.Discontinued
    });
  }

  submitUser(){
    this.isCreateButtonDisabled = true;
    this.isLoad = true;
    this.showWorkAreaError = false;
    if(this.userForm.invalid) {
      this.userForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isLoad = false;
      this.isCreateButtonDisabled = false;
      this.cd.detectChanges();
    }else {
      this.checkWorkArea();
      if(this.showWorkAreaError){
        this.userForm.markAllAsTouched();
        this.notify.snackBarShowError('Please fill out all required fields')
        this.isLoad = false;
        this.isUpdateButtonDisabled = false;
        this.cd.detectChanges();
      }else{
        this.userForm.get('multiBranchUser').setValue(this.userForm.get('branchId').value.length === 1 ? 'FALSE' : 'TRUE');
        let updatedData = this.convertUserKeys();
        updatedData['tenantId'] = updatedData['tenantId'] ? updatedData['tenantId'] : this.user.tenantId;
        updatedData['modified'] = "yes";
        updatedData['row_uuid'] = "";
        updatedData['branchId'] = updatedData['branchId'].join(',')
        updatedData['workAreas'] = updatedData['workAreas'].join(',')
        if (Object.keys(this.baseData).length > 0) {
          let temp = {}
          temp['users'] = this.baseData['users'];
          temp['users'].unshift(updatedData);
          temp['users'] = temp['users'].filter(item => item.modified === "yes");
          this.api.updateData({
            'tenantId' :  this.user.tenantId,
            'userEmail' : this.user.email,
            'data' : temp,
            'type' : 'user'
          }).pipe(first()).subscribe({
            next: (res) => {
              if (res['success']) {
                this.isLoad = false;
                this.notify.snackBarShowSuccess('User created successfully');
                this.close();
              }
            },
            error: (err) => {
              console.log(err);
            }
          });
        } else {
          this.isCreateButtonDisabled = false;
          this.isLoad = false;
        }
      }
    }
  }

  updateUser(){
    this.isUpdateButtonDisabled = true;
    this.showWorkAreaError = false;
    this.isLoad = true;
    if(this.userForm.invalid) {
      this.userForm.markAllAsTouched();
      this.notify.snackBarShowError('Please fill out all required fields')
      this.isLoad = false;
      this.isUpdateButtonDisabled = false;
      this.cd.detectChanges();
    }else {
      this.checkWorkArea();
      if(this.showWorkAreaError){
        this.userForm.markAllAsTouched();
        this.notify.snackBarShowError('Please fill out all required fields')
        this.isLoad = false;
        this.isUpdateButtonDisabled = false;
        this.cd.detectChanges();
      }else{
        this.userForm.get('multiBranchUser').setValue(this.userForm.get('branchId').value.length === 1 ? 'FALSE' : 'TRUE');
        let updatedData = this.convertUserKeys();
        updatedData['tenantId'] = updatedData['tenantId'] ? updatedData['tenantId'] : this.user.tenantId;
        updatedData['modified'] = "yes";
        updatedData['branchId'] = updatedData['branchId'].join(',')
        updatedData['workAreas'] = updatedData['workAreas'].join(',')
  
        if (Object.keys(this.baseData).length > 0) {
          let temp = {}
          temp['users'] = this.baseData['users']
          let required = temp['users'].find((el) => el.email == updatedData['email'])
          let index = temp['users'].indexOf(required)
          temp['users'][index] = updatedData;
          const uniqueData = temp['users'].filter((user, index, self) =>
          index === self.findIndex((t) => (
            t.email === user.email 
          ))
        );
        temp['users'] = uniqueData;
        temp['users'] = temp['users'].filter(item => item.modified === "yes");
          this.api.updateData({
            'tenantId' :  this.user.tenantId,
            'userEmail' : this.user.email,
            'data' : temp,
            'type' : 'user'
          }).pipe(first()).subscribe({
            next: (res) => {
              if (res['success']) {
                this.isLoad = false;
                this.cd.detectChanges();
                this.notify.snackBarShowSuccess('Updated successfully');
              }
            },
            error: (err) => { console.log(err) }
          });
        } else {
          this.isUpdateButtonDisabled = false;
          this.isLoad = false;
        }
      }
    }
  }


  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  getWorkAreas(value) {
    const selectedWorkAreasArray = this.allLocations.filter(branch => value.includes(branch.restaurantIdOld));
    this.workAreaBank = selectedWorkAreasArray;
    this.workAreas.next(this.workAreaBank.slice());
    this.workAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.Filter(this.workAreaBank, this.workAreaFilterCtrl, this.workAreas);
    });
}

  Filter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    const filteredBank = bank.map(item => {
      const filteredWorkAreas = item.workAreas.filter(workArea => workArea.toLowerCase().indexOf(search) > -1);
      return { ...item, workAreas: filteredWorkAreas };
    });
    data.next(filteredBank);
  }

  protected branchIdsFilter(bank, form, data) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.toLowerCase().indexOf(search) > -1)
    );
  }

  toggleSelectAll() {
    const control = this.userForm.controls['workAreas'];
    let data = [...this.workAreaBank.map(location => location.workAreas)];
    const flattenedArray = [].concat(...data);
    if (control.value.length - 1 === flattenedArray.length) {
      control.setValue([]);
    } else {
      control.setValue(flattenedArray);
    }
  }

  branchIdsToggleSelectAll(){
    const control = this.userForm.controls['branchId'];
    if (control.value.length - 1 === this.branchIdBank.length) {
      control.setValue([]);
      this.getWorkAreas(this.userForm.value.branchId);
    } else {
      control.setValue(this.branchIdBank);
      this.getWorkAreas(this.userForm.value.branchId);
    }
  }

  
  
  checkWorkArea(){
    this.userForm.value.branchId.forEach(restId => {
      const restaurant = this.allLocations.find(r => r.restaurantIdOld === restId);
      if (restaurant) {
        const hasMatchingWorkArea = this.userForm.value.workAreas.some(workArea => restaurant.workAreas.includes(workArea));
        if (!hasMatchingWorkArea) {
          // this.userForm.get('workAreas').setErrors({ 'checkWorkAreas': true });
          this.showWorkAreaError = true;
        }
      }
    });
  }


}
