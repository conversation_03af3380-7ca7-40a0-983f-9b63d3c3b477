<!-- <div class="closeBtn" *ngIf="isDuplicate == true">
  <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
</div> -->

<div class="registration-form m-3 py-2 px-3">
  <div>
    <div class="mb-2 topCreateAndUpdateBtn" style="float: right; padding-top: 0.5rem;">
      <button class="stepperBtns" (click)="submitRole()" style="margin-right: 5px;" mat-raised-button *ngIf="!showUpdateBtn" 
      color="accent" matTooltip="create" [disabled]="isCreateButtonDisabled">
        <mat-icon>add_circle</mat-icon>Create
      </button>
      <button mat-raised-button *ngIf="showUpdateBtn" color="accent" style="margin-right: 5px;" (click)="updateRole()" matTooltip="update"
      [disabled]="roleFromBtn || isUpdateButtonDisabled">
        <div *ngIf="isLoad" class="spinner-border" role="status">
          <span class="sr-only">Loading...</span>
        </div><mat-icon *ngIf="!isLoad">update</mat-icon>Update
      </button>
      <button mat-raised-button color="warn" style="margin-right: 5px;" (click)="close()" matTooltip="Close">
        <mat-icon>close</mat-icon>
        Close
      </button>
    </div>

    <div class="my-2 p-3 bottomTitles"> Role Form </div>

    <div class="my-2">
      <form [formGroup]="roleForm">
        <div class="row">
          <div class="col-md-12">
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': showUpdateBtn || this.dialogData.key == null}">
              <mat-label>Role Name</mat-label>
              <input formControlName="role" matInput placeholder="Role Name" (keyup)="checkRole($event);"
              oninput="this.value = this.value.toUpperCase()" [readonly]="showUpdateBtn || this.dialogData.key == null">
            </mat-form-field>
            <mat-error class="formError" *ngIf="roleForm.get('role').hasError('roleExists')">
              * Role name already exists
            </mat-error>
          </div>

          <div class="col-md-12">
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': disableModule}">
              <mat-label>Module Name</mat-label>
              <input matInput placeholder="module Name" aria-label="module" [matAutocomplete]="auto1" 
                formControlName="module" (keyup.enter)="addOption()" oninput="this.value = this.value.toUpperCase()" [readonly]="disableModule">
              <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected($event.option)">
                <mat-option *ngFor="let module of modulesBank | async" [value]="module" [disabled]="moduleOptionDisabled(module) || disableModule">
                  <span>{{ module }}</span>
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
          </div>

          <div class="col-md-12">
            <mat-form-field appearance="outline">
              <mat-label>Screen Name</mat-label>
              <mat-select formControlName="Page">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                    [formControl]="PageFilterCtrl"></ngx-mat-select-search>
                </mat-option>
                <mat-divider></mat-divider>
                <mat-option *ngFor="let option of pages | async" [value]="option" [disabled]="pageOptionDisabled(option)">{{option}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          
          <div>
            <div class="col">
              <label>Do you want to discontinue?</label>
              <mat-radio-group formControlName="Discontinued" aria-labelledby="example-radio-group-label">
                <mat-radio-button value="yes">Yes</mat-radio-button>
                <mat-radio-button value="no">No</mat-radio-button>
              </mat-radio-group>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>