import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import MarkdownIt from 'markdown-it';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { InventoryService } from 'src/app/services/inventory.service';
import { first } from 'rxjs';

@Component({
  selector: 'app-recipe-llm',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './recipe-llm.component.html',
  styleUrls: ['./recipe-llm.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RecipeLlmComponent implements OnInit {
  public md: MarkdownIt;
  @Input() master: any;
  @Input() child: any;

  public renderedMDHtml: SafeHtml;
  public isLoading: boolean = true;

  constructor(
    private sanitizer: DomSanitizer,
    private api: InventoryService,
    private cd: ChangeDetectorRef,
  ) { }

  public ngOnInit(): void {
    let mapping = {
      "menu_master": this.master,
      "menu_recipes": this.child,
    }
    this.api.recipeInsight(mapping).pipe(first()).subscribe({
      next: (res) => {
        this.md = new MarkdownIt();
        this.md
        .set({
          typographer: true,
          linkify: true,
          xhtmlOut: true,
          html: false,
        })
        .enable(['smartquotes', 'replacements', 'image']);
        const renderedMarkdown = this.md.render(res['data']);
        this.renderedMDHtml = this.sanitizer.bypassSecurityTrustHtml(renderedMarkdown);
        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: (err) => { 
        console.log(err);
        this.isLoading = false;
        this.cd.detectChanges();
      }
    });
  }
}