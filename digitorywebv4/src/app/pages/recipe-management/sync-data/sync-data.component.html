<div *ngIf="dataReady">
  <div class="closeBtn">
    <mat-icon (click)="return()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
  </div>
  <div class="registration-form py-2 px-3">
    <div>
      <!-- class="my-2" -->
      <div>
        <mat-card>
          <div *ngIf="this.retrieveData" class="evaluation-container pb-3">
            <div class="text-center m-2 p-2 bottomTitles">
              <span>
                Session Id -
                <ng-container
                  *ngIf="(retrieveData[0]?.sessionId !== 'NA' && retrieveData[0]) && retrieveData[0]?.sessionId !== undefined; else bulkUpdate">
                  {{ retrieveData[0]?.sessionId }}
                </ng-container>
                <ng-template #bulkUpdate>
                  Bulk Update
                </ng-template>
              </span>
            </div>
            <div _ngcontent-knm-c68 class="candidate-evaluation d-flex align-items-center justify-content-center">
              <div class="progress_bar">
                <div class="stepper-item stepper-item1" [ngClass]="{'completed': statusData.provision.status === true}">
                  <div class="step-counter">
                    <mat-icon *ngIf="statusData.provision.status === true">check_circle</mat-icon>
                    <mat-icon *ngIf="statusData.provision.error === true">cancel</mat-icon>
                    <div *ngIf="statusData.provision.status === false" class="spinner-border spinner_class"
                      role="status">
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>
                  <div class="step-name">Provision</div>
                  <div class="date_time d-flex">
                    <div>
                      <span *ngIf="!isSmallScreen" class="time ms-1">{{ statusData.provision.datetime | date:
                        'dd/MM/yyyy hh:mm a' }}</span>
                      <span *ngIf="isSmallScreen" class="time ms-1">{{ statusData.provision.datetime | date: 'MMM d, y'
                        }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="stepper-item stepper-item2"
                [ngClass]="{'completed': statusData.quality.status === true && statusData.quality.error === false, 'error' : statusData.quality.status === true && statusData.quality.error === true}">
                <div class="step-counter">
                  <mat-icon
                    *ngIf="statusData.quality.status === true && statusData.quality.error === false">check_circle</mat-icon>
                  <mat-icon
                    *ngIf="statusData.quality.status === true && statusData.quality.error === true">cancel</mat-icon>
                  <div
                    *ngIf="(statusData.provision.status === true && statusData.provision.error === false) && statusData.quality.status === false"
                    class="spinner-border spinner_class" role="status">
                    <span class="sr-only">Loading...</span>
                  </div>
                  <mat-icon *ngIf="statusData.provision.status === false || statusData.provision.error === true"
                    class="timerIcon">timer</mat-icon>
                </div>
                <div class="step-name">Quality</div>
                <div class="date_time d-flex">
                  <div>
                    <span *ngIf="!isSmallScreen" class="time ms-1">{{ statusData.quality.datetime | date: 'dd/MM/yyyy hh:mm a' }}</span>
                    <span *ngIf="isSmallScreen" class="time ms-1">{{ statusData.quality.datetime | date: 'MMM d, y'}}</span>
                  </div>
                </div>
              </div>


              <div class="stepper-item"
                [ngClass]="{'completed': statusData.deployment.status === true && statusData.deployment.error === false, 'error' : statusData.deployment.status === true && statusData.deployment.error === true}">

                <div class="step-counter">
                  <mat-icon
                    *ngIf="statusData.deployment.status === true && statusData.deployment.error === false">check_circle</mat-icon>
                  <mat-icon
                    *ngIf="statusData.deployment.status === true && statusData.deployment.error === true">cancel</mat-icon>
                  <div
                    *ngIf="(statusData.quality.status === true && statusData.quality.error === false) && statusData.deployment.status === false"
                    class="spinner-border spinner_class" role="status">
                    <span class="sr-only">Loading...</span>
                  </div>
                  <mat-icon *ngIf="statusData.quality.status === false || statusData.quality.error === true"
                    class="timerIcon">timer</mat-icon>
                </div>
                <div class="step-name">Deployment</div>
                <div class="date_time d-flex">
                  <div>
                    <span *ngIf="!isSmallScreen" class="time ms-1">{{ statusData.deployment.datetime | date: 'dd/MM/yyyy hh:mm a' }}</span>
                    <span *ngIf="isSmallScreen" class="time ms-1">{{ statusData.deployment.datetime | date: 'MMM d, y'}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </mat-card>

        <div class="section mt-3" *ngIf="this.dataSource.data.length > 0">
          <mat-table [dataSource]="dataSource" matSort>
            <ng-container matColumnDef="action">
              <mat-header-cell class="custom-header" *matHeaderCellDef> Preview </mat-header-cell>
              <mat-cell class="custom-cell" *matCellDef="let element">
                <button backgroundColor="primary" class="mx-2 mt-3 editIconBtn"><mat-icon>visibility</mat-icon></button>
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="id">
              <mat-header-cell class="custom-header" *matHeaderCellDef mat-sort-header > Session Id </mat-header-cell>
              <mat-cell class="custom-cell" *matCellDef="let element" >
                <ng-container *ngIf="element.sessionId !== 'NA' && element.sessionId; else bulkUpdate">
                  {{ element.sessionId }}
                </ng-container>
                <ng-template #bulkUpdate>
                  Bulk Update UI
                </ng-template>
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="client">
              <mat-header-cell class="custom-header" *matHeaderCellDef> Client </mat-header-cell>
              <mat-cell class="custom-cell" *matCellDef="let element"> {{ element.client }} </mat-cell>
            </ng-container>

            <ng-container matColumnDef="userName">
              <mat-header-cell class="custom-header" *matHeaderCellDef mat-sort-header> Created By </mat-header-cell>
              <mat-cell class="custom-cell" *matCellDef="let element" style="word-break: break-word !important;"> {{ element.email }} </mat-cell>
            </ng-container>

            <ng-container matColumnDef="syncEmail">
              <mat-header-cell class="custom-header" *matHeaderCellDef mat-sort-header> Synced By </mat-header-cell>
              <mat-cell class="custom-cell" *matCellDef="let element" style="word-break: break-word !important;"> {{ element.syncEmail ? element.syncEmail : '-' }} </mat-cell>
            </ng-container>

            <ng-container matColumnDef="createdDate">
              <mat-header-cell class="custom-header" *matHeaderCellDef mat-sort-header> Created Date </mat-header-cell>
              <mat-cell class="custom-cell" *matCellDef="let element">
                {{ element.createTs | date:'dd-MM-yyyy hh:mm:ss a' }}
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="category">
              <mat-header-cell class="custom-header" *matHeaderCellDef> Category </mat-header-cell>
              <mat-cell class="custom-cell" *matCellDef="let element"> {{element.category }} </mat-cell>
            </ng-container>

            <ng-container matColumnDef="status">
              <mat-header-cell class="custom-header" *matHeaderCellDef> Status </mat-header-cell>
              <mat-cell class="custom-cell justify-content-start" *matCellDef="let element">
                <div *ngIf="element.status === 'Completed'" class="d-flex align-items-center">
                  <mat-icon class="check_circleIcon">check_circle</mat-icon> Completed
                </div>
                <div *ngIf="element.status === 'Failed'" class="d-flex align-items-center">
                  <mat-icon class="error_circleIcon">error_outline</mat-icon> Failed
                </div>
                <div *ngIf="element.status === 'pending'" class="d-flex align-items-center">
                  <div class="spinner-border spinner_class" role="status">
                    <span class="sr-only">Loading...</span>
                  </div>
                  Progress
                </div>
              </mat-cell>
            </ng-container>

            <ng-container matColumnDef="errorLog">
              <mat-header-cell class="tableActionCol" *matHeaderCellDef> Action </mat-header-cell>
              <mat-cell class="tableActionCol" *matCellDef="let element">
                <button (click)="viewStatus(element)" backgroundColor="primary"
                  class="mx-2 editIconBtn editIconBtnSync" matTooltip="view status" [ngClass]="{'active-row': activeRowIndex === element}">
                  <mat-icon class="mt-1">remove_red_eye</mat-icon></button>
                <button (click)="getErrorlog(element)" backgroundColor="primary"
                  class="mx-2 editIconBtn editIconBtnSync" matTooltip="view error">
                  <mat-icon class="mt-1">error</mat-icon></button>
              </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
          </mat-table>
          <mat-paginator class="mat-paginator-sticky" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 50, 100]"></mat-paginator>
        </div>
        <div *ngIf="this.dataSource.data.length === 0">
          <app-empty-state
            icon="history"
            title="No Sync History Found"
            message="There is no sync history to display at the moment."
          ></app-empty-state>
        </div>
      </div>
    </div>
  </div>
</div>

<div *ngIf="!dataReady" class="loader">
  <span>Loading History...</span>
  <ngx-skeleton-loader count="5" animation="pulse" [theme]="{
    'border-radius': '4px',
    'height': '30px',
    'margin-bottom': '8px',
    'width': '19%',
    'margin-right': '1%',
    'display': 'inline-block',
    'opacity': '0.85'
  }"></ngx-skeleton-loader>
</div>
