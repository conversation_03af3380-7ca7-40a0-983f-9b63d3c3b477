<div class="closeBtn">
    <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
  </div>

  <div class="m-3 py-2 px-3">
    <div class="text-center p-2 my-2 bottomTitles">
      Mapping
    </div>

    <div>
      <canvas #barChart></canvas>
    </div>
    <br>
    <mat-tab-group animationDuration="1000ms" (selectedIndexChange)="tabChanged($event)" (click)="triggerChart()">
        <mat-tab label="vendor Mapping">
            <mat-card appearance="outlined" class="mt-1 p-3">  

                <form [formGroup]="vendorMappingForm">
                    <mat-form-field appearance="outline">
                        <mat-label>Vendor</mat-label>
                        <mat-select formControlName="vendor" (selectionChange)="getVendorCategories($event.value)">
                          <mat-option>
                            <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                              [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                          </mat-option>
                          <mat-divider></mat-divider>
                          <mat-option *ngFor="let option of vendor | async" [value]="option">{{option}}</mat-option>
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': !vendorMappingForm.value.vendor}">
                        <mat-label>Category</mat-label>
                        <mat-select formControlName="category" (selectionChange)="getSubCategories($event.value)" multiple
                        [disabled]="!vendorMappingForm.value.vendor">
                          <mat-option>
                            <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                              [formControl]="catFilterCtrl"></ngx-mat-select-search>
                          </mat-option>
                          <mat-option class="hide-checkbox" (click)="allSelectCategory('vendorMappingForm')" [disabled]="!vendorMappingForm.value.vendor">
                            <mat-icon matSuffix>check_circle</mat-icon>
                            Select All / Deselect All
                          </mat-option>
                          <mat-divider></mat-divider>
                          <mat-option *ngFor="let cat of cat | async" [value]="cat" [disabled]="!vendorMappingForm.value.vendor">
                            <span>{{ cat }}</span>
                          </mat-option>
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': this.filteredCatData.length === 0}">
                        <mat-label>Sub Category</mat-label>
                        <mat-select formControlName="subCategory" (selectionChange)="getFilteredData($event.value)" multiple
                        [disabled]="this.filteredCatData.length === 0">
                          <mat-option>
                            <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                              [formControl]="subCatFilterCtrl"></ngx-mat-select-search>
                          </mat-option>
                          <mat-option class="hide-checkbox" (click)="allSelectSubCategory('vendorMappingForm')" [disabled]="this.filteredCatData.length === 0">
                            <mat-icon matSuffix>check_circle</mat-icon>
                            Select All / Deselect All
                          </mat-option>
                          <mat-optgroup *ngFor="let group of subCat | async" [label]="group.category"
                            [disabled]="group.disabled">
                            <mat-option *ngFor="let val of group.subCategories" [value]="val" [disabled]="this.filteredCatData.length === 0">
                              {{val}}
                            </mat-option>
                          </mat-optgroup>
                        </mat-select>
                      </mat-form-field>

                      <div>
                        <button mat-flat-button [disabled]="!this.vendorMappingForm.valid"
                          color="accent" class="mt-1 floatRightBtn" (click)="mapping()" matTooltip="add">
                          Update Mapping
                        </button>
                      </div>
                </form>
            </mat-card>
        </mat-tab>

        <mat-tab label="WorkArea Mapping">
            <mat-card appearance="outlined" class="mt-1 p-3">
              <form [formGroup]="workAreaMappingForm">

                <mat-form-field appearance="outline">
                  <mat-label>Location</mat-label>
                  <mat-select formControlName="location" (selectionChange)="getWorkAreas($event.value)">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                        [formControl]="locationFilterCtrl"></ngx-mat-select-search>
                    </mat-option>
                    <mat-divider></mat-divider>
                    <mat-option *ngFor="let option of location | async" [value]="option">{{option | uppercase}}</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': !workAreaMappingForm.value.location}">
                  <mat-label>workArea</mat-label>
                  <mat-select formControlName="workArea" (selectionChange)="getWorkAreaCategory($event.value)"
                    [disabled]="!workAreaMappingForm.value.location">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                        [formControl]="workAreaFilterCtrl"></ngx-mat-select-search>
                    </mat-option>
                    <mat-divider></mat-divider>
                    <mat-option *ngFor="let option of workArea | async" [value]="option" [disabled]="!workAreaMappingForm.value.location">
                      {{option | uppercase}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': !workAreaMappingForm.value.workArea}">
                  <mat-label>Category</mat-label>
                  <mat-select formControlName="category" (selectionChange)="getSubCategories($event.value)" multiple
                  [disabled]="!workAreaMappingForm.value.workArea">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                        [formControl]="catFilterCtrl"></ngx-mat-select-search>
                    </mat-option>
                    <mat-option class="hide-checkbox" (click)="allSelectCategory('workAreaMappingForm')" [disabled]="!workAreaMappingForm.value.workArea">
                      <mat-icon matSuffix>check_circle</mat-icon>
                      Select All / Deselect All
                    </mat-option>
                    <mat-divider></mat-divider>
                    <mat-option *ngFor="let cat of cat | async" [value]="cat" [disabled]="!workAreaMappingForm.value.workArea">
                      <span>{{ cat }}</span>
                    </mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': this.filteredCatData.length === 0}">
                  <mat-label>Sub Category</mat-label>
                  <mat-select formControlName="subCategory" (selectionChange)="getFilteredData($event.value)" multiple
                  [disabled]="this.filteredCatData.length === 0">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                        [formControl]="subCatFilterCtrl"></ngx-mat-select-search>
                    </mat-option>
                    <mat-option class="hide-checkbox" (click)="allSelectSubCategory('workAreaMappingForm')">
                      <mat-icon matSuffix>check_circle</mat-icon>
                      Select All / Deselect All
                    </mat-option>
                    <mat-optgroup *ngFor="let group of subCat | async" [label]="group.category"
                      [disabled]="group.disabled">
                      <mat-option *ngFor="let val of group.subCategories" [value]="val" [disabled]="this.filteredCatData.length === 0">
                        {{val}}
                      </mat-option>
                    </mat-optgroup>
                  </mat-select>
                </mat-form-field>

                <div>
                  <button mat-flat-button [disabled]="!this.workAreaMappingForm.valid"
                    color="accent" class="mt-1 floatRightBtn" (click)="mapping()" matTooltip="add">
                    Update Mapping
                  </button>
                </div>
            </form>
            </mat-card>
        </mat-tab>
      </mat-tab-group>

  </div>
  
