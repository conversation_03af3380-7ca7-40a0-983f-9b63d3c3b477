<app-background-image-card
  [backgroundSrc]="'https://img.freepik.com/free-vector/cooks-set-with-three-square-compositions-professional-chefs-cutting-vegetables-frying-pot-decorating-meals-vector-illustration_1284-83937.jpg?w=5000'">
  <div class="headingBtns">
    <button (click)="openBottomSheet()" mat-raised-button color="warn" class="sync my-1" matTooltip="Other Options" [disabled]="!isDataReady">
      <mat-icon>settings</mat-icon>Other Options</button>
  </div>

</app-background-image-card>

<mat-card>
  <mat-tab-group [(selectedIndex)]="selectedTabIndex" (selectedTabChange)="tabClick($event)" class="m-1">
    <mat-tab *ngFor="let tab of tabs">
      <ng-template mat-tab-label>
        {{tab.label}}
      </ng-template>
      <div *ngIf="isDataReady">
        <app-http-table [page]="tab.page" [data]="baseData[tab.page]"
          *ngIf="selectedTabIndex == tab.index"></app-http-table>
      </div>
      <div *ngIf="!isDataReady" class="my-3">
        <ngx-skeleton-loader count="50" animation="pulse" [theme]="{
          'border-radius': '4px',
          'height': '30px',
          'margin-bottom': '8px',
          'width': '19%',
          'margin-right': '1%',
          'display': 'inline-block',
          'opacity': '0.85'
        }"></ngx-skeleton-loader>
      </div>
    </mat-tab>
  </mat-tab-group>
</mat-card>
