<div class="p-2">
  <div class="px-1 col-lg-12">
    <div class="closeBtn" *ngIf="isDuplicate == true">
      <mat-icon (click)="close()" matTooltip="close" class="closeBtnIcon">close</mat-icon>
    </div>

    <div *ngIf="this.costDialogkey == true" class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>

    <div class="mt-3 registration-form smallDialog" *ngIf="isDuplicate == true">
      <div class="col-md-12">
        <div class="text-center my-2 p-1 bottomTitles">
          <span>Menu Master Form</span>
        </div>
        <mat-form-field appearance="outline">
          <mat-label>Search Menu Master ..</mat-label>
          <input matInput placeholder="Menu Master Name" aria-label="menu master" [matAutocomplete]="auto1"
            [formControl]="itemNameControl" (keyup.enter)="addOption('menu master')" (keyup)="checkItem($event)"
            oninput="this.value = this.value.toUpperCase()">
          <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected('menu master', $event.option)">
            <ng-container *ngFor="let category of itemNameOptions | async">
              <ng-container *ngIf="category.items.length > 0">
                <mat-accordion [multi]="multi">
                  <mat-expansion-panel>
                    <mat-expansion-panel-header class="expansionClass">
                      <b *ngIf="category.category === 'pos only'"> UNLINKED POS RECIPES</b>
                      <b *ngIf="category.category === 'inventory only'"> UNLINKED INVENTORY RECIPES</b>
                      <b *ngIf="category.category != 'pos only' && category.category != 'inventory only'">INTERLINKED
                        RECIPES</b>
                    </mat-expansion-panel-header>
                    <mat-option *ngFor="let item of category.items" [value]="item">
                      <span>{{ item | uppercase }}</span>
                    </mat-option>
                  </mat-expansion-panel>
                </mat-accordion>
              </ng-container>

              <mat-option *ngIf="category.items.length === 0" [value]="category.category">
                <span>{{ category.category}}</span>
              </mat-option>
            </ng-container>
          </mat-autocomplete>
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <div class="text-end">
          <button (click)="addOption('inventory master')" mat-raised-button color="accent"
            [disabled]="!itemNameControl.value">
            <mat-icon *ngIf="!updateBtnActive">library_add</mat-icon>
            <mat-icon *ngIf="updateBtnActive">update</mat-icon>
            <span *ngIf="updateBtnActive">Update</span>
            <span *ngIf="!updateBtnActive">Add</span>
          </button>
        </div>
      </div>
    </div>

    <div *ngIf="isDuplicate == false" [style.min-width]="minWidth" [style.min-height]="minHeight">
      <form [formGroup]="createRecipeForm">

        <div class="header-container">
          <div class="topTitleName">
            <mat-icon class="restIcons">restaurant_menu</mat-icon>
            <span class="topNameClass">{{this.createRecipeForm.value.recipeName}} - Current preparation size
              <span class="badge hi-badge ml-2">
                {{this.createRecipeForm.value.overallServingSize.toUpperCase()}}
              </span>
            </span>
          </div>

          <div class="button-group">
            <ng-container *ngIf="isCreate; else updateButton">
              <button mat-raised-button color="accent" (click)="create(demo2tab)" [disabled]="isDone"
                matTooltip="Create">
                <mat-icon *ngIf="!isDone">add_circle</mat-icon>
                <span *ngIf="isDone" class="spinner-border" role="status">
                  <span class="sr-only">Loading...</span>
                </span>
                Create
              </button>
            </ng-container>

            <ng-template #updateButton>
              <button mat-raised-button color="accent" (click)="update(demo2tab)"
                [disabled]="loadMmBtn || isDone || dataSource.data.length == 0"
                matTooltip="Update">
                <span *ngIf="isDone" class="spinner-border" role="status">
                  <span class="sr-only">Loading...</span>
                </span>
                Update
              </button>

              <ng-container *ngIf="!inventoryItem">
                <button mat-raised-button color="accent" (click)="openMenuMapping(createRecipeForm.value)"
                  matTooltip="Menu Mapping">
                  Mapping
                </button>
              </ng-container>

              <button mat-raised-button (click)="printOption()" matTooltip="Print">
                <mat-icon>print</mat-icon>
                Print
              </button>
            </ng-template>
            <button mat-raised-button color="warn" (click)="close()" matTooltip="Close">
              <mat-icon>close</mat-icon>
              Close
            </button>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="row m-1">
          <!-- Gross Weight -->
          <div class="col">
            <div class="topCards topCardStorage">
              <div class="topCardText">Gross Weight</div>
              <div class="topCardsCircle">
                <div class="ab">{{this.getTotal('InitialWeight')}} (gm)</div>
                <mat-icon class="circleIcons">storage</mat-icon>
              </div>
            </div>
          </div>

          <!-- Cost Of Production -->
          <div class="col">
            <div class="topCards topCardmonetization_on">
              <div>
                <mat-form-field  class="topInuptFields cardSelect">
                  <mat-select placeholder="Select Branch" [formControl]="globalLocation" (selectionChange)="getMenuRecipes()">
                    <ng-container class="XXXXXXx" style="width: 200px !important;">
                      <mat-option>
                        <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                          [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                      </mat-option>
                      <mat-option *ngFor="let rest of vendorsBanks | async" [value]="rest" >
                        {{ rest.branchName | uppercase}}
                      </mat-option>
                    </ng-container>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="topCardText">Cost Of Production
                <!-- <mat-icon class="circleIcons" style="float: right;" (click)="openLocations('CostOfProduction')">location_on</mat-icon> -->

                <!-- <mat-form-field appearance="outline" class="topInuptFields">
                  <mat-select placeholder="Select Branch" [formControl]="globalLocation" (selectionChange)="getMenuRecipes()">
                    <ng-container class="XXXXXXx" style="width: 200px !important;">
                      <mat-option>
                        <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                          [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                      </mat-option>
                      <mat-option *ngFor="let rest of vendorsBanks | async" [value]="rest" >
                        {{ rest.branchName | uppercase}}
                      </mat-option>
                    </ng-container>
                  </mat-select>
                </mat-form-field> -->


              </div>
              <!-- <span class="cardValues">{{ this.globalLocation.value.branchName }}</span> -->

              <div class="topCardsCircle">
                <div class="ab">{{this.getTotal('finalRate')}} (Rs) </div>
                <mat-icon class="circleIcons">monetization_on</mat-icon>
              </div>
            </div>
          </div>

          <!-- Selling Price (non-inventory) -->
          <div class="col" *ngIf="!inventoryItem">
            <button class="topCards topCardshopping_basket" (click)="openSalesData()">
              <div>
                <mat-form-field class="topInuptFields cardSelect">
                  <mat-select placeholder="Select Branch" [formControl]="sellingLocation" (selectionChange)="getDetailedPriceList($event.value)"
                  (click)="$event.stopPropagation()">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                        [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                    </mat-option>
                    <mat-option *ngFor="let rest of vendorsBanks | async" [value]="rest"
                    [disabled]="!checkNewIdsInAllData(rest.restaurantIdOld)" >
                      <span>{{ rest.branchName | uppercase }}</span>
                      <mat-icon *ngIf="!checkNewIdsInAllData(rest.restaurantIdOld)"
                      class="deleteIconForMatSelect" matTooltip="add priceTier"
                      (click)="addSellingPrice()">
                      add</mat-icon>
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
              <div class="topCardText">Selling Price
                <!-- <mat-icon class="circleIcons" style="float: right;" matTooltip="change location"
                 (click)="$event.stopPropagation(); openLocations('SellingPrice')">location_on</mat-icon> -->

                <!-- <mat-form-field appearance="outline" class="topInuptFields">
                  <mat-select placeholder="Select Branch" [formControl]="sellingLocation" (selectionChange)="getDetailedPriceList($event.value)"
                  (click)="$event.stopPropagation()">
                    <mat-option>
                      <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                        [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                    </mat-option>
                    <mat-option *ngFor="let rest of vendorsBanks | async" [value]="rest"
                    [disabled]="!checkNewIdsInAllData(rest.restaurantIdOld)" >
                      <span>{{ rest.branchName | uppercase }}</span>
                      <mat-icon *ngIf="!checkNewIdsInAllData(rest.restaurantIdOld)"
                      class="deleteIconForMatSelect" matTooltip="add priceTier"
                      (click)="addSellingPrice()">
                      add</mat-icon>
                    </mat-option>
                  </mat-select>
                </mat-form-field> -->

              </div>
              <!-- <span class="cardValues">{{this.sellingLocation?.value?.branchName}}</span> -->
              <div class="topCardsCircle selling-price-hover">
                <div class="ab">{{ this.overAllSellingPrice }} (Rs)</div>
                <mat-icon class="circleIcons">shopping_basket</mat-icon>
              </div>
            </button>
          </div>

          <!-- Selling Price (inventory) -->
          <div class="col" *ngIf="inventoryItem">
            <div class="topCards topCardshopping_basket">
              <div class="topCardText">Selling Price</div>
              <div class="topCardsCircle selling-price-hover">
                <input matInput type="text" class="sellingInput" [formControl]="sellingPriceControl"
                  (input)="calculateRecipeCost()">
                <span>(Rs)</span>
                <mat-icon class="circleIcons">shopping_basket</mat-icon>
              </div>
            </div>
          </div>

          <!-- Profit Margin -->
          <div class="col">
            <div class="topCards topCardaccount_balance_wallet">
              <div class="topCardText">Profit Margin</div>
              <div class="topCardsCircle">
                <div class="ab">{{profitMargin}} (Rs)</div>
                <mat-icon class="circleIcons">account_balance_wallet</mat-icon>
              </div>
            </div>
          </div>

          <!-- CP/SP -->
          <div class="col">
            <div class="topCards topCardaccount-percentage">
              <div class="topCardText">CP/SP</div>
              <div class="topCardsCircle">
                <div class="ab">{{costPercentage}} (%)</div>
                <!-- <mat-icon class="circleIconsPer">%</mat-icon> -->
              </div>
            </div>
          </div>

          <!-- Profit Percentage -->
          <div class="col">
            <div class="topCards topCardaccount-ProPpercentage">
              <div class="topCardText">Profit Percentage</div>
              <div class="topCardsCircle">
                <div class="ab">{{profitPercentage}} (%)</div>
                <!-- <mat-icon class="circleIconsPer">%</mat-icon> -->
              </div>
            </div>
          </div>
        </div>

          <div class="form-row d-flex gap-3 mb-1">
            <div class="form-group customHeightfield flex-item">
              <label for="recipeName">Recipe Name</label>
              <input class="form-control" id="recipeName" type="text" formControlName="recipeName" autocomplete="off"
                placeholder="Recipe Name" [readonly]="!isEnableName">
            </div>
            <div class="form-group customHeightfield flex-item">
              <label for="recipeCode">Recipe Code</label>
              <div class="input-group">
                <input class="form-control" id="recipeCode" type="text" formControlName="recipeCode" autocomplete="off"
                  placeholder="Enter Recipe Code" readonly>
                <button *ngIf="!isCreateMode && inventoryItem" class="input-group-text" (click)="openRenameData()"
                  style="cursor: pointer;">
                  <i class="material-icons">edit</i>
                </button>
              </div>
            </div>

            <!-- <div class="form-group customHeightfield flex-item">
              <label for="restaurant">Recipe Preparatory Location</label>
              <select class="form-select" id="restaurant" formControlName="restaurant" (change)="restaurantChange($event)">
                <option disabled selected>Select Restaurant</option>
                <option *ngFor="let data of resData | async" [value]="data">{{data.branchName}}</option>
              </select>
            </div> -->

            <div class="form-group customHeightfield flex-item">
              <label for="overallServingSize">Serving Size</label>
              <select class="form-select" id="overallServingSize" formControlName="overallServingSize"
                (change)="servingSizeChange($event.target['value'])">
                <option disabled selected>Select Serving Size</option>
                <option value="full-default">Full(Default) - 100 %</option>
                <option *ngFor="let size of servingData | async" [value]="size['Serving Size']"
                  [ngStyle]="{'color': posServingSizes.includes(size['Serving Size']) ? '#0ccccc' : 'inherit'}">
                  {{ size['Serving Size'] }} - {{ size['Ratio'] }} %
                </option>
              </select>
            </div>

            <div class="form-group customHeightfield flex-item">
              <label for="portion">No. of Portions</label>
              <div class="input-group">
                <input class="form-control" id="portion" type="text" formControlName="portion" autocomplete="off"
                  placeholder="No. of Portions" (keyup)="getPortionWeight()">
                <span class="input-group-text" (click)="openPortionData()" title="Portion Details"
                  style="cursor: pointer;">
                  <i class="material-icons">info</i>
                </span>
              </div>
            </div>

            <div class="form-group customHeightfield flex-item">
              <label for="perPortionWeight">Weight per Portion (grams)</label>
              <input class="form-control" id="perPortionWeight" type="text" formControlName="PerPortionWeight" autocomplete="off"
                placeholder="Weight per Portion (grams)" (keyup)="convertToPortion()">
            </div>
          </div>

        <!-- </mat-card> -->
        <mat-card>
          <mat-tab-group animationDuration="500ms" #demo2tab>
            <mat-tab label="Ingredients">
              <ng-template mat-tab-label>
                Ingredients
              </ng-template>
              <div class="m-1">
                <div class="col text-end">
                  <mat-slide-toggle [(ngModel)]="isChecked" (change)="onToggleChange($event)"
                    [ngModelOptions]="{standalone: true}">Show
                    Modifier</mat-slide-toggle>
                </div>

                <div cdkTrapFocus>
                  <form [formGroup]="ingredientsForm">
                    <div class="d-flex gap-3">
                      <div class="form-group customHeightfield">
                        <label for="ingredientSelect">Ingredient Name</label>
                        <!-- <select class="form-select selectInputCustom" id="ingredientSelect" #ingredientSelect name="Ingredient Name"
                          [formControl]="itemNameControlIngredients" (change)="addOptionIngredients(addIngredients)"> -->
                          <!-- <option style="height: 30px;">
                            <input  type="text" class="form-control" placeholder="search..." placeholder="Search"
                            [formControl]="nameOptionsFilterCtrl">
                          </option> -->
                          <!-- <option *ngFor="let item of nameOptions | async" [value]="item['itemCode']"
                            [disabled]="isDisabled(item) || item['status'] === 'discontinued'"
                            [ngClass]="{'sub-recipe': item['isSubRecipe']}">
                            {{ item['itemName'] | uppercase }}
                          </option>
                        </select> -->
                      <!--  -->

                      <input matInput placeholder="Ingredient Name" [matAutocomplete]="autoIngredients" class="form-control"
                            formControlName="ingredientName" oninput="this.value = this.value.toUpperCase()">
                      <mat-autocomplete #autoIngredients="matAutocomplete" (optionSelected)="addOptionIngredients(addIngredients)">
                        <mat-option *ngFor="let item of ingredientNamesOptions | async" [value]="item"
                        [disabled]="isDisabled(item) || item['status'] === 'discontinued' || item === 'No Item Found'"
                        [ngClass]="{'sub-recipe': subRecData.includes(item)}">
                          {{ item | uppercase }}
                        </mat-option>
                      </mat-autocomplete>
                      </div>

                      <div class="form-group customHeightfield"
                        *ngIf="ingredientsForm.value.isModifier === 'Yes' || this.isChecked">
                        <label for="modifierSelect">Modifier Name</label>
                        <!-- <select class="form-select" id="modifierSelect" formControlName="modifierName"
                          style="width: 250px;">
                          <option>
                            <input type="text" class="form-control" placeholder="search..."
                              [formControl]="modifierFilterCtrl">
                          </option>
                          <option *ngFor="let restaurant of modifierData | async" [value]="restaurant">{{ restaurant
                            }}</option>
                        </select> -->

                        <input matInput placeholder="modifier Name" [matAutocomplete]="autoPack" class="form-control"
                        formControlName="modifierName" oninput="this.value = this.value.toUpperCase()">
                      <mat-autocomplete #autoPack="matAutocomplete">
                        <mat-option *ngFor="let restaurant of modifierOptions | async" [value]="restaurant" [disabled]="this.filtered.length == 1">
                          <span>{{ restaurant | uppercase }}</span>
                        </mat-option>
                      </mat-autocomplete>
                      </div>

                      <div class="form-group customHeightfield" [ngClass]="{'highlighted-input': isReadOnly}">
                        <label for="uomSelect">UOM</label>
                        <select class="form-select" id="uomSelect" formControlName="ConsumptionUOM"
                          (change)="uomChange($event)">
                          <option *ngFor="let val of ingredientClosingUOM" [value]="val"
                            [disabled]="!isOptionAccessible(val)">{{ val | uppercase }}</option>
                        </select>
                      </div>

                      <div class="form-group customHeightfield"
                        *ngIf="this.ingredientsForm.value.ConsumptionUOM  === 'PORTION'">
                        <label for="portionCountInput">No. Of Portions</label>
                        <input type="number" class="form-control" id="portionCountInput" placeholder="No. Of Portions"
                          formControlName="portionCount" (keyup)="convertPortionToUOM($event)"
                          (focus)="focusFunction('portionCount')" (focusout)="focusOutFunction('portionCount')" />
                      </div>

                      <div class="form-group customHeightfield"
                        [ngClass]="{'highlighted-input': this.ingredientsForm.value.ConsumptionUOM === 'PORTION'}">
                        <label *ngIf="checkWidth >= 1000" for="weightInUseInput">Weight In Use (UOM)</label>
                        <label *ngIf="1000 >= checkWidth" for="weightInUseInput">Weight</label>
                        <input type="number" class="form-control" id="weightInUseInput" placeholder="Weight In Use"
                          formControlName="weightInUse" (keyup)="setInitialWeight();makeTotalPrice()"
                          (focus)="focusFunction('weightInUse')" (focusout)="focusOutFunction('weightInUse')"
                          [readonly]="this.ingredientsForm.value.ConsumptionUOM === 'PORTION'" />
                          <!-- <div class="formError" *ngIf="this.showWeightError">
                            * weight should be greater than 0
                          </div> -->
                          <div *ngIf="ingredientsForm.get('weightInUse')?.touched && ingredientsForm.get('weightInUse')?.errors?.['weightInvalid']" class="formError">
                            Yield should be more than 0
                          </div>
                      </div>

                      <div class="form-group customHeightfield">
                        <label for="yieldInput">Yield</label>
                        <input type="number" class="form-control" id="yieldInput" placeholder="Yield"
                          formControlName="Yield" (keyup)="setInitialWeight()" (focus)="focusFunction('Yield')"
                          (focusout)="focusOutFunction('Yield')" />
                          <!-- <div *ngIf="this.showYieldError" class="formError">
                            Yield should be more than 0
                          </div> -->
                          <div *ngIf="ingredientsForm.get('Yield')?.touched && ingredientsForm.get('Yield')?.errors?.['yieldInvalid']" class="formError">
                            Yield should be more than 0
                          </div>
                      </div>

                      <div class="form-group customHeightfield" [ngClass]="{'highlighted-input': isReadOnly}">
                        <label for="priceInput">Price ₹</label>
                        <input type="text" class="form-control" id="priceInput" placeholder="Price ₹"
                          formControlName="rate" (keyup)="makeTotalPrice()" [readonly]="isReadOnly" />
                      </div>

                      <div class="form-group flex-shrink-0 d-flex align-items-end justify-content-end"
                        style="margin-bottom: 0.1px;">
                        <button type="submit" style="height: 2.2rem;" class="btn btn-secondary btn-sm px-3"
                          (click)="addNewSubRecipeRecipe()" matTooltip="Add">
                          <i class="material-icons align-middle">add</i> Add
                        </button>
                      </div>
                    </div>
                  </form>
                </div>

                <div class="col text-end pt-2 pb-3">
                  <mat-slide-toggle [(ngModel)]="showDeleteItems" [ngModelOptions]="{standalone: true}"
                    (change)="showItems()">Show Discontinued</mat-slide-toggle>
                </div>
                <div>
                  <div *ngIf="isIngredientsDataReady">
                    <table #table mat-table [dataSource]="dataSource">
                      <ng-container matColumnDef="select">
                        <th mat-header-cell *matHeaderCellDef class="tableSnoCol">
                          <mat-checkbox (change)="onSelectAll($event.checked)" [checked]="isAllSelected()"
                            [indeterminate]="selection.hasValue() && !isAllSelected()">
                          </mat-checkbox>
                        </th>
                        <td mat-cell *matCellDef="let row" class="tableSnoCol">
                          <mat-checkbox (click)="$event.stopPropagation()" (change)="onRowSelect($event.checked, row)"
                            [checked]="selection.isSelected(row)">
                          </mat-checkbox>
                        </td>
                        <td mat-footer-cell *matFooterCellDef class="tableSnoCol"></td>
                      </ng-container>

                      <ng-container matColumnDef="position">
                        <th mat-header-cell *matHeaderCellDef class="tableSnoCol"> S.No </th>
                        <td mat-cell *matCellDef="let element; let i = index;" class="tableSnoCol">{{i+1}}</td>
                        <td mat-footer-cell *matFooterCellDef class="tableSnoCol"></td>
                      </ng-container>

                      <ng-container matColumnDef="rowModified">
                        <th mat-header-cell *matHeaderCellDef class="tableModCol"> Row Modified </th>
                        <td mat-cell *matCellDef="let element" class="tableModCol">
                          <div *ngIf="element.modified == 'yes'">
                            <mat-chip color="primary">NOT SYNCED</mat-chip>
                          </div>
                          <div *ngIf="element.modified == 'no' || element.modified == '-'">
                            -
                          </div>
                        </td>
                        <td mat-footer-cell *matFooterCellDef class="tableModCol"></td>
                      </ng-container>

                      <ng-container matColumnDef="ingredientName" sticky>
                        <th mat-header-cell *matHeaderCellDef class="custom-header"> Ingredient Name </th>
                        <td mat-cell *matCellDef="let element" class="custom-cell">
                          <div class="d-flex align-items-center">
                            <div class="link" [ngClass]="{
                                'text-danger': element.inventoryStatus === 'discontinued' || element.Discontinued === 'yes',
                                'text-warning': element.isSubRecipe == true
                              }" (click)="editFun(element, addIngredients)" style="width: 200px; margin-right: 5px;">
                              {{ element.ingredientName }}
                              <div class="tableModName" *ngIf="element.modifierName && element.modifierName !== 'NA'">
                                ( {{ element.modifierName }} )
                              </div>
                            </div>
                            <mat-icon *ngIf="element.Discontinued == 'yes'"
                              class="cancelIcon tableIcons">cancel</mat-icon>
                            <mat-icon *ngIf="element.Discontinued == 'no'"
                              class="checkIcon tableIcons">check_circle</mat-icon>
                            <mat-icon *ngIf="element.Discontinued != 'no' && element.Discontinued != 'yes'"
                              class="checkIcon tableIcons">check_circle</mat-icon>
                            <button (click)="deleteFun(element)" *ngIf="element.Discontinued === 'no'" backgroundColor="primary" class="editIconBtn"
                              matTooltip="Delete"><mat-icon class="mt-1">delete</mat-icon></button>
                          </div>
                        </td>
                        <td mat-footer-cell *matFooterCellDef class="custom-footer">Total</td>
                      </ng-container>

                      <ng-container matColumnDef="ingredientCode">
                        <th mat-header-cell *matHeaderCellDef class="custom-header"> Ingredient Code </th>
                        <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.ingredientCode}} </td>
                        <td mat-footer-cell *matFooterCellDef class="custom-footer"></td>
                      </ng-container>

                      <ng-container matColumnDef="modified">
                        <th mat-header-cell *matHeaderCellDef class="custom-header">Modifier</th>
                        <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.isModifier}} </td>
                        <td mat-footer-cell *matFooterCellDef class="custom-footer"></td>
                      </ng-container>

                      <ng-container matColumnDef="uom">
                        <th mat-header-cell *matHeaderCellDef class="custom-header">UOM</th>
                        <td mat-cell *matCellDef="let element" class="custom-cell"> {{element.ConsumptionUOM}} </td>
                        <td mat-footer-cell *matFooterCellDef class="custom-footer"></td>
                      </ng-container>

                      <ng-container matColumnDef="initialWeight">
                        <th mat-header-cell *matHeaderCellDef class="custom-header">Initial Weight</th>
                        <td mat-cell *matCellDef="let element" class="custom-cell">{{getInitialWeight(element)}}</td>
                        <td mat-footer-cell *matFooterCellDef class="custom-footer">{{this.getTotal('InitialWeight')}}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="yield">
                        <th mat-header-cell *matHeaderCellDef style="min-width: 120px !important;">Yield</th>
                        <td mat-cell *matCellDef="let element" style="min-width: 120px !important;">{{getYield(element)}}</td>
                        <td mat-footer-cell *matFooterCellDef style="min-width: 120px !important;"></td>
                      </ng-container>

                      <ng-container matColumnDef="weightInUse">
                        <th mat-header-cell *matHeaderCellDef class="custom-header">Weight In Use (UOM)</th>
                        <td mat-cell *matCellDef="let element" class="custom-cell">{{getWeightInUse(element)}}</td>
                        <td mat-footer-cell *matFooterCellDef class="custom-footer"></td>
                      </ng-container>

                      <ng-container matColumnDef="price" stickyEnd>
                        <th mat-header-cell *matHeaderCellDef class="menuMasterCustomTable">Unit Cost</th>
                        <td mat-cell *matCellDef="let element" class="menuMasterCustomTable">{{this.notify.truncateAndFloor(element.rate)}}</td>
                        <td mat-footer-cell *matFooterCellDef class="menuMasterCustomTable"></td>
                      </ng-container>

                      <ng-container matColumnDef="totalPrice" stickyEnd>
                        <th mat-header-cell *matHeaderCellDef class="menuMasterCustomTable">Final Rate</th>
                        <td mat-cell *matCellDef="let element" class="menuMasterCustomTable">{{this.notify.truncateAndFloor(getFinalRate(element) )}}</td>
                        <td mat-footer-cell *matFooterCellDef class="menuMasterCustomTable">
                          {{this.notify.truncateAndFloor(this.getTotal('finalRate'))}}</td>
                      </ng-container>

                      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
                      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                      <tr mat-footer-row *matFooterRowDef="displayedColumns" class="border-top"></tr>
                    </table>
                  </div>

                  <div *ngIf="!isIngredientsDataReady">
                    <ngx-skeleton-loader count="50" animation="pulse" [theme]="{
                      'border-radius': '4px',
                      'height': '30px',
                      'margin-bottom': '8px',
                      'width': '19%',
                      'margin-right': '1%',
                      'display': 'inline-block',
                      'opacity': '0.85'
                    }"></ngx-skeleton-loader>
                  </div>
                  <div *ngIf="this.dataSource.data.length === 0 && isIngredientsDataReady">
                    <app-empty-state
                      *ngIf="this.showDeleteItems == false"
                      icon="restaurant_menu"
                      title="No Ingredients Found"
                      message="Time to get creative! Add ingredients to create your recipe."
                    ></app-empty-state>

                    <app-empty-state
                      *ngIf="this.showDeleteItems == true"
                      icon="check_circle"
                      title="No Discontinued Items"
                      message="There are no discontinued ingredients to display."
                    ></app-empty-state>
                  </div>
                </div>

              </div>
            </mat-tab>



            <mat-tab label="Settings">
              <ng-template mat-tab-label>Configuration</ng-template>

              <div class="container-fluid pt-4 col-md-6" style="overflow: hidden;">
                <div class="row">
                  <!-- Left Section: Procured Configuration -->
                  <div class="col-md-6 d-flex flex-column">
                    <h4 class="text-center">Procured Configuration</h4>
                    <mat-form-field appearance="outline">
                      <mat-label>Closing UOM</mat-label>
                      <mat-select placeholder="Select Closing UOM" formControlName="closingUOM">
                        <mat-option>
                          <ngx-mat-select-search placeholderLabel="Search..." noEntriesFoundLabel="'Not found'"
                            [formControl]="closingUomFilterCtrl"></ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let data of closingUomData | async" [value]="data">
                          {{ data | uppercase }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Category</mat-label>
                      <input matInput placeholder="Category Name" aria-label="Category" [matAutocomplete]="auto1"
                        formControlName="category" (keyup.enter)="addOptionCat()"
                        oninput="this.value = this.value.toUpperCase()">
                      <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelectedCat($event.option)">
                        <mat-option *ngFor="let cat of catBank | async" [value]="cat">
                          <span>{{ cat }}</span>
                        </mat-option>
                      </mat-autocomplete>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Sub Category</mat-label>
                      <input matInput placeholder="Sub Category" aria-label="SubCategory" [matAutocomplete]="auto2"
                        formControlName="subCategory" (keyup.enter)="addOptionSubCat()"
                        oninput="this.value = this.value.toUpperCase()">
                      <mat-autocomplete #auto2="matAutocomplete" (optionSelected)="optionSelectedSubCat($event.option)">
                        <mat-option *ngFor="let sub of subCatBank | async" [value]="sub">
                          <span>{{ sub }}</span>
                        </mat-option>
                      </mat-autocomplete>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Preparatory Location</mat-label>
                      <mat-select placeholder="Select PreparedAt" formControlName="preparedAt" multiple>
                        <mat-option>
                          <ngx-mat-select-search placeholderLabel="Search..." noEntriesFoundLabel="'Not found'"
                            [formControl]="prepareFilterCtrl"></ngx-mat-select-search>
                        </mat-option>
                        <mat-option (click)="PreparatoryToggleSelectAll()">
                          <mat-icon matSuffix>check_circle</mat-icon>
                          Select All / Deselect All
                        </mat-option>
                        <!-- <mat-option *ngFor="let restaurant of prepareData | async" [value]="restaurant">
                          {{ restaurant | uppercase }}
                        </mat-option> -->

                        <mat-option *ngFor="let restaurant of prepareData | async"
                          [value]="restaurant"
                          [disabled]="discontinuedPreLocData.includes(restaurant)"
                          [ngClass]="{'disabled-option': this.defaultPreLocData.includes(restaurant) || discontinuedPreLocData.includes(restaurant)}">
                          <span [ngClass]="{'disabledSelect': discontinuedPreLocData.includes(restaurant)}">{{ restaurant | uppercase }}</span>
                          <mat-icon *ngIf="this.defaultPreLocData.includes(restaurant) && !this.discontinuedPreLocData.includes(restaurant)"
                            class="deleteIconForMatSelect" matTooltip="discontinue"
                            (click)="onDelete(restaurant, $event, 'preparatoryLocation','null')"
                            [ngClass]="{'clickable': discontinuedPreLocData.includes(restaurant)}">
                            delete
                          </mat-icon>
                          <mat-icon *ngIf="this.discontinuedPreLocData.includes(restaurant)"
                            class="deleteIconForMatSelect" matTooltip="restore"
                            (click)="onRestore(restaurant, $event, 'preparatoryLocation','null')">
                            settings_backup_restore</mat-icon>
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>

                  <!-- Right Section: Sales Configuration -->
                  <div class="col-md-6 d-flex flex-column">
                    <h4 class="text-center">Sales Configuration</h4>

                    <mat-form-field appearance="outline">
                      <mat-label>Serving Size</mat-label>
                      <mat-select placeholder="Select Serving Size" formControlName="servingSize" multiple>
                        <mat-option>
                          <ngx-mat-select-search placeholderLabel="Search..." noEntriesFoundLabel="'Not found'"
                            [formControl]="servingFilterCtrl"></ngx-mat-select-search>
                        </mat-option>
                        <mat-option (click)="servingToggleSelectAll()">
                          <mat-icon matSuffix>check_circle</mat-icon>
                          Select All / Deselect All
                        </mat-option>
                        <mat-option *ngFor="let size of servingData | async" [value]="size['Serving Size']">
                          {{ size['Serving Size'] }}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                      <mat-label>Sales Outlet</mat-label>
                      <mat-select placeholder="Select Outlet" formControlName="usedOutlet" multiple
                        (selectionChange)="locationChange($event.value)">
                        <mat-option>
                          <ngx-mat-select-search placeholderLabel="Search..." noEntriesFoundLabel="'Not found'"
                            [formControl]="outletFilterCtrl"></ngx-mat-select-search>
                        </mat-option>
                        <mat-option (click)="salesOutletToggleSelectAll()">
                          <mat-icon matSuffix>check_circle</mat-icon>
                          Select All / Deselect All
                        </mat-option>
                        <!-- <mat-option *ngFor="let restaurant of outletData | async" [value]="restaurant">
                          {{ restaurant  | uppercase }}
                        </mat-option> -->

                        <mat-option *ngFor="let restaurant of outletData | async"
                        [value]="restaurant"
                        [disabled]="discontinuedOutletData.includes(restaurant)"
                        [ngClass]="{'disabled-option': this.defaultOutletData.includes(restaurant) || discontinuedOutletData.includes(restaurant)}">
                        <span [ngClass]="{'disabledSelect': discontinuedOutletData.includes(restaurant)}">{{ restaurant | uppercase  }}</span>
                        <mat-icon *ngIf="this.defaultOutletData.includes(restaurant) && !this.discontinuedOutletData.includes(restaurant)"
                          class="deleteIconForMatSelect" matTooltip="discontinue"
                          (click)="onDelete(restaurant, $event, 'usedOutlet','null')"
                          [ngClass]="{'clickable': discontinuedOutletData.includes(restaurant)}">
                          delete
                        </mat-icon>
                        <mat-icon *ngIf="this.discontinuedOutletData.includes(restaurant)"
                          class="deleteIconForMatSelect" matTooltip="restore"
                          (click)="onRestore(restaurant, $event, 'usedOutlet','null')">
                          settings_backup_restore</mat-icon>
                      </mat-option>


                      </mat-select>
                    </mat-form-field>

                    <mat-form-field appearance="outline"
                      [ngClass]="{'highlighted-input': !this.createRecipeForm.value.usedOutlet}">
                      <mat-label>Used WorkArea</mat-label>
                      <mat-select placeholder="Select WorkArea" formControlName="usedWorkArea" multiple>
                        <mat-option>
                          <ngx-mat-select-search placeholderLabel="Search..." noEntriesFoundLabel="'Not found'"
                            [formControl]="usedWorkAreaFilterCtrl"></ngx-mat-select-search>
                        </mat-option>
                        <mat-option (click)="toggleSelectAllUsedWorkArea()">
                          <mat-icon matSuffix>check_circle</mat-icon>
                          Select All / Deselect All
                        </mat-option>
                        <mat-optgroup *ngFor="let group of workAreas | async"
                          [label]="group.restaurantIdOld.split('@')[1]" [disabled]="group.disabled">
                          <!-- <mat-option *ngFor="let data of group.workAreas" [value]="data"
                            [disabled]="!this.createRecipeForm.value.usedOutlet">
                            {{ data }}
                          </mat-option> -->
                          <mat-option *ngFor="let data of group.workAreas" [value]="data" [disabled]="isOptionDisabled(data , group)"
                          [ngClass]="{'disabled-option': isCheckOptionDisabled(data , group) || this.defaultUsedWorkAreaData.includes(data)}">

                          <span [ngClass]="{'disabledSelect': isOptionDisabled(data , group) || group.disabled}">{{ data | uppercase }}</span>
                          <mat-icon *ngIf="!discontinuedOutletData.includes(group.branchName) && this.defaultUsedWorkAreaData.includes(data) && !isOptionDisabled(data , group)"
                          class="deleteIconForMatSelect" matTooltip="discontinue"
                          (click)="onDelete(data, $event, 'usedWorkArea' , group)"
                          [ngClass]="{'clickable': discontinuedUsedWorkAreaData.includes(data)}">
                          delete
                        </mat-icon>
                      <mat-icon *ngIf="isOptionDisabled(data , group) && !group.disabled"
                        class="deleteIconForMatSelect" matTooltip="restore" (click)="onRestore(data, $event, 'usedWorkArea',group)">
                        settings_backup_restore
                      </mat-icon>
                        </mat-option>
                        </mat-optgroup>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="llm">
              <ng-template mat-tab-label>
                <div style="display: flex; align-items: center;">
                  <span>Recipe Insights</span>
                  <mat-icon style="font-size: 1rem; color: #42a5f5; margin-left: 8px;">smart_toy</mat-icon>
                  <span style="font-size: 0.8rem; color: #42a5f5; font-weight: 600; margin-left: 4px;">AI Powered</span>
                </div>
              </ng-template>
              <div class="row m-2" *ngIf="demo2tab.selectedIndex == 2">
                <app-recipe-llm [master]="this.createRecipeForm.value" [child]="dataSource.data"></app-recipe-llm>
              </div>
            </mat-tab>

          </mat-tab-group>
        </mat-card>

        <div *ngIf="!isEdit && demo2tab.selectedIndex == 0 || this.dialogData.createNew" class="discontinue-row m-1">
          <label class="discontinue-label">Do you want to discontinue?</label>
          <mat-radio-group formControlName="Discontinued" aria-labelledby="example-radio-group-label">
            <mat-radio-button value="yes">Yes</mat-radio-button>
            <mat-radio-button value="no">No</mat-radio-button>
          </mat-radio-group>
        </div>
      </form>
    </div>
  </div>


  <ng-template #addSteps>
    <div class="closeBtn">
      <mat-icon (click)="closeAddStepDialog()" matTooltip="Close" class="closeBtnIcon">close</mat-icon>
    </div>
    <div class="m-4">
      <div class="mb-4 d-flex justify-content-between">
        <h1> <b>Add Steps</b> </h1>
        <button mat-flat-button type="button" color="accent" *ngIf="index === ''" (click)="createSteps()"
          matTooltip="Create"><mat-icon>add_circle</mat-icon>Create</button>
        <button mat-flat-button type="button" color="accent" *ngIf="index !== ''" (click)="updateSteps()"
          matTooltip="Update">
          Update
        </button>
      </div>
    </div>
  </ng-template>

  <ng-template #addIngredients>
    <div class="closeBtn">
      <mat-icon (click)="closeIngredientDialog()" matTooltip="Close" class="closeBtnIcon">close</mat-icon>
    </div>

    <div class="m-4">

      <div style="float: right; padding-top: 0.5rem; margin-right: 15px;">
        <button *ngIf="isEdit" mat-raised-button type="button" color="accent" class="topHeadingBtns"
          (click)="addNewSubRecipeRecipe()" matTooltip="Create"> <mat-icon>add_circle</mat-icon>Create</button>
        <button *ngIf="!isEdit" mat-raised-button type="button" color="accent" class="topHeadingBtns"
          (click)="editExistingSubRecipeRecipe()" matTooltip="Update" [disabled]="loadMrBtn">
          Update
        </button>
      </div>

      <div class="bottomTitles p-3 mb-3">
        <!-- <div> -->
          <!-- <mat-icon class="instructionsIcon"> local_grocery_store</mat-icon> class="col d-flex flex-wrap align-items-center"-->
          <span>Add Ingredient</span>
        <!-- </div> -->

      </div>

      <form [formGroup]="ingredientsForm">
        <div class="row">
          <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Ingredient Name</mat-label>
              <input matInput placeholder="Ingredient Name" formControlName="ingredientName" [readonly]="isReadOnly" />
            </mat-form-field>
          </div>

          <!-- <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Ingredient Code</mat-label>
              <input matInput placeholder="Ingredient Code" formControlName="ingredientCode" [readonly]="isReadOnly" />
            </mat-form-field>
          </div> -->

          <!-- <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Category</mat-label>
              <input matInput placeholder="Category" formControlName="category" [readonly]="isReadOnly" />
            </mat-form-field>
          </div> -->

          <!-- <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Sub-Category</mat-label>
              <input matInput placeholder="Sub-Category" formControlName="subCategory" [readonly]="isReadOnly" />
            </mat-form-field>
          </div> -->

          <div>
            <mat-form-field appearance="outline">
              <mat-label>Is Modifier</mat-label>
              <mat-select placeholder="select" formControlName="isModifier"
                (selectionChange)="modifierChange($event.value)">
                <mat-option *ngFor="let val of modified" [value]="val">{{val | titlecase}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div *ngIf="ingredientsForm.value.isModifier === 'Yes'">
            <mat-form-field appearance="outline">
              <mat-label>Modifier Name</mat-label>
              <mat-select placeholder="Modifier Name" formControlName="modifierName">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                    [formControl]="modifierFilterCtrl"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let restaurant of modifierData | async"
                  [value]="restaurant">{{restaurant}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>UOM</mat-label>
              <mat-select placeholder="select" formControlName="ConsumptionUOM" (selectionChange)="uomChange($event)">
                <mat-option *ngFor="let val of ingredientClosingUOM" [value]="val"
                  [disabled]="!isOptionAccessible(val)">{{val | uppercase}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div> -->

          <div *ngIf="this.ingredientsForm.value.ConsumptionUOM  === 'PORTION'">
            <mat-form-field appearance="outline">
              <mat-label>Portion</mat-label>
              <input matInput type="number" placeholder="Portion" class="outline" formControlName="portionCount"
                (keyup)="convertPortionToUOM($event)" (focus)="focusFunction('portionCount')"
                (focusout)="focusOutFunction('portionCount')" />
            </mat-form-field>
          </div>

          <!-- <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Initial Weight</mat-label>
              <input matInput type="number" placeholder="initial Weight" class="outline" formControlName="InitialWeight"
                (focus)="focusFunction('InitialWeight')" (focusout)="focusOutFunction('InitialWeight')" readonly />
            </mat-form-field>
          </div> -->

          <div>
            <mat-form-field appearance="outline">
              <mat-label>Yield</mat-label>
              <input matInput type="number" placeholder="Yield" class="outline" formControlName="Yield"
                (keyup)="setInitialWeight()" (focus)="focusFunction('Yield')" (focusout)="focusOutFunction('Yield')" />
            </mat-form-field>
          </div>

          <div>
            <mat-form-field appearance="outline"
              [ngClass]="{'highlighted-input': this.ingredientsForm.value.ConsumptionUOM  === 'PORTION'}">
              <mat-label>Weight In Use (UOM)</mat-label>
              <input matInput type="number" placeholder="Weight In Use" class="outline" formControlName="weightInUse"
                (keyup)="makeTotalPrice();setInitialWeight()" (focus)="focusFunction('weightInUse')"
                (focusout)="focusOutFunction('weightInUse')"
                [readonly]="this.ingredientsForm.value.ConsumptionUOM  === 'PORTION'" />
            </mat-form-field>
          </div>

          <div>
            <mat-form-field appearance="outline">
              <mat-label>Price ₹</mat-label>
              <input matInput type="text" placeholder="Price ₹" class="outline" formControlName="rate"
                (keyup)="makeTotalPrice()" />
            </mat-form-field>
          </div>

          <!-- <div>
            <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
              <mat-label>Total Price ₹</mat-label>
              <input matInput type="number" placeholder="Total Price ₹" class="outline" formControlName="finalRate"
                [readonly]="isReadOnly" />
            </mat-form-field>
          </div> -->
          <div>
            <div class="col" *ngIf="!isEdit">
              <label>Do you want to discontinue?</label>
              <mat-radio-group formControlName="Discontinued" aria-labelledby="example-radio-group-label">
                <mat-radio-button value="yes">Yes</mat-radio-button>
                <mat-radio-button value="no">No</mat-radio-button>
              </mat-radio-group>
            </div>
          </div>
        </div>
      </form>
    </div>
  </ng-template>

  <ng-template #createIngredient>
    <div class="closeBtn">
      <mat-icon (click)="closeCreateIngredientDialog()" matTooltip="Close" class="closeBtnIcon">close</mat-icon>
    </div>
    <div class="p-4 smallDialog">
      <div class="bottomTitles p-2 my-3 d-flex flex-wrap align-items-center">
        Add Ingredients
      </div>
      <div class="m-1">
        <mat-form-field appearance="outline">
          <mat-label>Search..</mat-label>
          <mat-select [formControl]="itemNameControlIngredients">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                [formControl]="nameOptionsFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let item of nameOptions | async" [value]="item['itemCode']"
              [disabled]="item['status'] === 'discontinued'">
              {{ item['itemName'] | uppercase}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div>
          {{this.questionIngredients}}
        </div>
      </div>
      <div class="text-end">
        <button (click)="addOptionIngredients(addIngredients)" mat-raised-button color="accent"
          [disabled]="!itemNameControlIngredients.value" matTooltip="Add">
          <mat-icon>library_add</mat-icon> Add
        </button>
      </div>
    </div>
  </ng-template>


  <ng-template #openCostDialog>
    <div class="closeBtn">
      <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeInfoDialog()">close</mat-icon>
    </div>

    <div class="registration-form mx-1 px-3">
      <div class="text-center my-2 p-2 bottomTitles">
        <span>Detailed Info</span>
      </div>
      <div class="mb-2">
        <div class="search-container mb-2">
          <input type="text" class="search-input" placeholder="Search..." (keyup)="applyFilter($event)">
          <mat-icon matSuffix class="search-icon">search</mat-icon>
        </div>

        <table mat-table [dataSource]="costDataSource">
          <ng-container matColumnDef="servingSize">
            <th mat-header-cell *matHeaderCellDef style="padding: 10px 16px"> Serving Size </th>
            <td mat-cell *matCellDef="let element" style="padding: 10px 16px"> {{element['Serving size']}} </td>
          </ng-container>

          <ng-container matColumnDef="preparationWeight">
            <th mat-header-cell *matHeaderCellDef style="padding: 10px 16px"> Weight </th>
            <td mat-cell *matCellDef="let element" style="padding: 10px 16px"> {{element['Preparation Weight']}} </td>
          </ng-container>

          <ng-container matColumnDef="costOfProduction">
            <th mat-header-cell *matHeaderCellDef style="padding: 10px 16px"> Cost of Production </th>
            <td mat-cell *matCellDef="let element" style="padding: 10px 16px"> {{element['cost of production']}} </td>
          </ng-container>

          <ng-container matColumnDef="sellingPrice">
            <th mat-header-cell *matHeaderCellDef style="padding: 10px 16px"> Selling Price </th>
            <td mat-cell *matCellDef="let element" style="padding: 10px 16px"> {{element['selling price']}} </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="costDisplayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: costDisplayedColumns;"></tr>
        </table>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #openSalesDialog>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeInfoDialog()">close</mat-icon>
  </div>

  <div class="registration-form mx-1 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Detailed Selling Prices</span>
    </div>
    <div class="portion-info">
      <!-- <div> -->
        <!-- <p><strong>Recipe Name:</strong> {{ this.createRecipeForm.value.recipeName }}</p> -->
        <!-- <p> <strong>Price:</strong>{{ this.menuCost }}</p>
      </div>
      <div *ngFor="let item of sellingPriceArray"> -->
        <!-- <p><strong>Modifier Name:</strong> {{ item.ingredientName }}</p> -->
        <!-- <p><strong>Price:</strong> {{ item.modifierCost }}</p>
      </div> -->

      <table class="info-table">
        <tbody>
          <tr>
            <td class="info-key"><strong>Price</strong></td>
            <td class="info-value">{{ this.menuCost }}</td>
            <td class="info-unit">Rs</td>
          </tr>
          <tr *ngFor="let item of sellingPriceArray">
            <td class="info-key"><strong>Price</strong></td>
            <td class="info-value">{{ item.modifierCost }}</td>
            <td class="info-unit">Rs</td>
          </tr>
          <tr>
            <td class="info-key"><strong>Grand Total</strong></td>
            <td class="info-value">{{ this.overAllSellingPrice }}</td>
            <td class="info-unit">Rs</td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- <div>
      <p><strong>Grand Total:</strong> {{ this.overAllSellingPrice}}</p>
    </div> -->
  </div>
</ng-template>


<ng-template #openPortionDialog>
  <div class="dialog-container">
    <div class="close-btn">
      <button mat-icon-button class="close-btn-icon" aria-label="Close" matTooltip="close" (click)="closeInfoDialog()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="registration-form mx-1 px-3">
      <div class="text-center my-2 p-2 bottom-titles">
        <span>Detailed Portion Info</span>
      </div>
      <div class="portion-info">
        <table class="info-table">
          <tbody>
            <tr>
              <td class="info-key"><strong>Gross Weight</strong></td>
              <td class="info-value">{{ this.notify.truncateAndFloor(this.getTotal('InitialWeight')) }}</td>
              <td class="info-unit">GM/ML</td>
            </tr>
            <tr>
              <td class="info-key"><strong>Portion Weight</strong></td>
              <td class="info-value">{{ this.notify.truncateAndFloor((getTotal('InitialWeight') * (1 / createRecipeForm.get('portion').value)) ) }}</td>
              <td class="info-unit">GM/ML</td>
            </tr>
            <br>
            <tr>
              <td class="info-key"><strong>Gross Cost</strong></td>
              <td class="info-value">{{ getTotal('finalRate') }}</td>
              <td class="info-unit">Rs</td>
            </tr>
            <tr>
              <td class="info-key"><strong>Portion Cost</strong></td>
              <td class="info-value">{{ this.notify.truncateAndFloor((getTotal('finalRate') * (1 / createRecipeForm.get('portion').value))) }}</td>
              <td class="info-unit">Rs</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #openRenameDialog>
  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="close" (click)="closeInfoDialog()">close</mat-icon>
  </div>
  <div class="registration-form mx-1 px-3">
    <!-- <form #recipeForm="ngForm" (ngSubmit)="submitForm(recipeForm.value)">
      <mat-form-field>
        <mat-label>Recipe Name</mat-label>
        <mat-select name="recipeName" ngModel required placeholder="Recipe Name" [(ngModel)]="currentItemName"
          (selectionChange)="recipeSelection($event.value)">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
            [formControl]="recipeOptionsFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          recipeOptions | async posItems
          <mat-option *ngFor="let recipe of recipeOptions | async " [value]="recipe.itemName">{{ recipe.itemName }}</mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field>
        <mat-label>Recipe Code</mat-label>
        <mat-select name="recipeCode" ngModel required placeholder="Enter Recipe Code" [ngModel]="currentItemCode"
          disabled>
          <mat-option *ngFor="let recipe of posItems" [value]="recipe.itemCode">{{ recipe.itemCode }}</mat-option>
        </mat-select>
      </mat-form-field>
      <div class="text-end">
        <button type="submit" mat-raised-button color="accent" [disabled]="!recipeForm.valid" matTooltip="Update">
          Update
        </button>
      </div>
    </form> -->


    <form [formGroup]="tempRecipeForm" (ngSubmit)="submitForm()">
      <mat-form-field>
        <mat-label>Recipe Name</mat-label>
        <mat-select formControlName="recipeName" required placeholder="Recipe Name" (selectionChange)="recipeSelection($event.value)">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
            [formControl]="recipeOptionsFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let recipe of recipeOptions | async" [value]="recipe.itemName">{{ recipe.itemName }}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field>
        <mat-label>Recipe Code</mat-label>
        <mat-select formControlName="recipeCode" required placeholder="Enter Recipe Code" [disabled]="true">
          <mat-option *ngFor="let recipe of posItems" [value]="recipe.itemCode">{{ recipe.itemCode }}</mat-option>
        </mat-select>
      </mat-form-field>

      <div class="text-end">
        <button type="submit" mat-raised-button color="accent" [disabled]="!tempRecipeForm.valid" matTooltip="Update">
          Update
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #openDeleteDialog>
  <div class="dialog-container1" style="padding: 10px;">
    <div>
      <button mat-icon-button class="close-btn-icon" aria-label="Close" matTooltip="close" (click)="closeInfoDialog()"
        style="float: right;">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="registration-form mx-1 px-3">
      <div class="text-center my-2 p-2 bottom-titles">
        <span>Confirm Ingredient Deletion</span>
      </div>
      <div class="portion-info1">
        <div class="mb-3" >
          Deleting this ingredient is permanent and will impact past records. Alternatively, you can choose to
          discontinue it </div>
        <div class="d-flex justify-content-center gap-3 m-2">
          <button mat-raised-button class="deleteBtn" matTooltip="delete" (click)="deleteData()">
            Yes, Delete
          </button>
          <button mat-raised-button class="discBtn" matTooltip="discontinue" (click)="discontinueData()">
            Just, Discontinue
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #discontinuedSelectDialog>
  <div class="registration-form px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Discontinued Location</span>
    </div>
    <div class="m-3 infoText text-center">
      Would you like to discontinue {{selectedData}} ?
    </div>
    <div class="text-end m-2">
      <button mat-raised-button (click)="discontinuedSelectData()" color="accent" matTooltip="Update" class="m-1">
        Yes</button>
      <button (click)="closeDiscontinuedDialog()" mat-raised-button matTooltip="close" class="m-1">
        No</button>
    </div>
  </div>
</ng-template>

<ng-template #setLocationDialog>
  <div class="d-flex justify-content-end">
    <button mat-icon-button class="close-btn-icon" aria-label="Close" matTooltip="close" (click)="closeLocDialog()">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <div class="registration-form px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Location</span>
    </div>
    <div class="m-3 infoText text-center">
      <mat-form-field appearance="outline" class="topInuptFields" style="width: 220px !important;" *ngIf="this.dialogLocation == 'CostOfProduction'">
        <mat-label>Production Price Locations</mat-label>
        <mat-select placeholder="Select Branch" [formControl]="globalLocation" (selectionChange)="getMenuRecipes()">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
              [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let rest of vendorsBanks | async" [value]="rest" >
            {{ rest.branchName | uppercase}}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="topInuptFields" style="width: 220px !important; margin-left: 10px;" *ngIf="this.dialogLocation == 'SellingPrice'">
        <mat-label>Selling Price Locations</mat-label>
        <mat-select placeholder="Select Branch" [formControl]="sellingLocation" (selectionChange)="getDetailedPriceList($event.value)">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
              [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let rest of vendorsBanks | async" [value]="rest"
          [disabled]="!checkNewIdsInAllData(rest.restaurantIdOld)" >
            <span>{{ rest.branchName | uppercase }}</span>
            <mat-icon *ngIf="!checkNewIdsInAllData(rest.restaurantIdOld)"
            class="deleteIconForMatSelect" matTooltip="add priceTier"
            (click)="addSellingPrice()">
            add</mat-icon>
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
</ng-template>
