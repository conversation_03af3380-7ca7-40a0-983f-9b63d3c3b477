  <div class="closeBtn">
    <mat-icon class="closeBtnIcon" matTooltip="Close" (click)="close()">close</mat-icon>
  </div>

  <div class="registration-form py-2 px-3">
    <div class="text-center my-2 p-2 bottomTitles">
      <span>Mapping Form</span>
    </div>

    <div class="d-flex justify-content-end flex-wrap mb-3" style="gap: 0.5rem">
      <button  (click)="create()" mat-raised-button
        color="accent" [disabled]="!isDone"  matTooltip="Create">
        Update
      </button>
    </div>
    <!-- [disabled]="this.menuMappingForm.value['restaurantId'] = ''" class="floatRightBtn" -->
    <form [formGroup]="menuMappingForm">
      <div class="row">
        <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
          <mat-label>Recipe Code</mat-label>
          <input formControlName="menuItemCode" matInput placeholder="Recipe Code" [readonly]="isReadOnly" />
        </mat-form-field>

        <mat-form-field appearance="outline" [ngClass]="{'highlighted-input': isReadOnly}">
          <mat-label>.Recipe Name</mat-label>
          <input formControlName="menuItemName" matInput placeholder="Recipe Name" [readonly]="isReadOnly" />
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Location</mat-label>
          <mat-select formControlName="restaurantId" (selectionChange)="selectRestaurant($event.value)">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                [formControl]="locationFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let rest of locationData | async" [value]="rest.restaurantIdOld">
              {{ rest.branchName | uppercase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <div *ngIf="spinner" class="spinner-container">
          <mat-spinner diameter="30"></mat-spinner>
        </div>

        <mat-form-field appearance="outline" *ngIf = "false">
          <mat-label>Store Id</mat-label>
          <input formControlName="storeId" matInput placeholder="Store Id" readonly />
        </mat-form-field>
        <div *ngIf = "filteredFloorList.length > 0 && !spinner" >
          <mat-form-field appearance="fill">
            <mat-label>Search Floors</mat-label>
            <input matInput placeholder="Type to filter floors..." (input)="applyFilter1($event)">
          </mat-form-field>
        </div>

        <div *ngIf = "filteredFloorList.length > 0 && !spinner">
          <form [formGroup]="floorForm">
            <mat-accordion multi="true">
              <mat-expansion-panel *ngFor="let floor of filteredFloorList">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    {{ floor.section }}
                  </mat-panel-title>
                </mat-expansion-panel-header>
                <mat-form-field appearance="outline">
                  <mat-label>Select Area in {{ floor.section }}</mat-label>
                  <mat-select formControlName="{{ floor.section }}" (selectionChange)="selectedFloor($event.value, floor)">
                    <mat-option *ngFor="let area of floor.workArea" [value]="area">
                      {{ area | uppercase}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </mat-expansion-panel>
            </mat-accordion>
          </form>
        </div>

        <mat-form-field appearance="outline" *ngIf = "false">
          <mat-label>Work Area</mat-label>
          <mat-select formControlName="workArea">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="search..." noEntriesFoundLabel="'not found'"
                [formControl]="workAreaFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let workArea of workAreaNames |async" [value]="workArea">
              {{ workArea | uppercase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" *ngIf="false">
          <mat-label>Floor No.</mat-label>
          <input formControlName="floorNo" matInput placeholder="Floor No." />
        </mat-form-field>
      </div>
    </form>
  </div>

  <ng-template #openActionDialog>
    <div class="dialog-container1" style="padding: 10px;">
      <div>
        <button mat-icon-button class="close-btn-icon" aria-label="Close" matTooltip="close" (click)="closeInfoDialog()"
          style="float: right;">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="registration-form mx-1 py-2 px-3">
        <div class="text-center my-2 p-2 bottom-titles">
          <span>Apply</span>
        </div>
        <div class="portion-info1">
          <div class="mb-3" style="font-size: medium;font-weight: 600;">
            Apply this work area to other sections as well?
          </div>
          <div class="d-flex justify-content-center gap-3 m-2">
            <button mat-raised-button class="discBtn" matTooltip="proceed" (click)="proceed()">
              Yes,Proceed
            </button>
            <button mat-raised-button class="deleteBtn" matTooltip="skip" (click)="skipProcess()">
              No, Skip
            </button>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
