import { Injectable } from '@angular/core';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest
} from '@angular/common/http';
import { Observable, catchError, map, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { NotificationService } from '../services/notification.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  engineToken: string = 'Bearer eyJhbGciOiJIUzI1NiJ9.InJwYV9ib3Qi.baB1eGAWiYsC58FAgKqnQ52RYon50cs6wygMvmPS6PM';

  constructor(
    private auth: AuthService,
    private router: Router,
    private utils: NotificationService
  ) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (this.auth.getCurrentUser()) {
      const parsedUrl = new URL(req.url);
      const port = parsedUrl.port;
      const token = (port === '5000') ? this.engineToken : this.auth.getCurrentUser().token;

      if (req.url !== "https://api.bigdatacloud.net/data/client-ip") {
        req = req.clone({
          setHeaders: {
            'Content-Type': 'application/json; charset=utf-8',
            'Accept': 'application/json',
            'Authorization': `${token}`,
          },
        });
      }
    }

    return next.handle(req).pipe(
      map(response => {
        const res: any = response;
        if (res.body && res.body.newObj) {
          if (Object.keys(res.body.newObj.obj).length === 0 && Object.keys(res.body.newObj).length === 0) {
            throwError(() => new Error('Empty response'));
          }
        }
        return res;
      }),
      catchError((error: HttpErrorResponse) => {
        let err: any = {};

        // ✅ First check for .status === 0
        if (error.status === 0) {
          err.msg = !navigator.onLine
            ? 'No internet. Please check your connection and try again.'
            : 'Server is unreachable. Please try again later.';
        }

        // ✅ Then handle known HTTP errors
        else {
          err.status = error.status;

          switch (err.status) {
            case 403:
              err.msg = 'Access Forbidden';
              this.router.navigate(['/dashboard/unauthorized'], { queryParams: { key: false, message: "You're not on the authorized IP. Contact the responsible person to get it authorized" } });
              break;
            case 400:
              err.msg = 'Session already taken';
              this.router.navigate(['/dashboard/unauthorized'], { queryParams: { key: true, message: error.error.message } });
              break;
            case 401:
              err.msg = 'Your session has expired. Please Login again'
              // this.auth.logout();
              this.router.navigate(['/signin']);
              break;
            case 404:
              err.msg = 'Invalid credentials';
              // this.auth.logout();
              this.router.navigate(['/signin']);
              break;
            case 500:
              err.msg = 'We seem to be having trouble right now. Please try again after sometime';
              break;
            case 0:
              err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';
              break;
            case 409:
              err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';
              break;
            case undefined:
              err.msg = 'No data found for the current selections';
              break;
            default:
              err.msg = 'Internal error';
          }
        }

        this.utils.snackBarShowError(err.msg);
        return throwError(() => new Error(err.msg));
      })
    );
  }
}
