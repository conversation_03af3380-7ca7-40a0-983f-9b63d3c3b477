


<div class="fixed-size-container">
    <mat-card class="gradient-card" [ngClass]="gradientClass">
      <mat-card-header>
        <mat-icon class="icon-display" mat-card-avatar>person_pin</mat-icon>
        <mat-card-title>{{title}}</mat-card-title>
        <mat-card-subtitle>{{subtitle}}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="content">
          <ng-content></ng-content>
        </div>
      </mat-card-content>
  </mat-card>

  <mat-card class="gradient-card" [ngClass]="gradientClass">
      <mat-card-header>
        <mat-icon class="icon-display" mat-card-avatar>access_time</mat-icon>
        <mat-card-title>{{title}}</mat-card-title>
        <mat-card-subtitle>{{subtitle}}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="content">
          <ng-content></ng-content>
        </div>
      </mat-card-content>
  </mat-card>


  <mat-card class="gradient-card" [ngClass]="gradientClass">
      <mat-card-header>
        <mat-icon class="icon-display" mat-card-avatar>timelapse</mat-icon>
        <mat-card-title>{{title}}</mat-card-title>
        <mat-card-subtitle>{{subtitle}}</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="content">
          <ng-content></ng-content>
        </div>
      </mat-card-content>
    </mat-card>
</div>

<!-- 
 <div class="fixed-size-container">
  <div class="fixed-size">
    <h1>1</h1>
  </div>
   <div class="fixed-size">
    <h1>2</h1>
  </div>
   <div class="fixed-size">
    <h1>3</h1>
  </div>
   <div class="fixed-size">
    <h1>4</h1>
  </div>
   <div class="fixed-size">
    <h1>5</h1>
  </div>
</div> -->
