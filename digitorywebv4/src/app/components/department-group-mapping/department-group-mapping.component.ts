import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { CdkDragDrop, DragDropModule, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Services
import { DepartmentService, DepartmentGroup, GroupMappingConfig } from '../../services/department.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

// Interfaces
export interface Department {
  id: string;
  name: string;
  code?: string;
}

export interface Category {
  name: string;
  type: string;
}

export interface WorkArea {
  name: string;
  type: string;
}

@Component({
  selector: 'app-department-group-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatSnackBarModule,
    MatButtonModule,
    MatInputModule,
    MatChipsModule,
    MatCardModule,
    DragDropModule
  ],
  templateUrl: './department-group-mapping.component.html',
  styleUrls: ['./department-group-mapping.component.scss']
})
export class DepartmentGroupMappingComponent implements OnInit, OnDestroy {
  @Input() tenantId: string = '';
  @Input() showAsDialog: boolean = false;
  @Input() autoEmit: boolean = true;
  @Output() mappingsChanged = new EventEmitter<DepartmentGroup[]>();
  @Output() closeDialog = new EventEmitter<void>();

  private destroy$ = new Subject<void>();

  // Data
  departments: Department[] = [];
  categories: Category[] = [];
  workAreas: WorkArea[] = [];
  
  // State
  isLoading = true;
  isSaving = false;
  
  // Department Groups
  departmentGroups: DepartmentGroup[] = [];
  ungroupedDepartments: string[] = [];
  
  // Forms
  groupForm: FormGroup;
  newGroupNameCtrl = new FormControl('', [Validators.required, Validators.minLength(2)]);
  
  // Current group being edited
  currentGroupIndex: number = -1;
  
  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.groupForm = this.fb.group({
      groups: this.fb.array([])
    });
  }

  get groupsFormArray(): FormArray {
    return this.groupForm.get('groups') as FormArray;
  }

  private async loadInitialData(): Promise<void> {
    try {
      this.isLoading = true;
      
      // Load all required data in parallel
      await Promise.all([
        this.loadDepartments(),
        this.loadCategories(),
        this.loadWorkAreas(),
        this.loadExistingGroupMappings()
      ]);
      
      this.initializeUngroupedDepartments();
      this.buildGroupForm();
      
    } catch (error) {
      console.error('Error loading initial data:', error);
      this.showError('Failed to load initial data');
    } finally {
      this.isLoading = false;
      this.cdr.detectChanges();
    }
  }

  private async loadDepartments(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartments(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (departments) => {
            this.departments = departments.map((dept: any) => ({
              id: dept.id || dept._id,
              name: dept.name,
              code: dept.code
            }));
            resolve();
          },
          error: (error) => {
            console.error('Error loading departments:', error);
            reject(error);
          }
        });
    });
  }

  private async loadCategories(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.smartDashboardService.getCategories(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response.success && response.data) {
              this.categories = response.data.map((cat: any) => ({
                name: cat.name,
                type: cat.type || 'inventory'
              }));
            }
            resolve();
          },
          error: (error) => {
            console.error('Error loading categories:', error);
            reject(error);
          }
        });
    });
  }

  private async loadWorkAreas(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Get work areas from user's restaurant access data
        const user = this.authService.getCurrentUser();
        const workAreaSet = new Set<string>();

        if (user && user.restaurantAccess) {
          user.restaurantAccess.forEach((restaurant: any) => {
            if (restaurant.workAreas && restaurant.workAreas.length > 0) {
              restaurant.workAreas.forEach((wa: string) => workAreaSet.add(wa));
            }
          });
        }

        this.workAreas = Array.from(workAreaSet).map(name => ({
          name: name,
          type: 'physical'
        }));

        resolve();
      } catch (error) {
        console.error('Error loading work areas:', error);
        this.workAreas = [];
        reject(error);
      }
    });
  }

  private async loadExistingGroupMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getGroupMappingConfig(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (config) => {
            this.departmentGroups = config.departmentGroups || [];
            this.ungroupedDepartments = config.ungroupedDepartments || [];
            resolve();
          },
          error: (error) => {
            console.error('Error loading existing group mappings:', error);
            // Don't reject - just continue with empty mappings
            this.departmentGroups = [];
            this.ungroupedDepartments = [];
            resolve();
          }
        });
    });
  }

  private initializeUngroupedDepartments(): void {
    if (this.ungroupedDepartments.length === 0) {
      // If no existing configuration, all departments are ungrouped
      this.ungroupedDepartments = this.departments.map(dept => dept.id);
    }
  }

  private buildGroupForm(): void {
    const groupsArray = this.fb.array(
      this.departmentGroups.map(group => this.createGroupFormGroup(group))
    );

    this.groupForm.setControl('groups', groupsArray);
  }

  private createGroupFormGroup(group: DepartmentGroup): FormGroup {
    return this.fb.group({
      id: [group.id],
      name: [group.name, [Validators.required, Validators.minLength(2)]],
      departments: [group.departments],
      categories: [group.categories],
      workAreas: [group.workAreas]
    });
  }

  // Drag and drop handlers
  onDepartmentDrop(event: CdkDragDrop<string[]>, groupIndex: number): void {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      
      // Update the form
      const groupControl = this.groupsFormArray.at(groupIndex);
      groupControl.get('departments')?.setValue([...event.container.data]);
      
      this.emitChanges();
    }
  }

  onUngroupedDrop(event: CdkDragDrop<string[]>): void {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      
      // Find which group this department was removed from and update its form
      this.groupsFormArray.controls.forEach((groupControl, index) => {
        if (event.previousContainer.data === this.getGroupDepartments(index)) {
          groupControl.get('departments')?.setValue([...event.previousContainer.data]);
        }
      });
      
      this.emitChanges();
    }
  }

  // Helper methods
  getDepartmentName(departmentId: string): string {
    const dept = this.departments.find(d => d.id === departmentId);
    return dept ? dept.name : departmentId;
  }

  getGroupDepartments(groupIndex: number): string[] {
    return this.groupsFormArray.at(groupIndex).get('departments')?.value || [];
  }

  private emitChanges(): void {
    if (this.autoEmit) {
      const groups = this.groupsFormArray.value;
      this.mappingsChanged.emit(groups);
    }
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  // Group management methods
  addNewGroup(): void {
    if (this.newGroupNameCtrl.invalid) {
      this.showError('Please enter a valid group name');
      return;
    }

    const newGroup: DepartmentGroup = {
      id: this.generateGroupId(),
      name: this.newGroupNameCtrl.value || '',
      departments: [],
      categories: [],
      workAreas: []
    };

    this.departmentGroups.push(newGroup);
    this.groupsFormArray.push(this.createGroupFormGroup(newGroup));
    this.newGroupNameCtrl.reset();

    this.emitChanges();
  }

  deleteGroup(groupIndex: number): void {
    const group = this.groupsFormArray.at(groupIndex);
    const departments = group.get('departments')?.value || [];

    // Move departments back to ungrouped
    this.ungroupedDepartments.push(...departments);

    // Remove group
    this.departmentGroups.splice(groupIndex, 1);
    this.groupsFormArray.removeAt(groupIndex);

    this.emitChanges();
  }

  editGroup(groupIndex: number): void {
    this.currentGroupIndex = groupIndex;
  }

  onCategorySelectionChange(groupIndex: number, selectedCategories: string[]): void {
    const groupControl = this.groupsFormArray.at(groupIndex);
    groupControl.get('categories')?.setValue(selectedCategories);
    this.emitChanges();
  }

  onWorkAreaSelectionChange(groupIndex: number, selectedWorkAreas: string[]): void {
    const groupControl = this.groupsFormArray.at(groupIndex);
    groupControl.get('workAreas')?.setValue(selectedWorkAreas);
    this.emitChanges();
  }

  private generateGroupId(): string {
    return 'group_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  // Save and validation methods
  isValidConfiguration(): boolean {
    return this.departmentGroups.length > 0 &&
           this.departmentGroups.some(group => group.departments.length > 0);
  }

  getCurrentConfiguration(): GroupMappingConfig {
    return {
      departmentGroups: this.groupsFormArray.value,
      ungroupedDepartments: this.ungroupedDepartments,
      lastUpdated: new Date().toISOString()
    };
  }

  saveConfiguration(): void {
    if (!this.isValidConfiguration()) {
      this.showError('Please create at least one group with departments');
      return;
    }

    this.isSaving = true;
    const config = this.getCurrentConfiguration();

    this.departmentService.saveGroupMappingConfig(this.tenantId, config)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            this.showSuccess('Group mapping configuration saved successfully');
            this.closeDialog.emit();
          } else {
            this.showError('Failed to save group mapping configuration');
          }
        },
        error: (error) => {
          console.error('Error saving group mapping config:', error);
          this.showError('Failed to save group mapping configuration');
        },
        complete: () => {
          this.isSaving = false;
        }
      });
  }

  cancel(): void {
    this.closeDialog.emit();
  }

  // Utility methods for template
  trackByGroupId(index: number, group: any): string {
    return group.get('id')?.value || index;
  }

  trackByDepartmentId(index: number, department: Department): string {
    return department.id;
  }

  trackByDepartmentIdString(index: number, departmentId: string): string {
    return departmentId;
  }

  getAvailableCategories(): Category[] {
    // Return categories that haven't been assigned to other groups
    const assignedCategories = new Set<string>();
    this.groupsFormArray.controls.forEach(control => {
      const categories = control.get('categories')?.value || [];
      categories.forEach((cat: string) => assignedCategories.add(cat));
    });

    return this.categories.filter(cat => !assignedCategories.has(cat.name));
  }

  getAvailableWorkAreas(): WorkArea[] {
    // Return work areas that haven't been assigned to other groups
    const assignedWorkAreas = new Set<string>();
    this.groupsFormArray.controls.forEach(control => {
      const workAreas = control.get('workAreas')?.value || [];
      workAreas.forEach((wa: string) => assignedWorkAreas.add(wa));
    });

    return this.workAreas.filter(wa => !assignedWorkAreas.has(wa.name));
  }
}
