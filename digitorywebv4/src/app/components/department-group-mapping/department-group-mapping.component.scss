.department-group-mapping {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;

  &.dialog-mode {
    height: 90vh;
    max-height: 900px;
  }

  // Header
  .header {
    background: #ff6b35;
    color: white;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .header-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 500;
      }
    }

    .close-button {
      color: white;
    }
  }

  // Loading
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 16px;

    p {
      color: #666;
      margin: 0;
    }
  }

  // Main Content
  .mapping-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }

  // Sections
  .section {
    background: white;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .section-header {
      background: #f8f9fa;
      padding: 16px 24px;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      align-items: center;
      gap: 12px;

      .step-number {
        background: #ff6b35;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
      }

      h4 {
        margin: 0;
        color: #ff6b35;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  // Department Chips
  .departments-chips {
    padding: 24px;

    .department-chip {
      margin: 4px;
      background: #e3f2fd;
      color: #1976d2;
    }
  }

  // Ungrouped Section
  .ungrouped-section {
    padding: 24px;
    border-bottom: 1px solid #e9ecef;

    h5 {
      margin: 0 0 16px 0;
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }

    .ungrouped-departments {
      min-height: 60px;
      border: 2px dashed #ddd;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      background: #fafafa;

      &.cdk-drop-list-dragging {
        background: #e8f5e8;
        border-color: #4caf50;
      }

      .department-item {
        .mat-mdc-chip {
          background: #fff3e0;
          color: #f57c00;
        }
      }
    }
  }

  // Groups Container
  .groups-container {
    padding: 24px;
  }

  // Group Card
  .group-card {
    border: 2px dashed #ff6b35;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    background: white;

    .group-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .group-name-field {
        flex: 1;
      }

      .delete-group-btn {
        color: #f44336;
      }
    }

    .group-departments-section {
      margin-bottom: 20px;

      .departments-drop-zone {
        min-height: 60px;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 12px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        background: #f9f9f9;

        &.cdk-drop-list-dragging {
          background: #e8f5e8;
          border-color: #4caf50;
        }

        .department-item {
          .mat-mdc-chip {
            background: #e3f2fd;
            color: #1976d2;
          }
        }

        .empty-drop-zone {
          color: #999;
          font-style: italic;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          min-height: 40px;
        }
      }
    }

    .mapping-section {
      margin-bottom: 16px;

      h6 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 14px;
        font-weight: 600;
      }

      .mapping-field {
        width: 100%;
        margin-bottom: 12px;
      }

      .selected-items {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .selected-chip {
          &.category-chip {
            background: #ff6b35;
            color: white;
          }

          &.workarea-chip {
            background: #4caf50;
            color: white;
          }
        }
      }
    }
  }

  // Add Group Section
  .add-group-section {
    padding: 0 24px 24px;
    border-top: 1px solid #e9ecef;
    padding-top: 24px;

    .add-group-form {
      display: flex;
      align-items: center;
      gap: 12px;
      max-width: 400px;

      .new-group-name-field {
        flex: 1;
      }

      .add-group-btn {
        background: #f0f0f0;
        border: 1px solid #ddd;
        color: #666;
        white-space: nowrap;

        &:not(:disabled):hover {
          background: #e0e0e0;
        }
      }
    }
  }

  // Footer Actions
  .footer-actions {
    background: white;
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);

    .cancel-btn {
      color: #666;
    }

    .save-btn {
      background: #ff6b35;
      color: white;
      min-width: 140px;

      .mat-mdc-progress-spinner {
        margin-right: 8px;
      }
    }
  }

  // Drag and Drop Styles
  .cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
                0 8px 10px 1px rgba(0, 0, 0, 0.14),
                0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }

  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .cdk-drop-list-dragging .cdk-drag {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
}
