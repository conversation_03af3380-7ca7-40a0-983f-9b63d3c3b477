<div class="department-group-mapping" [class.dialog-mode]="showAsDialog">
  <!-- Header -->
  <div class="header">
    <div class="header-content">
      <mat-icon class="header-icon">business</mat-icon>
      <h2>Configure Department Groups & Category Mapping</h2>
    </div>
    <button mat-icon-button (click)="cancel()" class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading departments and categories...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    
    <!-- Step 1: Available Departments -->
    <div class="section">
      <div class="section-header">
        <div class="step-number">1</div>
        <h4>Available Departments</h4>
      </div>
      
      <div class="departments-chips">
        <mat-chip-listbox>
          <mat-chip *ngFor="let department of departments; trackBy: trackByDepartmentId" 
                   [value]="department.id"
                   class="department-chip">
            {{department.name}}
          </mat-chip>
        </mat-chip-listbox>
      </div>
    </div>

    <!-- Step 2: Create Department Groups & Configure Mappings -->
    <div class="section">
      <div class="section-header">
        <div class="step-number">2</div>
        <h4>Create Department Groups & Configure Mappings</h4>
      </div>

      <!-- Ungrouped Departments -->
      <div class="ungrouped-section">
        <h5>Ungrouped Departments (drag to groups below)</h5>
        <div class="ungrouped-departments" 
             cdkDropList 
             [cdkDropListData]="ungroupedDepartments"
             (cdkDropListDropped)="onUngroupedDrop($event)"
             cdkDropListConnectedTo="group-departments">
          <div *ngFor="let departmentId of ungroupedDepartments; trackBy: trackByDepartmentIdString"
               class="department-item"
               cdkDrag>
            <mat-chip>{{getDepartmentName(departmentId)}}</mat-chip>
          </div>
        </div>
      </div>

      <!-- Department Groups -->
      <form [formGroup]="groupForm">
        <div formArrayName="groups" class="groups-container">
          <div *ngFor="let groupControl of groupsFormArray.controls; let i = index; trackBy: trackByGroupId"
               [formGroupName]="i" 
               class="group-card">
            
            <!-- Group Header -->
            <div class="group-header">
              <mat-form-field appearance="outline" class="group-name-field">
                <input matInput 
                       formControlName="name"
                       placeholder="Enter group name (e.g., Food & Beverages)">
              </mat-form-field>
              <button mat-icon-button 
                      (click)="deleteGroup(i)" 
                      class="delete-group-btn"
                      color="warn">
                <mat-icon>delete</mat-icon>
              </button>
            </div>

            <!-- Group Departments -->
            <div class="group-departments-section">
              <div class="departments-drop-zone"
                   cdkDropList
                   [cdkDropListData]="getGroupDepartments(i)"
                   (cdkDropListDropped)="onDepartmentDrop($event, i)"
                   [id]="'group-departments-' + i"
                   cdkDropListConnectedTo="ungrouped-departments">
                <div *ngFor="let departmentId of getGroupDepartments(i); trackBy: trackByDepartmentIdString"
                     class="department-item"
                     cdkDrag>
                  <mat-chip>{{getDepartmentName(departmentId)}}</mat-chip>
                </div>
                <div *ngIf="getGroupDepartments(i).length === 0" class="empty-drop-zone">
                  Drag departments here
                </div>
              </div>
            </div>

            <!-- Category Mapping -->
            <div class="mapping-section">
              <h6>Category Mapping</h6>
              <mat-form-field appearance="outline" class="mapping-field">
                <mat-select formControlName="categories" 
                           multiple
                           placeholder="Select categories"
                           (selectionChange)="onCategorySelectionChange(i, $event.value)">
                  <mat-option *ngFor="let category of getAvailableCategories()" 
                             [value]="category.name">
                    {{category.name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              
              <!-- Selected Categories Display -->
              <div class="selected-items" *ngIf="groupControl.get('categories')?.value?.length > 0">
                <mat-chip *ngFor="let category of groupControl.get('categories')?.value"
                         class="selected-chip category-chip">
                  {{category}} ×
                </mat-chip>
              </div>
            </div>

            <!-- Work Area Mapping -->
            <div class="mapping-section">
              <h6>Work Area Mapping</h6>
              <mat-form-field appearance="outline" class="mapping-field">
                <mat-select formControlName="workAreas" 
                           multiple
                           placeholder="Select work areas"
                           (selectionChange)="onWorkAreaSelectionChange(i, $event.value)">
                  <mat-option *ngFor="let workArea of getAvailableWorkAreas()" 
                             [value]="workArea.name">
                    {{workArea.name}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              
              <!-- Selected Work Areas Display -->
              <div class="selected-items" *ngIf="groupControl.get('workAreas')?.value?.length > 0">
                <mat-chip *ngFor="let workArea of groupControl.get('workAreas')?.value"
                         class="selected-chip workarea-chip">
                  {{workArea}} ×
                </mat-chip>
              </div>
            </div>
          </div>
        </div>
      </form>

      <!-- Add New Group -->
      <div class="add-group-section">
        <div class="add-group-form">
          <mat-form-field appearance="outline" class="new-group-name-field">
            <input matInput 
                   [formControl]="newGroupNameCtrl"
                   placeholder="Enter group name (e.g., Food & Beverages)">
          </mat-form-field>
          <button mat-button 
                  (click)="addNewGroup()"
                  [disabled]="newGroupNameCtrl.invalid"
                  class="add-group-btn">
            + Add New Group
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Actions -->
  <div class="footer-actions" *ngIf="!isLoading">
    <button mat-button (click)="cancel()" class="cancel-btn">
      Cancel
    </button>
    <button mat-raised-button 
            color="primary"
            (click)="saveConfiguration()"
            [disabled]="!isValidConfiguration() || isSaving"
            class="save-btn">
      <mat-spinner *ngIf="isSaving" diameter="20"></mat-spinner>
      <span *ngIf="!isSaving">Save Configuration</span>
      <span *ngIf="isSaving">Saving...</span>
    </button>
  </div>
</div>
