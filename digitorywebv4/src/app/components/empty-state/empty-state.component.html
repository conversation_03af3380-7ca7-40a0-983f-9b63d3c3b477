<div class="empty-state-container" [ngClass]="customClass" [@fadeIn]>
  <div class="empty-state-content">
    <div class="icon-container">
      <mat-icon class="empty-state-icon">{{icon}}</mat-icon>
    </div>
    <h3 class="empty-state-title">{{title}}</h3>
    <p class="empty-state-message">{{message}}</p>
    <button
      *ngIf="showAction && actionLabel"
      mat-flat-button
      color="primary"
      class="empty-state-action"
      (click)="onActionClick()">
      {{actionLabel}}
    </button>
  </div>
</div>
