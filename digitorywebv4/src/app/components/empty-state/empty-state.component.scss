.empty-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  margin: 1rem 0;
  min-height: 180px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  }
}

.empty-state-content {
  text-align: center;
  max-width: 400px;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(245, 124, 0, 0.1);
  margin: 0 auto 1rem;
}

.empty-state-icon {
  font-size: 36px;
  height: 36px;
  width: 36px;
  color: #f57c00; /* Orange color to match the app theme */
}

.empty-state-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 0.75rem 0;
  color: #555;
}

.empty-state-message {
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  color: #757575;
}

.empty-state-action {
  margin-top: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 13px;
  font-weight: 500;
}

/* Dialog specific styling */
.dialog-empty-state {
  min-height: 140px;
  padding: 1rem;

  .icon-container {
    width: 60px;
    height: 60px;
  }

  .empty-state-icon {
    font-size: 30px;
    height: 30px;
    width: 30px;
  }

  .empty-state-title {
    font-size: 16px;
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .empty-state-container {
    padding: 1.25rem;
    min-height: 150px;
  }

  .icon-container {
    width: 60px;
    height: 60px;
  }

  .empty-state-icon {
    font-size: 30px;
    height: 30px;
    width: 30px;
  }

  .empty-state-title {
    font-size: 16px;
  }

  .empty-state-message {
    font-size: 13px;
  }
}
