import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-avatar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './avatar.component.html',
  styleUrls: ['./avatar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AvatarComponent {
  @Input({ required: true }) user!: any;

  public initials: string;

  public isHover: boolean = false;

  private colors = [
    '#ff8c42', // Orange primary
    '#87a96b', // Sage green
    '#6b9bd2', // Soft blue
    '#9b7bb8', // Muted purple
    '#8d7b68', // Warm gray
    '#d4a5a5', // Dusty rose
  ];

  constructor() {}

  ngOnInit() {
    this.initials = this.createInitials(this.user.value.displayName);
    if (!this.user.value.color) {
      const randomIndex = Math.floor(
        Math.random() * Math.floor(this.colors.length)
      );
      this.user.value.color = this.colors[randomIndex];
    }
  }

  public createInitials(name: string): string {
    let initials = '';

    // for (let i = 0; i < name.length; i++) {
    //   if (name.charAt(i) === ' ') {
    //     continue;
    //   }

    //   if (name.charAt(i) === name.charAt(i).toUpperCase()) {
    //     initials += name.charAt(i);

    //     if (initials.length == 2) {
    //       break;
    //     }
    //   }
    // }

    return initials;
  }
}
