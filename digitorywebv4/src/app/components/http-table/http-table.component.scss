@use "../../../styles/variables" as v;

.custom-header {
  min-width: 185px;
}

.custom-cell {
  min-width: 185px;
}

/* Fix for container width */
:host {
  display: block;
  width: 100%;
}

.tableDiv {
  width: 100%;
  overflow-x: auto;
  max-width: 100%;
  display: block;
}

.table-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;

  .search-container {
    flex: 1;
    max-width: 400px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .table-actions-container {
    flex-direction: column;
    align-items: stretch;

    .search-container {
      max-width: 100%;
      margin-bottom: 12px;
    }

    .action-buttons {
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }
}

.mat-paginator-sticky {
  bottom: 0px;
  position: sticky;
  z-index: 10;
}

.tableNotes{
  text-align: center;
  font-size: medium;
  font-weight: bold
}

.disabledBtn{
  color: grey;
}

.cards{
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
  margin: 10px 0px;
}

.parentCard{
  border: 0.5px solid #80808042;
  border-radius: 5px;
  height: 4rem;
  width: 15rem;
  padding: 5px;
}

.cardHeading{
  font-size: small;
  font-weight: 600;
}

.cardData{
  font-size: 18px;
  font-weight: bolder;
}

.cardIcons{
  margin-right: 10px;
}

.icons{
  border-radius: 50px;
  padding: 3px;
}

.recipeIcon{
  background-color: mistyrose;
  font-size: 20px;
}

.LinkedPosIcon{
  background-color: #e1f1ff;
  font-size: 18px;
}

.equalizerIcon{
  background-color: #eaffe1;
  font-size: 17px;
}

.verticalIcon{
  background-color: #ffebb3;
  font-size: 17px;
}

.pendingColor {
  background-color: #FFDAB9;
}

.fadedButton {
  opacity: 0.4;
}
.warningText{
  text-align: center;
  font-size: medium;
  font-weight: bold;
  background-color: #e5e5e5;
  color: crimson;
  padding: 10px;
}

.emailClass{
  word-break: break-word !important;
  white-space: normal !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

mat-tree{
  border: 1px solid lightgrey;
  margin: 30px;
  padding: 30px;
}

// .mat-tree-node {
//   // border-bottom: 1px solid lightgrey;
//   margin-left: 80px !important;
// }

.treeChildClass{
  width: 300px;
  gap: 5px;
}

.treeChildIconClass{
  font-size: 20px;
  cursor: grab;
}

.highlighted-node{
  // color: gold;
  color: #f44336;

}

.highlighted-childNode{
  // color: gold;
  color: #f44336;
  font-size: 10px;
}

.highlighted-dicNode{
  color: grey;
}

.removePaginator{
  display: none;
}

.addCircleIcon{
  opacity: 0.2;
  cursor: grab;
}

.addCircleIcon:hover{
  opacity: 1;
}
.subrecClass{
  min-width: 134px !important;
}

// .partyClass{
//   min-width: 120px !important;
// }

.partyClass{
  min-width: 160px !important;
}

.posAddIcon{
  font-size: 20px !important;
  margin: 0px 5px !important;
  padding-top: 2px !important;
  cursor: pointer !important;
}

// .extraSuppliesClass{
//   table {
//     border: none;
//   }

//   th, td {
//     border: none;
//   }
// }