<mat-card appearance="outlined">
  <mat-card-header>
    <mat-card-subtitle>
      Keep your calories diary up to date!
    </mat-card-subtitle>
    <mat-card-title> Add New entry </mat-card-title>
  </mat-card-header>
  <mat-card-content class="mat-card-content">
    <form [formGroup]="form" class="form" (submit)="onSubmit()">
      <mat-form-field>
        <mat-label>Choose a date</mat-label>
        <input matInput [matDatepicker]="picker" formControlName="date" />
        <mat-datepicker-toggle
          matIconSuffix
          [for]="picker"
        ></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
      <div>
        <mat-slider
          class="example-margin"
          [discrete]="true"
          [step]="1"
          [min]="1"
          [max]="8000"
          [ngStyle]="{ width: '100%' }"
        ><input matSliderThumb />
          <input matSliderThumb formControlName="value" #slider />
        </mat-slider>
      </div>
      <button
        type="submit"
        class="button"
        mat-fab
        color="primary"
        aria-label="Example icon button with a delete icon"
      >
        <mat-icon>add_circle</mat-icon>
      </button>
    </form>
  </mat-card-content>
</mat-card>
