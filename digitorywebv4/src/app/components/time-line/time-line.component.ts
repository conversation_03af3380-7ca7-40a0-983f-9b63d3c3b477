import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvatarComponent } from '../avatar/avatar.component';

@Component({
  selector: 'app-time-line',
  standalone: true,
  imports: [CommonModule,AvatarComponent],
  templateUrl: './time-line.component.html',
  styleUrls: ['./time-line.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TimeLineComponent {
  @Input({ required: true }) item!: any;
  @Input({ required: true }) index!: any;

  ngOnInit(): void {
  }
    
  
}
