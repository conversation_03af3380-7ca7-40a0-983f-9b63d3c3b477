import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { AuthService } from 'src/app/services/auth.service';


@Component({
  selector: 'app-dashboard-menu',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatDividerModule,
  ],
  templateUrl: './dashboard-menu.component.html',
  styleUrls: ['./dashboard-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardMenuComponent implements OnInit {
  public isReady = false;
  public dashboardPanel: {
    icon: string;
    title: string;
    links: { label: string; routerLink: string }[];
  }[] = [];
  public user: any;
  public userRole: any;
  constructor(private auth: AuthService) {
    this.user = this.auth.getCurrentUser();
    this.userRole = this.auth.getCurrRole();
  }

  ngOnInit(): void {
    let modules ={
      "Master Data": [
          "masterData"
      ],
      "Recipe Builder": [
        "recipe"
    ]
  }
    if (this.userRole) {
      for (const [key, value] of Object.entries(modules)) {
        let temp = {
          icon: 'account_circle',
          title: key,
          links: value ? this.generateLinks(value) : [],
        }
        this.dashboardPanel.push(temp)
      }
    }
    this.isReady = true;
  }

  generateLinks(module) {
    const links = module.map(label => {
      return { label, routerLink: `/dashboard/${label}` };
    });
    return links;
  }
}
