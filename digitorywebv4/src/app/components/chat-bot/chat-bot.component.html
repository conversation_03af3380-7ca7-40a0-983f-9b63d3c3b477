<div class="chat-layout">
  <div class="chat-container">
  <!-- Start/Resume Overlay -->
  <div class="chat-overlay" *ngIf="!conversationStarted" (click)="startConversation()">
    <div class="overlay-content">
      <mat-icon class="overlay-icon">restaurant</mat-icon>
      <h2>Restaurant Onboarding Assistant</h2>
      <p>I'll help you collect information about your restaurant outlets, cuisines, and more.</p>
      <button mat-raised-button color="primary" class="start-button">
        <mat-icon>play_arrow</mat-icon>
        <span *ngIf="messages.length === 0">Click to Start</span>
        <span *ngIf="messages.length > 0">Click to Resume</span>
      </button>
    </div>
  </div>

  <div class="chat-header">
    <div class="chat-title">
      <mat-icon class="chat-icon">restaurant</mat-icon>
      <span class="assistant-title">Time to cook up the details of your restaurant!</span>
    </div>
    <div class="chat-actions">
      <button mat-icon-button matTooltip="Clear Chat" (click)="clearConversationHistory()" class="clear-btn">
        <mat-icon>clear</mat-icon>
      </button>
    </div>
  </div>

  <div class="chat-messages">
    <div *ngFor="let message of messages; trackBy: trackById" class="message-container" [ngClass]="{
        'user-message': message.sender === 'user',
        'bot-message': message.sender === 'bot',
        'number-container': message.messageType === 'number',
        'short-answer-container': message.messageType === 'short-answer',
        'system-message-container': message.messageType === 'system-message'
      }">
      <div class="message-content" [ngClass]="{
        'number-message': message.messageType === 'number',
        'short-answer-message': message.messageType === 'short-answer',
        'system-message': message.messageType === 'system-message'
      }">
        <div class="message-wrapper">
          <div class="message-text" [innerHTML]="message.text | markdown"></div>
          <div class="message-timestamp" *ngIf="message.sender !== 'system'">{{ message.timestamp | date:'shortTime' }}</div>
        </div>
      </div>
    </div>

    <!-- Improved loading indicator when waiting for a response -->
    <div *ngIf="isWaitingForResponse" class="message-container bot-message">
      <div class="typing-indicator">
        <div class="typing-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div class="typing-text">DIGI is thinking...</div>
      </div>
    </div>
  </div>

  <div class="chat-input">
    <mat-form-field appearance="outline" class="message-field">
      <input matInput
             [(ngModel)]="currentMessage"
             placeholder="Type your message..."
             (keydown)="onKeyPress($event)"
             [disabled]="isConnecting"
             #messageInput
             >
    </mat-form-field>
    <button mat-mini-fab color="primary" (click)="sendMessage()" [disabled]="!currentMessage.trim() || isConnecting">
      <mat-icon>send</mat-icon>
    </button>
  </div>
</div>

  <!-- Restaurant Data Panel -->
  <div class="restaurant-data-panel">
    <div class="panel-header">
      <div class="header-left">
        <mat-icon>restaurant_menu</mat-icon>
        <h2>Restaurant Information</h2>
      </div>
      <div class="header-right">
        <button mat-icon-button class="action-button refresh-button" (click)="refreshRestaurantData()" matTooltip="Refresh restaurant data" [disabled]="isRefreshing">
          <mat-icon [class.rotating]="isRefreshing">refresh</mat-icon>
        </button>
      </div>
    </div>

    <div class="panel-content">
      <!-- No Data Message -->
      <div *ngIf="!restaurantData">
        <app-empty-state
          icon="restaurant"
          title="No Restaurant Data Yet"
          message="As you provide information about your restaurant through the chat, it will appear here."
          customClass="restaurant-empty-state"
        ></app-empty-state>
      </div>

      <!-- Restaurant Data (shown when available) -->
      <ng-container *ngIf="restaurantData">
        <!-- Summary Section -->
        <div class="data-section summary-section">
          <h3><mat-icon>info</mat-icon> Restaurant Summary</h3>
          <div class="data-item">
            <span class="label">Total Outlets:</span>
            <span class="value">{{restaurantData.totalOutlets}}</span>
          </div>
          <div class="data-item">
            <span class="label">Cuisines:</span>
            <span class="value">
              <span *ngIf="restaurantData.commonCuisinesAcrossOutlets && restaurantData.commonCuisinesAcrossOutlets.length > 0">
                <ng-container *ngFor="let cuisine of restaurantData.commonCuisinesAcrossOutlets; let last = last">
                  {{cuisine}}
                  <ng-container *ngIf="getCuisineItemCount(cuisine) > 0">
                    ({{getCuisineItemCount(cuisine)}})
                  </ng-container>
                  <ng-container *ngIf="!last">, </ng-container>
                </ng-container>
              </span>
              <span *ngIf="!restaurantData.commonCuisinesAcrossOutlets || restaurantData.commonCuisinesAcrossOutlets.length === 0">
                None specified
              </span>
            </span>
          </div>
          <div class="data-item">
            <span class="label">Signature Dishes:</span>
            <span class="value">
              <span *ngIf="restaurantData.signatureElements && restaurantData.signatureElements.signatureDishes && restaurantData.signatureElements.signatureDishes.length > 0">
                {{restaurantData.signatureElements.signatureDishes.join(', ')}}
              </span>
              <span *ngIf="!restaurantData.signatureElements || !restaurantData.signatureElements.signatureDishes || restaurantData.signatureElements.signatureDishes.length === 0">
                None specified
              </span>
            </span>
          </div>
          <div class="data-item">
            <span class="label">Alcohol Service:</span>
            <span class="value">{{restaurantData.beverageInfo?.alcoholService || 'Not specified'}}</span>
          </div>
          <div class="data-item">
            <span class="label">Tobacco Service:</span>
            <span class="value">{{restaurantData.tobaccoInfo?.tobaccoService || 'Not specified'}}</span>
          </div>
        </div>

        <!-- Outlets Section -->
        <div class="data-section outlet-section" *ngFor="let outlet of restaurantData.outletDetails; let i = index">
          <h3><mat-icon>store</mat-icon> Outlet {{i+1}}: {{outlet.outletName}} {{outlet.outletAbbreviation ? '(' + outlet.outletAbbreviation + ')' : ''}}</h3>
          <div class="data-item">
            <span class="label">Address:</span>
            <span class="value">{{outlet.outletAddress}}</span>
          </div>
          <div class="data-item">
            <span class="label">Work Areas:</span>
            <span class="value">
              <span *ngIf="outlet.outletWorkAreas && outlet.outletWorkAreas.length > 0">
                <span *ngFor="let area of outlet.outletWorkAreas; let last = last" class="work-area-tag">
                  {{area}}<span *ngIf="!last">, </span>
                </span>
              </span>
              <span *ngIf="!outlet.outletWorkAreas || outlet.outletWorkAreas.length === 0">
                None specified
              </span>
            </span>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>