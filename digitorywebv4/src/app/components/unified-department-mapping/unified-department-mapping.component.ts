import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormBuilder, FormGroup, FormArray, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Material Design Modules
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';

// Third-party modules
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';

import { DepartmentService, DepartmentGroup, GroupMappingConfig } from '../../services/department.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

export interface Department {
  id: string;
  name: string;
  code?: string;
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
}

export interface CategoryWorkareaMapping {
  categoryName: string;
  workAreas: string[];
  virtualWorkAreas?: VirtualWorkareaMapping[];
}

export interface WorkAreaData {
  restaurantIdOld: string;
  branchName: string;
  workAreas: string[];
  disabled: boolean;
}

export interface VirtualWorkareaMapping {
  physicalWorkarea: string;
  virtualWorkarea: string;
  departmentId?: string;
  departmentName?: string;
  isVirtual: boolean;
}

export interface SharedWorkareaConfig {
  physicalWorkarea: string;
  isShared: boolean;
  virtualMappings: VirtualWorkareaMapping[];
}

@Component({
  selector: 'app-unified-department-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatOptionModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatSnackBarModule,
    MatButtonModule,
    NgxMatSelectSearchModule
  ],
  templateUrl: './unified-department-mapping.component.html',
  styleUrls: ['./unified-department-mapping.component.scss']
})
export class UnifiedDepartmentMappingComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Inputs
  @Input() tenantId: string = '';
  @Input() autoEmit: boolean = true; // Whether to auto-emit changes
  @Input() showAsDialog: boolean = false; // Whether component is shown in a dialog

  // Outputs
  @Output() mappingsChanged = new EventEmitter<DepartmentCategoryMapping[]>();
  @Output() categoryWorkareaMappingsChanged = new EventEmitter<CategoryWorkareaMapping[]>();
  @Output() dialogClosed = new EventEmitter<void>();

  // Data
  departments: Department[] = [];
  categories: string[] = [];
  workAreas: WorkAreaData[] = [];
  filteredDepartments: Department[] = [];
  
  // Form controls
  departmentFilterCtrl = new FormControl('');
  selectedDepartmentsCtrl = new FormControl<string[]>([]);
  mappingForm: FormGroup;

  // State
  isLoading = true;
  isSaving = false;
  isFormStable = false; // Flag to prevent unnecessary form rebuilds
  currentMappings: DepartmentCategoryMapping[] = [];
  categoryWorkareaMappings: CategoryWorkareaMapping[] = [];
  selectedCategoriesForWorkarea: string[] = [];

  // Category-Workarea mapping properties
  categoryWorkareaForm: FormGroup;
  allWorkAreas: string[] = [];
  selectedCategories: string[] = [];
  enableVirtualWorkareas: boolean = false;
  sharedWorkareaConfigs: Map<string, SharedWorkareaConfig> = new Map();
  virtualWorkareaMap: Map<string, VirtualWorkareaMapping> = new Map();
  validationErrors: string[] = [];

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.initializeForm();
    this.initializeCategoryWorkareaForm();
  }

  ngOnInit(): void {
    if (!this.tenantId) {
      const user = this.authService.getCurrentUser();
      this.tenantId = user?.tenantId || '';
    }

    this.setupFormSubscriptions();
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.mappingForm = this.fb.group({
      mappings: this.fb.array([])
    });
  }

  private setupFormSubscriptions(): void {
    // Department filter subscription
    this.departmentFilterCtrl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(filterValue => {
        this.filterDepartments(filterValue || '');
      });

    // Selected departments subscription with debounce to prevent multiple calls
    this.selectedDepartmentsCtrl.valueChanges
      .pipe(
        debounceTime(100), // Small debounce to prevent rapid fire
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(selectedIds => {
        console.log('DEBUG: Department selection changed:', selectedIds);
        console.log('DEBUG: hasSelectedDepartments:', this.hasSelectedDepartments);
        console.log('DEBUG: selectedDepartmentsCount:', this.selectedDepartmentsCount);

        // Always update the form when departments are selected
        this.updateMappingForm(selectedIds || []);

        // Only emit if auto-emit is enabled and we're not in initial loading
        if (this.autoEmit && !this.isLoading) {
          this.emitCurrentMappings();
        }
      });
  }

  private loadData(): void {
    this.isLoading = true;

    // Load departments, categories, workareas and mappings in parallel
    Promise.all([
      this.loadDepartments(),
      this.loadCategories(),
      this.loadWorkAreas(),
      this.loadExistingMappings(),
      this.loadCategoryWorkareaMappings()
    ]).then(() => {
      this.extractAllWorkAreas();

      // If we have existing mappings, make sure the form is properly updated
      if (this.currentMappings.length > 0) {
        const selectedDepartmentIds = this.selectedDepartmentsCtrl.value || [];
        this.updateMappingForm(selectedDepartmentIds);
      }

      this.updateSelectedCategoriesAndForm();
      this.isLoading = false;
      this.isFormStable = true; // Mark form as stable after initial setup
      this.cdr.detectChanges();
    }).catch(() => {
      this.isLoading = false;
      this.cdr.detectChanges();
    });
  }

  private loadDepartments(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartments(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (departments) => {
            this.departments = departments;
            this.filteredDepartments = [...departments];
            resolve();
          },
          error: (error) => {
            this.departments = [];
            this.filteredDepartments = [];
            reject(error);
          }
        });
    });
  }

  private loadCategories(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.smartDashboardService.getCategories(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            if (response && response.categories) {
              // Handle if categories are strings directly or objects
              this.categories = response.categories.map((cat: any) => {
                if (typeof cat === 'string') {
                  return cat;
                } else if (typeof cat === 'object') {
                  return cat.name || cat.categoryName || cat.category || cat.Category || cat.CATEGORY || Object.keys(cat)[0] || 'Unknown';
                }
                return String(cat);
              });
            } else {
              this.categories = [];
            }
            resolve();
          },
          error: (error) => {
            this.categories = [];
            reject(error);
          }
        });
    });
  }

  private loadExistingMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartmentCategoryMappings(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            this.currentMappings = mappings;
            this.restoreSelectionFromMappings(mappings);
            resolve();
          },
          error: (error) => {
            this.currentMappings = [];
            reject(error);
          }
        });
    });
  }

  private loadWorkAreas(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Get work areas from user's restaurant access data
        const user = this.authService.getCurrentUser();
        console.log('DEBUG: Current user for work areas:', user);
        const workAreaData: WorkAreaData[] = [];

        if (user && user.restaurantAccess) {
          console.log('DEBUG: User restaurant access:', user.restaurantAccess);
          user.restaurantAccess.forEach((restaurant: any) => {
            console.log('DEBUG: Processing restaurant:', restaurant);
            if (restaurant.workAreas && restaurant.workAreas.length > 0) {
              workAreaData.push({
                restaurantIdOld: restaurant.restaurantIdOld,
                branchName: restaurant.branchName,
                workAreas: restaurant.workAreas,
                disabled: false
              });
            }
          });
        }

        this.workAreas = workAreaData;
        console.log('DEBUG: Final work areas data:', this.workAreas);
        resolve();
      } catch (error) {
        console.log('DEBUG: Error loading work areas:', error);
        this.workAreas = [];
        reject(error);
      }
    });
  }

  private loadCategoryWorkareaMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getCategoryWorkareaMappings(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (mappings) => {
            // Process mappings to ensure work areas are in correct format
            this.categoryWorkareaMappings = mappings.map(mapping => {
              let processedWorkAreas = mapping.workAreas || [];

              // Handle case where workAreas might be stored as string instead of array
              if (typeof processedWorkAreas === 'string') {
                processedWorkAreas = [processedWorkAreas];
              }

              return {
                ...mapping,
                workAreas: processedWorkAreas,
                virtualWorkAreas: mapping.virtualWorkAreas || processedWorkAreas
              };
            });

            resolve();
          },
          error: (error) => {
            this.categoryWorkareaMappings = [];
            reject(error);
          }
        });
    });
  }

  private restoreSelectionFromMappings(mappings: DepartmentCategoryMapping[]): void {
    if (mappings.length > 0) {
      const selectedDepartmentIds = mappings.map(m => String(m.departmentId));

      // Set the value without triggering subscriptions during initial load
      this.selectedDepartmentsCtrl.setValue(selectedDepartmentIds, { emitEvent: false });

      // Manually update the mapping form with the restored selections
      this.updateMappingForm(selectedDepartmentIds);
    }
  }

  private filterDepartments(filterValue: string): void {
    if (!filterValue) {
      this.filteredDepartments = [...this.departments];
    } else {
      const filter = filterValue.toLowerCase();
      this.filteredDepartments = this.departments.filter(dept =>
        dept.name.toLowerCase().includes(filter) ||
        (dept.code && dept.code.toLowerCase().includes(filter))
      );
    }
  }

  private updateMappingForm(selectedDepartmentIds: string[]): void {
    console.log('DEBUG: updateMappingForm called with:', selectedDepartmentIds);
    const mappingsArray = this.mappingForm.get('mappings') as FormArray;

    // Ensure all department IDs are strings
    const stringDepartmentIds = selectedDepartmentIds.map(id => String(id));

    // Check if the form already has the correct departments to avoid unnecessary rebuilds
    const currentDepartmentIds = mappingsArray.controls.map(control => control.get('departmentId')?.value).filter(id => id);
    const currentSet = new Set(currentDepartmentIds.map(id => String(id)));
    const newSet = new Set(stringDepartmentIds);

    const isSameSelection = currentSet.size === newSet.size &&
                           [...currentSet].every(id => newSet.has(id));

    // Only skip rebuild if we have the exact same selection and form is not empty
    if (isSameSelection && mappingsArray.length > 0) {
      console.log('DEBUG: Skipping form rebuild - same selection');
      return;
    }

    console.log('DEBUG: Rebuilding form array for departments:', stringDepartmentIds);

    // Store current form values to preserve user selections
    const currentFormValues = new Map<string, string[]>();
    for (let i = 0; i < mappingsArray.length; i++) {
      const control = mappingsArray.at(i);
      const departmentId = control.get('departmentId')?.value;
      const categories = control.get('categories')?.value || [];
      if (departmentId) {
        currentFormValues.set(String(departmentId), categories);
      }
    }

    // Clear existing form array
    while (mappingsArray.length !== 0) {
      mappingsArray.removeAt(0);
    }

    // Add form groups for selected departments
    stringDepartmentIds.forEach(departmentId => {
      const department = this.departments.find(d => String(d.id) === String(departmentId));

      if (department) {
        // Try to get categories from current form values first, then from existing mappings
        const currentFormCategories = currentFormValues.get(String(departmentId));
        const existingMapping = this.currentMappings.find(m => String(m.departmentId) === String(departmentId));
        const categories = currentFormCategories || (existingMapping ? existingMapping.categories : []);

        const mappingGroup = this.fb.group({
          departmentId: [String(departmentId)],
          departmentName: [department.name],
          categories: [categories]
        });

        // Subscribe to category changes for this department with debounce
        mappingGroup.get('categories')?.valueChanges
          .pipe(
            debounceTime(200), // Debounce to prevent rapid fire on multi-select
            takeUntil(this.destroy$)
          )
          .subscribe(() => {
            if (this.autoEmit && !this.isLoading) {
              this.emitCurrentMappings();
            }
          });

        mappingsArray.push(mappingGroup);
      }
    });

    this.cdr.detectChanges();
  }

  private emitCurrentMappings(): void {
    if (!this.autoEmit || this.isLoading) {
      return;
    }

    const mappingsArray = this.mappingForm.get('mappings') as FormArray;
    const currentMappings: DepartmentCategoryMapping[] = mappingsArray.value;

    // Filter out mappings without categories
    const validMappings = currentMappings.filter(m => m.categories && m.categories.length > 0);

    // Update category-workarea form when department mappings change
    this.updateSelectedCategoriesAndForm();

    this.mappingsChanged.emit(validMappings);
  }

  // Public methods for external access
  getCurrentMappings(): DepartmentCategoryMapping[] {
    const mappingsArray = this.mappingForm.get('mappings') as FormArray;
    const mappings = mappingsArray.value;
    return mappings;
  }

  getCurrentCategoryWorkareaMappings(): CategoryWorkareaMapping[] {
    const mappings: CategoryWorkareaMapping[] = [];

    for (let i = 0; i < this.categoryWorkareaFormArray.length; i++) {
      const formGroup = this.categoryWorkareaFormArray.at(i) as FormGroup;
      const categoryName = formGroup.get('categoryName')?.value;
      const selectedWorkAreas = formGroup.get('workAreas')?.value || [];

      if (categoryName) {
        // Find existing mapping to preserve virtual work areas
        const existingMapping = this.categoryWorkareaMappings.find(m => m.categoryName === categoryName);

        mappings.push({
          categoryName,
          workAreas: selectedWorkAreas, // Include even if empty
          virtualWorkAreas: existingMapping?.virtualWorkAreas || []
        });
      }
    }

    return mappings;
  }

  // Getter methods for template
  get selectedDepartmentsCount(): number {
    const count = this.selectedDepartmentsCtrl.value?.length || 0;
    console.log('DEBUG: selectedDepartmentsCount getter called, value:', this.selectedDepartmentsCtrl.value, 'count:', count);
    return count;
  }

  get mappedDepartmentsCount(): number {
    const mappings = this.getCurrentMappings();
    return mappings.filter(m => m.categories && m.categories.length > 0).length;
  }

  get totalCategoriesAssigned(): number {
    const mappings = this.getCurrentMappings();
    return mappings.reduce((total, m) => total + (m.categories ? m.categories.length : 0), 0);
  }

  get hasSelectedDepartments(): boolean {
    const result = this.selectedDepartmentsCount > 0;
    console.log('DEBUG: hasSelectedDepartments getter called, result:', result, 'count:', this.selectedDepartmentsCount);
    return result;
  }

  getAvailableCategories(departmentIndex: number): string[] {
    const mappingsArray = this.mappingForm.get('mappings') as FormArray;
    const usedCategories = new Set<string>();

    // Collect categories used by other departments
    for (let i = 0; i < mappingsArray.length; i++) {
      if (i !== departmentIndex) {
        const categories = mappingsArray.at(i).get('categories')?.value || [];
        categories.forEach((cat: string) => usedCategories.add(cat));
      }
    }

    // Return categories not used by other departments
    const availableCategories = this.categories.filter(cat => !usedCategories.has(cat));


    return availableCategories;
  }

  get mappingsFormArray(): FormArray {
    return this.mappingForm.get('mappings') as FormArray;
  }

  // UI Helper methods
  toggleAllDepartments(event: Event): void {
    event.stopPropagation();
    
    const allSelected = this.selectedDepartmentsCtrl.value?.length === this.filteredDepartments.length;
    if (allSelected) {
      this.selectedDepartmentsCtrl.setValue([]);
    } else {
      this.selectedDepartmentsCtrl.setValue(this.filteredDepartments.map(d => d.id));
    }
  }

  areAllDepartmentsSelected(): boolean {
    return this.selectedDepartmentsCtrl.value?.length === this.filteredDepartments.length;
  }

  trackByIndex(index: number): number {
    return index;
  }

  trackByDepartmentId(_index: number, item: any): string {
    return item.departmentId;
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // ===== CATEGORY-WORKAREA MAPPING METHODS =====

  get selectedCategoriesFromDepartments(): string[] {
    const allSelectedCategories: string[] = [];
    this.mappingsFormArray.controls.forEach(control => {
      const categories = control.get('categories')?.value || [];
      allSelectedCategories.push(...categories);
    });
    // Remove duplicates
    return [...new Set(allSelectedCategories)];
  }

  get hasSelectedCategories(): boolean {
    return this.selectedCategoriesFromDepartments.length > 0;
  }

  get hasValidMappings(): boolean {
    const mappingsArray = this.mappingForm.get('mappings') as FormArray;
    const currentMappings: DepartmentCategoryMapping[] = mappingsArray.value;
    return currentMappings.some(m => m.categories && m.categories.length > 0);
  }

  onCategoryWorkareaMapping(mappings: CategoryWorkareaMapping[]): void {
    this.categoryWorkareaMappings = mappings;
  }

  private updateSelectedCategoriesAndForm(): void {
    // Update selected categories based on department mappings
    this.selectedCategories = this.selectedCategoriesFromDepartments;
    console.log('DEBUG: Selected categories from departments:', this.selectedCategories);
    console.log('DEBUG: All work areas:', this.allWorkAreas);
    console.log('DEBUG: Current category-workarea mappings:', this.categoryWorkareaMappings);

    // Build category-workarea form based on selected categories
    this.buildCategoryWorkareaFormFromSelectedCategories();
  }

  saveAndClose(): void {
    if (!this.hasValidMappings) {
      this.showError('Please assign at least one category to a department before saving.');
      return;
    }

    this.isSaving = true;
    const allMappings = this.getCurrentMappings();

    const mappingsToSave = allMappings.filter(m => {
      const hasCategories = m.categories && m.categories.length > 0;
      return hasCategories;
    });

    // Save department-category mappings first
    this.departmentService.saveDepartmentCategoryMappings(this.tenantId, mappingsToSave)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {

          if (success) {
            // Get current category-workarea mappings from the form
            const currentCategoryWorkareaMappings = this.getCurrentCategoryWorkareaMappings();
            console.log('DEBUG: Current category-workarea mappings from form:', currentCategoryWorkareaMappings);
            console.log('DEBUG: Selected categories:', this.selectedCategories);
            console.log('DEBUG: Category workarea form array length:', this.categoryWorkareaFormArray.length);

            // Save category-workarea mappings if we have selected categories (even if no work areas are assigned)
            if (this.selectedCategories && this.selectedCategories.length > 0) {
              // Process intelligent mappings to create virtual workareas for conflicts
              const processedMappings = this.processIntelligentMappings(currentCategoryWorkareaMappings);

              this.departmentService.saveCategoryWorkareaMappings(this.tenantId, processedMappings)
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                  next: (workareaSuccess) => {

                    this.isSaving = false;
                    if (workareaSuccess) {
                      this.showSuccess('All mappings saved successfully!');
                      this.currentMappings = mappingsToSave;
                      this.categoryWorkareaMappings = processedMappings; // Update with processed mappings
                      this.emitCurrentMappings();
                      this.categoryWorkareaMappingsChanged.emit(processedMappings);
                      this.closeDialog();
                    } else {
                      this.showError('Failed to save category-workarea mappings. Please try again.');
                    }
                    this.cdr.detectChanges();
                  },
                  error: () => {
                    this.isSaving = false;
                    this.showError('An error occurred while saving category-workarea mappings. Please try again.');
                    this.cdr.detectChanges();
                  }
                });
            } else {
              // No category-workarea mappings to save, just close
              this.isSaving = false;
              this.showSuccess('Department-category mappings saved successfully!');
              this.currentMappings = mappingsToSave;
              this.emitCurrentMappings();
              this.closeDialog();
              this.cdr.detectChanges();
            }
          } else {
            this.isSaving = false;
            this.showError('Failed to save department-category mappings');
            this.cdr.detectChanges();
          }
        },
        error: () => {
          this.isSaving = false;
          this.showError('Failed to save department-category mappings');
          this.cdr.detectChanges();
        }
      });
  }

  // ===== CATEGORY-WORKAREA MAPPING METHODS =====

  private initializeCategoryWorkareaForm(): void {
    this.categoryWorkareaForm = this.fb.group({
      mappings: this.fb.array([])
    });
  }

  get categoryWorkareaFormArray(): FormArray {
    return this.categoryWorkareaForm.get('mappings') as FormArray;
  }

  private extractAllWorkAreas(): void {
    console.log('DEBUG: Extracting work areas from:', this.workAreas);
    const workAreaSet = new Set<string>();
    this.workAreas.forEach(branch => {
      console.log('DEBUG: Processing branch:', branch.branchName, 'with work areas:', branch.workAreas);
      branch.workAreas.forEach(wa => workAreaSet.add(wa));
    });
    this.allWorkAreas = Array.from(workAreaSet).sort();
    console.log('DEBUG: All extracted work areas:', this.allWorkAreas);
  }

  private buildCategoryWorkareaFormFromSelectedCategories(): void {
    console.log('DEBUG: Building category-workarea form for categories:', this.selectedCategories);

    // Check if we need to rebuild the form (categories changed)
    const currentCategories = [];
    for (let i = 0; i < this.categoryWorkareaFormArray.length; i++) {
      const formGroup = this.categoryWorkareaFormArray.at(i) as FormGroup;
      const categoryName = formGroup.get('categoryName')?.value;
      if (categoryName) {
        currentCategories.push(categoryName);
      }
    }

    // If categories haven't changed and form is not empty, don't rebuild
    if (JSON.stringify(currentCategories.sort()) === JSON.stringify(this.selectedCategories.sort()) &&
        this.categoryWorkareaFormArray.length > 0 && this.selectedCategories.length > 0) {

      return;
    }

    // Store current form values to preserve user selections
    const currentFormValues = new Map<string, string[]>();
    for (let i = 0; i < this.categoryWorkareaFormArray.length; i++) {
      const formGroup = this.categoryWorkareaFormArray.at(i) as FormGroup;
      const categoryName = formGroup.get('categoryName')?.value;
      const workAreas = formGroup.get('workAreas')?.value || [];
      if (categoryName) {
        currentFormValues.set(categoryName, workAreas);
      }
    }

    // Clear existing form array
    while (this.categoryWorkareaFormArray.length !== 0) {
      this.categoryWorkareaFormArray.removeAt(0);
    }

    // Add form groups for selected categories
    this.selectedCategories.forEach(categoryName => {
      const existingMapping = this.categoryWorkareaMappings.find(m => m.categoryName === categoryName);
      let workAreasToUse: string[] = [];

      // Priority: current form values > existing saved mappings > empty array
      if (currentFormValues.has(categoryName)) {
        workAreasToUse = currentFormValues.get(categoryName) || [];
      } else if (existingMapping && existingMapping.workAreas) {
        // For existing mappings, we need to extract the physical workarea names
        // because the saved workAreas might contain virtual names
        if (existingMapping.virtualWorkAreas && existingMapping.virtualWorkAreas.length > 0) {
          // If virtual workareas exist, extract physical workarea names
          workAreasToUse = existingMapping.virtualWorkAreas.map(vw => vw.physicalWorkarea);
        } else {
          // No virtual workareas, use workAreas directly (they should be physical names)
          if (Array.isArray(existingMapping.workAreas)) {
            workAreasToUse = existingMapping.workAreas;
          } else if (typeof existingMapping.workAreas === 'string') {
            workAreasToUse = [existingMapping.workAreas];
          }
        }
      }



      const mappingGroup = this.fb.group({
        categoryName: [categoryName],
        workAreas: [workAreasToUse]
      });

      this.categoryWorkareaFormArray.push(mappingGroup);
    });


    this.updateCategoryWorkareaMappings();
    this.cdr.detectChanges();
  }

  getAvailableWorkAreas(_categoryIndex: number): string[] {
    // Return all workareas - smart mapping will handle conflicts
    return this.allWorkAreas;
  }

  onWorkAreasChange(categoryIndex: number, selectedWorkAreas: string[]): void {
    console.log('DEBUG: Work areas changed for category index', categoryIndex, 'to:', selectedWorkAreas);
    const formGroup = this.categoryWorkareaFormArray.at(categoryIndex) as FormGroup;
    const categoryName = formGroup.get('categoryName')?.value;
    console.log('DEBUG: Category name for index', categoryIndex, ':', categoryName);
    const currentWorkAreas = formGroup.get('workAreas')?.value || [];
    console.log('DEBUG: Current work areas:', currentWorkAreas);

    // Only update if the values actually changed
    if (JSON.stringify(currentWorkAreas.sort()) !== JSON.stringify(selectedWorkAreas.sort())) {
      console.log('DEBUG: Work areas actually changed, updating form and mappings');
      formGroup.get('workAreas')?.setValue(selectedWorkAreas);
      this.updateCategoryWorkareaMappings();
    } else {
      console.log('DEBUG: Work areas did not change, skipping update');
    }
  }

  private updateCategoryWorkareaMappings(): void {
    this.categoryWorkareaMappings = this.getCurrentCategoryWorkareaMappings();
    console.log('DEBUG: Updated category-workarea mappings:', this.categoryWorkareaMappings);
  }

  private processIntelligentMappings(mappings: CategoryWorkareaMapping[]): CategoryWorkareaMapping[] {

    // Smart mapping logic - handle conflicts by creating virtual workareas
    const processedMappings: CategoryWorkareaMapping[] = [];
    const workareaUsage = new Map<string, string[]>(); // workarea -> categories using it

    // Track workarea usage
    mappings.forEach(mapping => {
      mapping.workAreas.forEach(workarea => {
        if (!workareaUsage.has(workarea)) {
          workareaUsage.set(workarea, []);
        }
        workareaUsage.get(workarea)!.push(mapping.categoryName);
      });
    });



    // Process each mapping
    mappings.forEach(mapping => {
      const processedMapping: CategoryWorkareaMapping = {
        categoryName: mapping.categoryName,
        workAreas: [],
        virtualWorkAreas: []
      };

      mapping.workAreas.forEach(physicalWorkarea => {
        const categoriesUsingWorkarea = workareaUsage.get(physicalWorkarea) || [];

        if (categoriesUsingWorkarea.length === 1) {
          // No conflict - use original physical workarea name
          processedMapping.workAreas.push(physicalWorkarea);
        } else {
          // Conflict detected - create virtual workarea
          const virtualWorkarea = `${physicalWorkarea}_${mapping.categoryName.toUpperCase()}`;
          processedMapping.workAreas.push(virtualWorkarea);

          // Find the department for this category
          const departmentMapping = this.getCurrentMappings().find(dm =>
            dm.categories.includes(mapping.categoryName)
          );

          processedMapping.virtualWorkAreas!.push({
            physicalWorkarea: physicalWorkarea,
            virtualWorkarea: virtualWorkarea,
            departmentId: departmentMapping?.departmentId || '',
            departmentName: departmentMapping?.departmentName || '',
            isVirtual: true
          });
        }
      });

      processedMappings.push(processedMapping);
    });


    return processedMappings;
  }

  trackByCategoryName(index: number, item: any): string {
    return item.get('categoryName')?.value || index.toString();
  }

  trackByWorkArea(_index: number, item: string): string {
    return item;
  }

  get hasValidationErrors(): boolean {
    return this.validationErrors.length > 0;
  }

  closeDialog(): void {
    this.dialogClosed.emit();
  }
}
