<div class="unified-department-mapping" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading departments and categories...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    
    <!-- Step 1: Department Selection -->
    <div class="departments-section">
      <div class="section-header">
        <mat-icon>business</mat-icon>
        <h4>1. Select Your Departments</h4>
      </div>

      <mat-form-field *ngIf="departments.length > 0" appearance="outline" class="department-filter-field">
        <mat-select [formControl]="selectedDepartmentsCtrl" multiple
          [placeholder]="'Select departments (' + selectedDepartmentsCount + '/' + filteredDepartments.length + ')'">
          
          <!-- Search -->
          <mat-option>
            <ngx-mat-select-search [formControl]="departmentFilterCtrl" 
              placeholderLabel="Search departments..."
              noEntriesFoundLabel="No departments found">
            </ngx-mat-select-search>
          </mat-option>
          
          <!-- Select All / Deselect All -->
          <div class="select-all-custom-option" (click)="toggleAllDepartments($event)">
            <strong>{{areAllDepartmentsSelected() ? 'Deselect All' : 'Select All'}}</strong>
          </div>
          <mat-divider></mat-divider>
          
          <!-- Department Options -->
          <mat-option *ngFor="let department of filteredDepartments; trackBy: trackByIndex" [value]="department.id">
            {{department.name}} <span *ngIf="department.code">({{department.code}})</span>
          </mat-option>
        </mat-select>
      </mat-form-field>

      <div *ngIf="departments.length === 0" class="no-data-message">
        <mat-icon>info</mat-icon>
        <p>No departments available</p>
      </div>
    </div>

    <!-- Step 2: Department-Category Mapping -->
    <div class="department-mapping-section" *ngIf="hasSelectedDepartments">
      <div class="section-header">
        <mat-icon>account_tree</mat-icon>
        <h4>2. Assign Categories to Departments</h4>
      </div>

      <form [formGroup]="mappingForm">
        <div formArrayName="mappings" class="mappings-container">
          <div *ngFor="let mappingGroup of mappingsFormArray.controls; let i = index; trackBy: trackByDepartmentId" 
               [formGroupName]="i" class="mapping-row">
            
            <!-- Department Info -->
            <div class="department-info">
              <mat-icon class="department-icon">business</mat-icon>
              <div class="department-details">
                <h5>{{mappingGroup.get('departmentName')?.value}}</h5>
                <span class="department-id">ID: {{mappingGroup.get('departmentId')?.value}}</span>
              </div>
            </div>

            <!-- Category Selection -->
            <div class="category-selection">
              <mat-form-field appearance="outline" class="categories-field">
                <mat-label>Select Categories</mat-label>
                <mat-select formControlName="categories" multiple>
                  <mat-option *ngFor="let category of getAvailableCategories(i)" [value]="category">
                    {{category}}
                  </mat-option>
                </mat-select>
                <mat-hint>
                  {{mappingGroup.get('categories')?.value?.length || 0}} categories selected
                </mat-hint>
              </mat-form-field>
            </div>
          </div>
        </div>
      </form>

      <!-- No Categories Available Message -->
      <div *ngIf="categories.length === 0" class="no-data-message">
        <mat-icon>info</mat-icon>
        <p>No categories available for mapping</p>
      </div>
    </div>

    <!-- Step 3: Category-Workarea Mapping -->
    <div class="category-workarea-section" *ngIf="hasSelectedCategories">
      <div class="section-header">
        <mat-icon>work</mat-icon>
        <h4>3. Assign Work Areas to Categories</h4>
      </div>

      <div class="workarea-mapping-container">
        <!-- <div class="selected-categories-info">
          <p><strong>Selected Categories:</strong> {{selectedCategoriesFromDepartments.join(', ')}}</p>
        </div> -->

        <!-- Smart Mapping Info -->
        <div class="smart-mapping-info">
          <div class="info-card">
            <mat-icon class="info-icon">auto_awesome</mat-icon>
            <div class="info-content">
              <h4>Smart Mapping Enabled</h4>
              <p>Select any workarea for any category. The system will automatically handle conflicts and create unique mappings when needed.</p>
            </div>
          </div>
        </div>

        <!-- Validation Errors -->
        <div *ngIf="hasValidationErrors" class="validation-errors">
          <mat-icon class="error-icon">error</mat-icon>
          <div class="error-list">
            <p *ngFor="let error of validationErrors" class="error-message">{{ error }}</p>
          </div>
        </div>

        <!-- Category-Workarea Mapping Form -->
        <form [formGroup]="categoryWorkareaForm" class="mapping-form">
          <div formArrayName="mappings" class="mappings-container">
            <!-- Empty State Message -->
            <div *ngIf="categoryWorkareaFormArray.controls.length === 0" class="empty-state-message">
              <mat-icon class="empty-icon">arrow_back</mat-icon>
              <p>Select categories from the departments above to configure work area mappings</p>
            </div>

            <!-- Mapping Rows -->
            <div *ngFor="let mappingGroup of categoryWorkareaFormArray.controls; let i = index; trackBy: trackByCategoryName"
              [formGroupName]="i" class="mapping-row">
              <div class="category-name">
                <mat-icon class="category-icon">category</mat-icon>
                <span>{{ mappingGroup.get('categoryName')?.value || 'Unknown Category' }}</span>
              </div>

              <div class="workareas-dropdown">
                <mat-form-field appearance="outline" class="workareas-field">
                  <mat-select formControlName="workAreas" multiple
                    placeholder="Select work areas"
                    (selectionChange)="onWorkAreasChange(i, $event.value)"
>
                    <mat-option *ngFor="let workArea of getAvailableWorkAreas(i); trackBy: trackByWorkArea"
                      [value]="workArea">
                      {{ workArea }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Summary Section (only show when not in dialog) -->
    <div class="summary-section" *ngIf="hasSelectedDepartments && !showAsDialog">
      <div class="section-header">
        <mat-icon>summarize</mat-icon>
        <h4>Mapping Summary</h4>
      </div>
      
      <div class="summary-cards">
        <div class="summary-card">
          <div class="summary-number">{{selectedDepartmentsCount}}</div>
          <div class="summary-label">Departments Selected</div>
        </div>

        <div class="summary-card">
          <div class="summary-number">{{mappedDepartmentsCount}}</div>
          <div class="summary-label">Departments Mapped</div>
        </div>

        <div class="summary-card">
          <div class="summary-number">{{totalCategoriesAssigned}}</div>
          <div class="summary-label">Total Categories Assigned</div>
        </div>
      </div>
    </div>

    <!-- Dialog Actions (only show when in dialog mode) -->
    <div class="dialog-actions" *ngIf="showAsDialog">
      <button mat-raised-button (click)="closeDialog()">
        <mat-icon>close</mat-icon>
        Cancel
      </button>

      <button mat-raised-button color="primary"
              (click)="saveAndClose()"
              [disabled]="isSaving || !hasValidMappings"
              class="save-button">
        <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
        <mat-icon *ngIf="!isSaving">save</mat-icon>
        {{ isSaving ? 'Saving...' : 'Save & Close' }}
      </button>
    </div>

    <!-- Empty State -->
    <div *ngIf="!hasSelectedDepartments" class="empty-state">
      <mat-icon class="empty-icon">business</mat-icon>
      <h3>Select Departments to Begin</h3>
      <p>Choose one or more departments from the dropdown above to start configuring category mappings.</p>
    </div>
  </div>
</div>
