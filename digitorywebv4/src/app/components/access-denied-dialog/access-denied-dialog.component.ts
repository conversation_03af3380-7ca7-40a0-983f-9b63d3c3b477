import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

export interface AccessDeniedDialogData {
  title: string;
  message: string;
}

@Component({
  selector: 'app-access-denied-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="access-denied-dialog">
      <h3>{{ data.title }}</h3>
      <p>{{ data.message }}</p>
      <button mat-raised-button color="primary" [mat-dialog-close]="true">OK</button>
    </div>
  `,
  styles: [`
    .access-denied-dialog {
      padding: 24px;
      text-align: center;
      min-width: 300px;
    }

    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    p {
      margin: 0 0 24px 0;
      font-size: 14px;
      line-height: 1.4;
      color: #666;
    }

    button {
      min-width: 80px;
    }
  `]
})
export class AccessDeniedDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<AccessDeniedDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AccessDeniedDialogData
  ) {}
}
