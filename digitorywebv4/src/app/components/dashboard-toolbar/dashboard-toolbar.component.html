<mat-toolbar>
  <div class="toolbar-left">
    <!-- Main Navigation Menu -->
    <div class="nav-menu">
      <!-- Use ngIf/else to ensure only one set of tabs is shown -->
      <ng-container *ngIf="menuItems?.length > 0; else placeholderTabs">
        <ng-container *ngFor="let item of menuItems; trackBy: trackByPath">
          <a mat-button [routerLink]="item.path" routerLinkActive="active" class="nav-item">
            <mat-icon>{{item.icon}}</mat-icon>
            <span>{{item.title}}</span>
          </a>
        </ng-container>
      </ng-container>

      <!-- Template for placeholder tabs -->
      <ng-template #placeholderTabs>
        <a mat-button class="nav-item placeholder-tab">
          <mat-icon>dashboard</mat-icon>
          <span>Dashboard</span>
        </a>
        <a mat-button class="nav-item placeholder-tab">
          <mat-icon>table_chart</mat-icon>
          <span>Inventory Management</span>
        </a>
        <a mat-button class="nav-item placeholder-tab">
          <mat-icon>person</mat-icon>
          <span>User Management</span>
        </a>
        <a mat-button class="nav-item placeholder-tab">
          <mat-icon>fastfood</mat-icon>
          <span>Recipe Management</span>
        </a>
        <a mat-button class="nav-item placeholder-tab">
          <mat-icon>event_note</mat-icon>
          <span>Party Management</span>
        </a>
      </ng-template>
    </div>
  </div>

  <span class="example-spacer"></span>

  <p class="formal-text">{{ versionNumber }} <span class="beta-tag">Beta</span></p>
  <div class="user-info">
    <button mat-button [matMenuTriggerFor]="beforeMenu" class="user-menu-button">
      <mat-icon>account_circle</mat-icon>
      <div class="user-details">
        <span class="user-name">{{ user?.name }}</span>
        <span class="user-role">{{ cardDesc }}</span>
      </div>
    </button>
  </div>

  <mat-menu #beforeMenu="matMenu" xPosition="before">
    <button mat-menu-item (click)="setting()" [disabled]="!enableSettingBtn">
      <i class="fa-solid fa-gear"></i> &nbsp; Setting
    </button>
    <button mat-menu-item (click)="logout()">
      <i class="fa-solid fa-right-from-bracket"></i> &nbsp; Logout
    </button>
  </mat-menu>
</mat-toolbar>