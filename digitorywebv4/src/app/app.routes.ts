import { Routes } from '@angular/router';
import { routesConfig } from './config/routes';
import { AuthGuard } from './_guards/auth.guard';
import { LoginGuard } from './_guards/login.guard';
export const routes: Routes = [{
    path: routesConfig.dashboard.routerPath,
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/dashboard/dashboard.component').then((c) => c.DashboardComponent),
    loadChildren: () =>
      import('./dashboard.routes').then((r) => r.dashboardroutes),
  },
  {
    path: routesConfig.signin.routerPath,
    canActivate: [LoginGuard],
    loadComponent: () =>
      import('./pages/signin/signin.component').then((c) => c.SigninComponent),
  },
  {
    path: '',
    pathMatch: 'full',
    redirectTo: '/signin',
  },
  {
    path:'*',
    redirectTo:'/signin',
  }
];
