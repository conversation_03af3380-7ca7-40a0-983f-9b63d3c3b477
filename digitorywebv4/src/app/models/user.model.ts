export class User{
    name : string;
    tenantId?: string;
    restaurantId?: string;
    mobile?: string;
    company?: string;
    companyId?: string;
    email: string;
    mId?: string;
    role: string[];
    type?: string;
    restaurantAccess?: any[]
    token? : string;
    tenantName? : string;
    uType? : string;
}
export class Inventory {
    itemName!: string;
    itemCode!: string;
    category!: string[];
    subCategory!: string[];
    classification!: string[];
    vendor!: string[];
    inventoryUom!: string;
    closingUOM!: string;
    procuredAt!: string[];;
    issuedTo!: string[];;
    taxRate!: number;
    weight!: number;
    yield!: number;
    rate!: number;
    finalRate!: number;
    leadTime!: number;
    discontinued!: string[];
    row_uuid!: string;
  }

  export class Package {
    category!: string[];
    subCategory!: string[];
    itemCode!: string;
    itemName!: string;
    packageName!: string;
    brand!: string;
    unitUOM!: string[];
    unitPerPackage!: number;
    qtyPerUnit!: number;
    totalQtyOfPackage!: number;
    emptyBottleWeight!: number;
    fullBottleWeight!: number;
    price!: number;
    row_uuid!: string;
    discontinued!: string[];
  }
  
  export class Vendor {
    firstName!: string;
    lastName!: string;
    email!: string;
    mobile!: number;
    weight!: number;
    height!: number;
    bmi!: number;
    bmiResult!: string;
    gender!: string;
    requireTrainer!: string;
    package!: string;
    important!: string[];
    haveGymBefore!: string;
    enquiryDate!: string;
    id!: number;
    row_uuid!: string;
  }
  export class SubrecipeRecipe {
    itemName!: string;
    itemCode!: string;
    category!: string[];
    subCategory!: string[];
    classification!: string[];
    vendor!: string[];
    inventoryUom!: string;
    closingUom!: string;
    procuredAt!: string[];;
    issuedTo!: string[];;
    taxRate!: number;
    weight!: number;
    yield!: number;
    rate!: number;
    finalRate!: number;
    leadTime!: number;
    discontinued!: string[];
  }
  export class subrecipeMaster {
    itemName!: string;
    itemCode!: string;
    category!: string[];
    subCategory!: string[];
    classification!: string[];
    vendor!: string[];
    inventoryUom!: string;
    closingUom!: string;
    procuredAt!: string[];;
    issuedTo!: string[];;
    taxRate!: number;
    weight!: number;
    yield!: number;
    rate!: number;
    finalRate!: number;
    leadTime!: number;
    discontinued!: string[];
  }
  export class MenuRecipe {
    itemName!: string;
    itemCode!: string;
    category!: string[];
    subCategory!: string[];
    classification!: string[];
    vendor!: string[];
    inventoryUom!: string;
    closingUom!: string;
    procuredAt!: string[];;
    issuedTo!: string[];;
    taxRate!: number;
    weight!: number;
    yield!: number;
    rate!: number;
    finalRate!: number;
    leadTime!: number;
    discontinued!: string[];
  }
  export class MenuMaster {
    itemName!: string;
    itemCode!: string;
    category!: string[];
    subCategory!: string[];
    classification!: string[];
    vendor!: string[];
    inventoryUom!: string;
    closingUom!: string;
    procuredAt!: string[];;
    issuedTo!: string[];;
    taxRate!: number;
    weight!: number;
    yield!: number;
    rate!: number;
    finalRate!: number;
    leadTime!: number;
    discontinued!: string[];
  }

  export class MenuMapping {
    itemName!: string;
    itemCode!: string;
    category!: string[];
    subCategory!: string[];
    classification!: string[];
    vendor!: string[];
    inventoryUom!: string;
    closingUom!: string;
    procuredAt!: string[];;
    issuedTo!: string[];;
    taxRate!: number;
    weight!: number;
    yield!: number;
    rate!: number;
    finalRate!: number;
    leadTime!: number;
    discontinued!: string[];
  }
