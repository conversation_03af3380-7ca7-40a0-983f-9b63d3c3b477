import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  horizontalPositionMessage: MatSnackBarHorizontalPosition = 'center';
  verticalPosition: MatSnackBarVerticalPosition = 'top'
  constructor(private snackBar: MatSnackBar) { 

  }

  openSnackBar(message: string): void {
    let action = '❌'
    this.snackBar.open(message, action, {
      duration: 100000,
      horizontalPosition: this.horizontalPositionMessage,
      verticalPosition: this.verticalPosition,
      panelClass : ['message-snackbar']
    });
  }

  snackBarShowSuccess(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 5000,
      panelClass : ['success-snackbar']
    })
  }

  snackBarShowError(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 5000,
      panelClass : ['error-snackbar']
    })
  }

  snackBarShowWarning(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 5000,
      panelClass : ['warning-snackbar']
    })
  }

  snackBarShowInfo(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 5000,
      panelClass : ['info-snackbar']
    })
  }

  snackBarForSync(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      panelClass : ['warning-snackbar']
    })
  }
  truncateAndFloor(number, precision = 3) {
    try {
        if (typeof number === 'string') {
            number = parseFloat(number);
        }
        if (isNaN(number)) {
            number = 0;
        }
        const factor = Math.pow(10, precision);
        const truncatedNumber = Math.trunc(number * factor) / factor;
        return Math.floor(truncatedNumber * Math.pow(10, precision)) / Math.pow(10, precision);
    } catch (error) {
        console.error('Error occurred in truncateNew:', error);
        return 0;
    }
}

}
