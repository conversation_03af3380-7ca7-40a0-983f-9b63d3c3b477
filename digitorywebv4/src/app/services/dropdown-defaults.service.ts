import { Injectable } from '@angular/core';
import { FormControl } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class DropdownDefaultsService {

  constructor() { }

  /**
   * Sets default selection for a dropdown based on whether it's multi-select or single-select
   * @param formControl - The FormControl to set the value on
   * @param options - Array of available options
   * @param isMultiSelect - Whether the dropdown supports multiple selection
   * @param emptyValue - Value to set when no options are available (default: [] for multi-select, null for single-select)
   */
  setDefaultSelection(
    formControl: FormControl, 
    options: any[], 
    isMultiSelect: boolean = false,
    emptyValue?: any
  ): void {
    if (!formControl || !Array.isArray(options)) {
      return;
    }

    if (options.length === 0) {
      // No options available
      const defaultEmpty = emptyValue !== undefined ? emptyValue : (isMultiSelect ? [] : null);
      formControl.setValue(defaultEmpty);
      return;
    }

    if (isMultiSelect) {
      // Multi-select: select all options by default
      formControl.setValue([...options]);
    } else {
      // Single-select: select first option by default
      formControl.setValue(options[0]);
    }
  }

  /**
   * Sets default selection for location dropdowns specifically
   * @param formControl - The FormControl to set the value on
   * @param branches - Array of branch/location objects
   * @param isMultiSelect - Whether the dropdown supports multiple selection
   * @param valueProperty - Property name to extract from branch objects (default: 'restaurantIdOld')
   */
  setDefaultLocationSelection(
    formControl: FormControl,
    branches: any[],
    isMultiSelect: boolean = false,
    valueProperty: string = 'restaurantIdOld'
  ): void {

    if (!formControl || !Array.isArray(branches)) {
      return;
    }

    if (branches.length === 0) {
      const emptyValue = isMultiSelect ? [] : null;
      formControl.setValue(emptyValue);
      return;
    }

    if (isMultiSelect) {
      // Multi-select: select all branch IDs by default
      const allValues = branches.map(branch => branch[valueProperty]);
      formControl.setValue(allValues);
    } else {
      // Single-select: select first branch ID by default
      const singleValue = branches[0][valueProperty];
      formControl.setValue(singleValue);
    }
  }

  /**
   * Sets default selection for category/subcategory dropdowns (always multi-select)
   * @param formControl - The FormControl to set the value on
   * @param categories - Array of category strings
   */
  setDefaultCategorySelection(formControl: FormControl, categories: string[]): void {
    this.setDefaultSelection(formControl, categories, true);
  }

  /**
   * Sets default selection for department dropdowns (always multi-select)
   * @param formControl - The FormControl to set the value on
   * @param departments - Array of department objects
   * @param valueProperty - Property name to extract from department objects (default: 'id')
   */
  setDefaultDepartmentSelection(
    formControl: FormControl,
    departments: any[],
    valueProperty: string = 'id'
  ): void {
    if (!formControl || !Array.isArray(departments)) {
      return;
    }

    if (departments.length === 0) {
      formControl.setValue([]);
      return;
    }

    const allDepartmentIds = departments.map(dept => dept[valueProperty]);
    formControl.setValue(allDepartmentIds);
  }

  /**
   * Sets default selection for work area dropdowns
   * @param formControl - The FormControl to set the value on
   * @param workAreas - Array of work area objects (can be nested in branch structure)
   * @param isMultiSelect - Whether the dropdown supports multiple selection
   * @param flattenWorkAreas - Whether to flatten nested work areas from branch structure
   */
  setDefaultWorkAreaSelection(
    formControl: FormControl,
    workAreas: any[],
    isMultiSelect: boolean = true,
    flattenWorkAreas: boolean = false
  ): void {
    if (!formControl || !Array.isArray(workAreas)) {
      return;
    }

    if (workAreas.length === 0) {
      formControl.setValue(isMultiSelect ? [] : null);
      return;
    }

    let options = workAreas;
    if (flattenWorkAreas) {
      // Flatten work areas from branch structure
      options = workAreas.flatMap(branch => branch.workAreas || []);
    }

    this.setDefaultSelection(formControl, options, isMultiSelect);
  }

  /**
   * Utility method to check if all options are selected in a multi-select dropdown
   * @param formControl - The FormControl to check
   * @param totalOptions - Total number of available options
   * @returns boolean indicating if all options are selected
   */
  areAllOptionsSelected(formControl: FormControl, totalOptions: number): boolean {
    const selectedValues = formControl.value;
    if (!Array.isArray(selectedValues)) {
      return false;
    }
    return selectedValues.length === totalOptions && totalOptions > 0;
  }

  /**
   * Toggles between select all and deselect all for multi-select dropdowns
   * @param formControl - The FormControl to toggle
   * @param allOptions - Array of all available options
   * @param emptyValue - Value to set when deselecting all (default: [])
   */
  toggleSelectAll(formControl: FormControl, allOptions: any[], emptyValue: any[] = []): void {
    if (!formControl || !Array.isArray(allOptions)) {
      return;
    }

    const isAllSelected = this.areAllOptionsSelected(formControl, allOptions.length);

    if (isAllSelected) {
      formControl.setValue(emptyValue);
    } else {
      formControl.setValue([...allOptions]);
    }
  }
}
