import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
@Injectable({
  providedIn: 'root'
})
export class ToasterService {

  constructor(private snackBar: MatSnackBar) { }

  showSuccess(message: string, duration: number) {
    this.snackBar.open(message, 'X', {
      duration: duration,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: 'snackbar-show'
    })
  }

  showError(message: string, duration: number) {
    this.snackBar.open(message, 'X', {
      duration: duration,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: 'snackbar-show'

    })
  }

  showInfo(message: string, duration: number) {
    this.snackBar.open(message, 'X', {
      duration: duration,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: 'snackbar-show'
    })
  }


}
