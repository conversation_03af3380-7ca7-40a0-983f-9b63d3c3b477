import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Country, Customer, colorentity } from '../models/http-table-example.interface';
import { BehaviorSubject, Subject } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class MasterDataService {
  private isCheckedSubject = new BehaviorSubject<boolean>(false);
    isChecked$ = this.isCheckedSubject.asObservable();
    
  setChecked(isChecked: boolean) {
    this.isCheckedSubject.next(isChecked);
  }

  private getRoute = new BehaviorSubject<string>(undefined);
  route$ = this.getRoute.asObservable();

  setNavigation(route: string) {
    this.getRoute.next(route);
  }

  private refreshTableSubject = new Subject<void>();
  refreshTable$ = this.refreshTableSubject.asObservable();

  triggerRefreshTable() {
    this.refreshTableSubject.next();
  }

  constructor(private http: HttpClient) { }
  inventoryMapping = [
    {'value': 'position', 'displayName': 'No.'},
    {'value': 'action', 'displayName': 'action'},
    {'value': 'modified', 'displayName': 'modified'},
    {'value': 'Discontinued', 'displayName': 'Discontinued'},
    {'value': 'category', 'displayName': 'Category'},
    {'value': 'subCategory', 'displayName': 'Sub Category'},
    {'value': 'itemCode', 'displayName': 'Item Code'},
    {'value': 'itemName', 'displayName': 'Item Name'},
    {'value': 'classification', 'displayName': 'Classification'},
    {'value': 'vendor', 'displayName': 'Vendor'},
    {'value': 'Inventory UOM', 'displayName': 'Unit UOM'},
    {'value': 'closingUOM', 'displayName': 'Closing UOM'},
    {'value': 'procuredAt', 'displayName': 'Procured At'},
    {'value': 'issuedTo', 'displayName': 'Issued To'},
    {'value': 'taxRate', 'displayName': 'Tax Rate %'},
    {'value': 'weight', 'displayName': 'Weight'},
    {'value': 'yield', 'displayName': 'Yield'},
    {'value': 'rate', 'displayName': 'Rate'},
    {'value': 'finalRate', 'displayName': 'Final Rate'},
    {'value': 'leadTime(days)', 'displayName': 'Lead Time (days)'},
  ]

  packagingMapping = [
    {'value': 'position', 'displayName': 'No.'},
    {'value': 'action', 'displayName': 'action'},
    {'value': 'modified', 'displayName': 'modified'},
    {'value': 'category', 'displayName': 'Category'},
    {'value': 'subCategory', 'displayName': 'Sub Category'},
    {'value': 'InventoryCode', 'displayName': 'Item Code'},
    {'value': 'ItemName', 'displayName': 'Item Name'},
    {'value': 'PackageName', 'displayName': 'Package Name'},
    {'value': 'Units/ package', 'displayName': 'Units per Package'},
    {'value': 'Quantity per unit', 'displayName': 'Quantity per Unit'},
    {'value': 'Total qty of package', 'displayName': 'Total Quantity of Package'},
    {'value': 'UnitUOM', 'displayName': 'Unit UOM'},
    {'value': 'Empty bottle weight', 'displayName': 'Empty Bottle Weight'},
    {'value': 'Full bottle weight', 'displayName': 'Full Bottle Weight'},
    {'value': 'PackagePrice', 'displayName': 'Package Price'},
  ]

vendorMapping = [
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'modified', 'displayName': 'modified'},
  {'value': 'Discontinued', 'displayName': 'Discontinued'},
  {'value': 'vendorTenantId', 'displayName': 'Vendor ID'},
  {'value': 'vendorName', 'displayName': 'Vendor Name'},
  {'value': 'contactName', 'displayName': 'Contact Name'},
  {'value': 'contactNo', 'displayName': 'Contact Number'},
  {'value': 'email', 'displayName': 'Email'},
  {'value': 'cinNo', 'displayName': 'CIN No'},
  {'value': 'gstNo', 'displayName': 'GST No'},
  {'value': 'panNo', 'displayName': 'PAN No'},
  {'value': 'tinNo', 'displayName': 'TIN No'},
]

accountMapping = [
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'tenantName', 'displayName': 'Tenant Name'},
  {'value': 'tenantId', 'displayName': 'Tenant ID'},
  // {'value': 'shortName', 'displayName': 'Short Name'},
  {'value': 'emailId', 'displayName': 'Email'},
  // {'value': 'accountNo', 'displayName': 'Account No'},
  {'value': 'password', 'displayName': 'Password'},
  {'value': 'account', 'displayName': 'Account Status'},
  {'value': 'sales', 'displayName': 'Sales'},
  {'value': 'forecast', 'displayName': 'Forecast'},
]

menuToWorkareaMapping =[
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'restaurantId', 'displayName': 'Restaurant ID'},
  {'value': 'storeId', 'displayName': 'Store ID'},
  {'value': 'menuItemCode', 'displayName': 'Recipe Code'},
  {'value': 'menuItemName', 'displayName': 'Recipe Name'},
  {'value': 'floorNo', 'displayName': 'Floor Number'},
  {'value': 'section', 'displayName': 'Section'},
  {'value': 'workArea', 'displayName': 'Work Area'},
]

subrecipeRecipeMapping = [
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'modified', 'displayName': 'modified'},
  {'value': 'Discontinued', 'displayName': 'Discontinued'},
  {'value': 'subRecipeCode', 'displayName': 'Subrecipe Code'},
  {'value': 'subRecipeName', 'displayName': 'Subrecipe Name'},
  {'value': 'ingredientCode', 'displayName': 'Ingredient Code'},
  {'value': 'ingredientName', 'displayName': 'Ingredient Name'},
  {'value': 'UOM', 'displayName': 'UOM'},
  {'value': 'Initialweight', 'displayName': 'Initial Weight'},
  {'value': 'Yield', 'displayName': 'Yield'},
  {'value': 'weightInUse', 'displayName': 'Weight in Use'},
  {'value': 'rate', 'displayName': 'Rate'},
  {'value': 'finalRate', 'displayName': 'Final Rate'},
]

subrecipeMasterMapping = [
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'category', 'displayName': 'Category'},
  {'value': 'subCategory', 'displayName': 'Sub Category'},
  {'value': 'menuItemCode', 'displayName': 'Item Code'},
  {'value': 'menuItemName', 'displayName': 'Item Name'},
  {'value': 'UOM', 'displayName': 'UOM'},
  {'value': 'weightInUse', 'displayName': 'Initial Weight'},
  {'value': 'rate', 'displayName': 'Cost'},
  {'value': 'yield', 'displayName': 'Yield'},
  // {'value': 'modified', 'displayName': 'modified'},
  {'value': 'preparedAt', 'displayName': 'Preparatory Location'},
  {'value': 'usedInWorkArea', 'displayName': 'Used in Work Area'},
  {'value': 'usedAtOutlet', 'displayName': 'Sales Outlet'},
  {'value': 'closingUOM', 'displayName': 'Closing UOM'},
  {'value': 'Discontinued', 'displayName': 'Discontinued'}
]

menuRecipeMapping =[
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'modified', 'displayName': 'modified'},
  {'value': 'Category', 'displayName': 'Category'},
  {'value': 'Sub Category', 'displayName': 'Sub-Category'},
  {'value': 'menuItemCode', 'displayName': 'Recipe Code'},
  {'value': 'menuItemName', 'displayName': 'Recipe Name'},
  {'value': 'ingredientCode', 'displayName': 'Ingredient Code'},
  {'value': 'ingredientName', 'displayName': 'Ingredient Name'},
  {'value': 'ConsumptionUOM', 'displayName': 'Consumption UOM'},
  {'value': 'InitialWeight', 'displayName': 'Initial Weight'},
  {'value': 'Yield', 'displayName': 'Yield'},
  {'value': 'weightInUse', 'displayName': 'Weight in Use'},
  {'value': 'rate', 'displayName': 'Rate'},
  {'value': 'finalRate', 'displayName': 'Final Rate'},
  {'value': 'Discontinued', 'displayName': 'Discontinued'}
]

menuMasterMapping = [
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  // {'value': 'modified', 'displayName': 'modified'},
  {'value': 'category', 'displayName': 'Category'},
  {'value': 'subCategory', 'displayName': 'Sub-Category'},
  {'value': 'menuItemCode', 'displayName': 'Recipe Code'},
  {'value': 'menuItemName', 'displayName': 'Recipe Name'},
  {'value': 'Discontinued', 'displayName': 'Discontinued'},
  {'value': 'weight', 'displayName': 'Weight'},
  {'value': 'rate', 'displayName': 'Cost'},
  // {'value': 'itemType', 'displayName': 'Item Type'},
  {'value': 'preparedAt', 'displayName': 'Preparatory Location'},
  {'value': 'usedInWorkArea', 'displayName': 'Used in Work Area'},
  {'value': 'usedAtOutlet', 'displayName': 'Sales Outlet'},
  {'value': 'closingUOM', 'displayName': 'Closing UOM'},
  {'value': 'servingSize', 'displayName': 'Serving Size'},
]

indexMapping =[
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'modified', 'displayName': 'modified'},
  {'value': 'Discontinued', 'displayName': 'Discontinued'},
  {'value': 'menuItemCode', 'displayName': 'Recipe Code'},
  {'value': 'menuItemName', 'displayName': 'Recipe Name'},
  {'value': 'category', 'displayName': 'Category'},
  {'value': 'subCategory', 'displayName': 'Sub Category'},
]

userMapping =[
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'Action'},
  {'value': 'modified', 'displayName': 'Modified'},
  {'value': 'Discontinued', 'displayName': 'Status'},
  {'value': 'roles', 'displayName': 'Role'},
  {'value': 'email', 'displayName': 'Email'},
  {'value': 'branchId', 'displayName': 'Permitted Branches'},
  {'value': 'workAreas', 'displayName': 'Permitted Areas'},
  {'value': 'password', 'displayName': 'Password'},
  {'value': 'firstName', 'displayName': 'First Name'},
  {'value': 'lastName', 'displayName': 'Last Name'},
  {'value': 'mobile', 'displayName': 'Mobile'},
]

rolesMapping = [
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'action'},
  {'value': 'modified', 'displayName': 'Modified'},
  {'value': 'Discontinued', 'displayName': 'Status'},
  {'value': 'role', 'displayName': 'Role'},
  {'value': 'module', 'displayName': 'Module'},
  {'value': 'Page', 'displayName': 'Page'},
]

branchMapping = [ 
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'Action'},
  {'value': 'modified', 'displayName': 'Modified'},
  {'value': 'Discontinued', 'displayName': 'Status'},
  {'value': 'tenantName', 'displayName': 'Tenant'},
  {'value': 'branchName', 'displayName': 'Branch Name'},
  {'value': 'branchLocation', 'displayName': 'Branch Location'},
  {'value': 'restaurantId', 'displayName': 'Branch ID'},
  {'value': 'branchType', 'displayName': 'Branch Type'},
  {'value': 'abbreviated restaurantId', 'displayName': 'Abbreviated ID'},
  {'value': 'workArea', 'displayName': 'WorkAreas'},
  {'value': 'restaurantClosingTime', 'displayName': 'Closing Time'},
  {'value': 'storeId', 'displayName': 'Store ID'},
  {'value': 'defaultSalesFloorNo', 'displayName': 'Default Floor No'},
]

servingSizeMapping = [ 
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'Action'},
  // {'value': 'modified', 'displayName': 'Modified'},
  {'value': 'Discontinued', 'displayName': 'Status'},
  {'value': 'Serving Size', 'displayName': 'Serving Size'},
  {'value': 'Ratio', 'displayName': 'Ratio'},
  // {'value': 'Conversion Unit', 'displayName': 'Conversion Unit'},// Add this based on need
  {'value': 'Quantity', 'displayName': 'Quantity'},
  {'value': 'QuantityUnit', 'displayName': 'Quantity Unit'},
]

partyMapping = [
  {'value': 'position', 'displayName': 'No.'},
  {'value': 'action', 'displayName': 'Action'},
  {'value': 'partyName', 'displayName': 'Party Name'},
  {'value': 'partyCode', 'displayName': 'Party Code'},
  {'value': 'partyCreator', 'displayName': 'Creator Name'},
  {'value': 'phoneNumber', 'displayName': 'Number'},
  {'value': 'address', 'displayName': 'Address'},
  {'value': 'email', 'displayName': 'Email'},
  // {'value': 'totalMembers', 'displayName': 'Total Member'},
  {'value': 'startDate', 'displayName': 'Start Date'},
  {'value': 'endDate', 'displayName': 'End Date'},
  // {'value': 'time', 'displayName': 'Time'},
  {'value': 'venue', 'displayName': 'Venue'},
  // {'value': 'extraSupplies', 'displayName': 'Extra Supplies'},
  // {'value': 'paidType', 'displayName': 'Paid Type'},
  // {'value': 'paidAmount', 'displayName': 'Paid Amount'},
  {'value': 'discontinued', 'displayName': 'discontinued'},
]

mandatory_columns = {
  'inventory master': [
      'category', 'subCategory', 'itemCode', 'itemName', 'itemType', 'vendor', 'Inventory UOM', 'closingUOM', 'procuredAt', 'issuedTo', 'taxRate', 'weight', 'yield', 'rate', 'finalRate', 'leadTime(days)', 'Discontinued'
  ], 
  'packagingmasters': [
      'PackageName', 'TenantId', 'category', 'subCategory', 'InventoryCode', 'ItemName', 'Units/ package', 'Quantity per unit', 'Total qty of package', 'UnitUOM', 'Empty bottle weight', 'Full bottle weight', 'PackagePrice', 'Discontinued'
  ], 
  'menu master': [
      'subCategory', 'category', 'menuItemCode', 'menuItemName', 'weight', 'rate', 'itemType', 'preparedAt', 'usedInWorkArea', 'usedAtOutlet', 'closingUOM', 'servingSize', 'Discontinued'
  ], 
  'menu recipes': [
      'Category', 'Sub Category', 'menuItemCode', 'menuItemName', 'ingredientCode', 'ingredientName', 'ConsumptionUOM', 'InitialWeight', 'Yield', 'weightInUse', 'rate', 'finalRate', 'Discontinued'
  ], 
  'Subrecipe Master': [
      'category', 'subCategory', 'menuItemCode', 'menuItemName', 'itemType', 'preparedAt', 'usedInWorkArea', 'usedAtOutlet', 'UOM', 'weightInUse', 'yield', 'rate', 'closingUOM', 'Discontinued'
  ], 
  'Subrecipe Recipe': [
      'subRecipeCode', 'subRecipeName', 'ingredientCode', 'ingredientName', 'UOM', 'Initialweight', 'yield', 'weightInUse', 'rate', 'finalRate', 'Discontinued'
  ], 
  'servingsize conversion': [ 
      'Serving Size', 'Ratio', 'Conversion Unit', 'Quantity' 
  ], 
  'users': [
      'tenantId', 'branchId', 'email', 'password', 'roles', 'mobile', 'firstName', 'lastName', 'Discontinued', 'multiBranchUser', 'workAreas'
  ], 
  'branches': [
      'tenantId', 'tenantName', 'branchName', 'branchLocation', 'restaurantId', 'closingTimeFormat', 'restaurantClosingTime', 'storeId', 'abbreviated restaurantId', 'branchType', 'workArea'
  ], 
  'Roles': [
    'role', 'module', 'Page', 'tenantId'
  ], 
  'vendors': [
    'vendorTenantId', 'vendorName', 'contactNo', 'contactName', 'email','Discontinued'
   ], 
   'account': [
    'tenantId', 'tenantName', 'shortName', 'accountNo', 'emailId','password','forecast','sales','account'
   ],
  'menu-to-workArea-mapping': [
     'restaurantId', 'storeId', 'menuItemCode', 'menuItemName', 'floorNo', 'section', 'workArea'
     ]
}


reArrangeColumns = {
  'inventory master': [
    'category','subCategory','itemCode','itemName','classification','itemType','vendor','Inventory UOM','closingUOM','procuredAt','issuedTo','taxRate','weight','yield','recovery','rate','finalRate','leadTime(days)','Discontinued','modified','HSN_SAC'], 
'packagingmasters': [
    'PackageName',	'TenantId',	'category',	'subCategory',	'InventoryCode',	'ItemName',	'brand',	'Units/ package',	'Quantity per unit',	'Total qty of package',	'UnitUOM',	'Empty bottle weight',	'Full bottle weight',	'PackagePrice',	'Changed',	'Discontinued'],
'menu master': [
    'subCategory',	'category',	'menuItemCode',	'menuItemName',	'weight',	'rate',	'itemType',	'preparedAt',	'usedInWorkArea',	'usedAtOutlet',	'closingUOM',	'servingSize',	'Discontinued'], 
'menu recipes': [
    'Category',	'Sub Category',	'menuItemCode',	'menuItemName',	'ingredientCode',	'ingredientName',	'isModifier',	'modifierName',	'ConsumptionUOM',	'InitialWeight',	'Yield',	'weightInUse',	'rate',	'finalRate',	'Discontinued'], 
'Subrecipe Master': [
    'category',	'subCategory',	'menuItemCode',	'menuItemName',	'itemType',	'preparedAt',	'usedInWorkArea',	'usedAtOutlet',	'UOM',	'weightInUse',	'yield',	'rate',	'closingUOM',	'Discontinued'], 
'Subrecipe Recipe': [
    'subRecipeCode',	'subRecipeName',	'ingredientCode',	'ingredientName',	'UOM',	'Initialweight',	'yield',	'weightInUse',	'rate',	'finalRate',	'Discontinued'], 
'servingsize conversion': [ 
    'Serving Size',	'Ratio',	'Conversion Unit',	'Quantity'], 
'users': [
    'tenantId',	'branchId',	'email',	'password',	'roles',	'mobile',	'firstName',	'lastName',	'Discontinued',	'multiBranchUser',	'modified',	'workAreas'], 
'branches': [
    'tenantId',	'tenantName',	'branchName',	'branchLocation',	'restaurantId',	'closingTimeFormat',	'restaurantClosingTime',	'storeId',	'storeAvailable',	'abbreviated restaurantId',	'workArea',	'billTo',	'shipTo',	'panNo',	'gstNo'], 
'Roles': [
  'role',	'module',	'Page',	'tenantId'], 
'vendors': [
  'vendorTenantId',	'vendorName',	'contactNo',	'contactName',	'email',	'gstNo',	'tinNo',	'panNo',	'cinNo',	'vendorQuotationRef',	'POTerms',	'address',	'Discontinued'], 
'account': [
    'tenantId', 'tenantName', 'shortName', 'accountNo', 'emailId','password','forecast','sales','account'
   ],
'menu-to-workArea-mapping': [
   'restaurantId',	'storeId',	'menuItemCode',	'menuItemName',	'floorNo',	'section',	'workArea'				]
}
}
