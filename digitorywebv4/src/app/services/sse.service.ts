import { Injectable } from '@angular/core';
import { Observable, Subject, throwError, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ChatMessage } from '../models/chat-message.model';
@Injectable({
  providedIn: 'root'
})
export class SseService {
  private baseUrl: string = environment.engineUrl;
  private messageSubject = new Subject<ChatMessage>();
  private dataUpdateSubject = new Subject<any>();
  private conversationHistoryCache: { [tenantId: string]: ChatMessage[] } = {};

  constructor(private http: HttpClient) { }

  private requestInProgress = false;
  private connectionTimeout: any = null;

  getResponse(tenantId: string, query: string): Observable<string> {
    if (this.requestInProgress) {
      return of('');
    }
    this.requestInProgress = true;

    const responseSubject = new Subject<string>();
    const encodedQuery = encodeURIComponent(query);
    const timestamp = new Date().getTime();
    const url = `${this.baseUrl}llm/ask?query=${encodedQuery}&tenant_id=${tenantId}&_=${timestamp}`;

    this.http.get<any>(url).subscribe({
      next: (response) => {
        // Handle restaurant data if present
        if (response.restaurant_data) {
          this.dataUpdateSubject.next({
            type: 'restaurant_data',
            data: response.restaurant_data
          });
        }

        // Handle the content
        if (response.content) {
          responseSubject.next(response.content);
        } else if (response.error) {
          console.error('Error from API:', response.error);
          const errorMessage = `I'm sorry, I encountered an error: ${response.error}. Please try again.`;
          responseSubject.next(errorMessage);
        } else {
          responseSubject.next("I'm sorry, I couldn't generate a response. Please try again.");
        }

        responseSubject.complete();
        this.requestInProgress = false;
      },
      error: (error) => {
        console.error('Error fetching response:', error);
        responseSubject.next("I'm sorry, I encountered an error connecting to the server. Please try again.");
        responseSubject.complete();
        this.requestInProgress = false;
      }
    });

    return responseSubject.asObservable();
  }
  disconnect(): void {
    this.requestInProgress = false;
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }
  sendMessage(tenantId: string, message: string): Observable<ChatMessage> {
    const now = new Date();
    const userMessage: ChatMessage = {
      id: this.generateId(),
      text: message,
      sender: 'user',
      timestamp: now
    };
    if (!message.startsWith('__') || !message.endsWith('__')) {
      this.messageSubject.next(userMessage);
    }
    const botMessageId = this.generateId();
    const botTimestamp = new Date(now.getTime() + 100);
    const initialBotMessage: ChatMessage = {
      id: botMessageId,
      text: 'DIGI is thinking...',
      sender: 'bot',
      timestamp: botTimestamp
    };
    if (message && message.trim() && (!message.startsWith('__') || !message.endsWith('__'))) {
      this.messageSubject.next(initialBotMessage);
    }

    this.getResponse(tenantId, message).subscribe({
      next: (response: string) => {
        if (!response.trim()) {
          const fallbackMessage: ChatMessage = {
            id: botMessageId,
            text: "I'm sorry, I couldn't generate a response. Please try again.",
            sender: 'bot',
            timestamp: botTimestamp
          };
          this.messageSubject.next(fallbackMessage);
        } else {
          const finalMessage: ChatMessage = {
            id: botMessageId,
            text: response,
            sender: 'bot',
            timestamp: botTimestamp
          };
          this.messageSubject.next(finalMessage);
        }

        // Send completion message
        const completionMessage: ChatMessage = {
          id: 'completion-' + botMessageId,
          text: '',
          sender: 'system',
          timestamp: new Date(botTimestamp.getTime() + 200)
        };
        this.messageSubject.next(completionMessage);
      },
      error: (error) => {
        console.error('Error getting response:', error);
        const errorMessage: ChatMessage = {
          id: botMessageId,
          text: 'Sorry, there was an error processing your request. Please try again.',
          sender: 'bot',
          timestamp: botTimestamp
        };
        this.messageSubject.next(errorMessage);

        const completionMessage: ChatMessage = {
          id: 'completion-' + botMessageId,
          text: '',
          sender: 'system',
          timestamp: new Date(botTimestamp.getTime() + 200)
        };
        this.messageSubject.next(completionMessage);
      }
    });

    return of(userMessage);
  }

  get messages$(): Observable<ChatMessage> {
    return this.messageSubject.asObservable();
  }
  fetchRestaurantData(tenantId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}llm/restaurant_data?tenant_id=${tenantId}`)
      .pipe(
        map(response => {
          if (response.status === 'ok' && response.data) {
            this.dataUpdateSubject.next({
              type: 'restaurant_data',
              data: response.data
            });
            return response.data;
          } else {
            console.warn('No restaurant data found:', response.message);
            this.dataUpdateSubject.next({
              type: 'restaurant_data',
              data: null
            });
            return null;
          }
        }),
        catchError(error => {
          console.error('Error fetching restaurant data:', error);
          return of(null);
        })
      );
  }

  get dataUpdates$(): Observable<any> {
    return this.dataUpdateSubject.asObservable();
  }
  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  getConversationHistory(tenantId: string): Observable<ChatMessage[]> {
    if (!tenantId) {
      console.error('getConversationHistory called with empty tenantId');
      return of([]);
    }
    const url = `${this.baseUrl}llm/conversation_history?tenant_id=${encodeURIComponent(tenantId)}`;
    return this.http.get<any>(url)
      .pipe(
        map(response => {
          if (response && response.messages) {
            const messages = response.messages.map((msg: any) => {
              return {
                id: msg.id ? msg.id.toString() : this.generateId(),
                text: msg.content,
                sender: msg.type === 'human' ? 'user' : 'bot',
                timestamp: new Date(msg.created_at)
              };
            });
            return messages;
          }
          return [];
        }),
        catchError(error => {
          console.error('Error fetching conversation history:', error);
          return of([]);
        })
      );
  }

  clearConversationHistory(tenantId: string, clearCache: boolean = true, clearServer: boolean = false): Observable<any> {
    if (!tenantId) {
      console.error('clearConversationHistory called with empty tenantId');
      return throwError(() => new Error('Invalid tenant ID'));
    }
    if (clearCache) {
      delete this.conversationHistoryCache[tenantId];
    }
    if (clearServer) {
      const url = `${this.baseUrl}llm/clear_history?tenant_id=${encodeURIComponent(tenantId)}`;
      return this.http.post<any>(url, {})
        .pipe(
          map(response => {
            return response;
          }),
          catchError(error => {
            console.error('Error clearing conversation history on server:', error);
            return throwError(() => new Error('Failed to clear conversation history on server'));
          })
        );
    }
    return of({ status: 'ok', message: 'Conversation history cleared in UI' });
  }

  loadConversationHistory(tenantId: string, addToStream: boolean = true): Observable<ChatMessage[]> {
    if (!tenantId) {
      console.error('loadConversationHistory called with empty tenantId');
      return of([]);
    }
    delete this.conversationHistoryCache[tenantId];
    return this.getConversationHistory(tenantId).pipe(
      map(messages => {
        this.conversationHistoryCache[tenantId] = messages;
        if (addToStream) {
          messages.forEach(message => {
            this.messageSubject.next(message);
          });
        }
        return messages;
      }),
      catchError(error => {
        console.error('Error loading conversation history:', error);
        return of([]);
      })
    );
  }
}
