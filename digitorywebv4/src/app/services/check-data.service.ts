import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CheckDataService {

  constructor() { }

  mandatory_columns = {
    'inventory master': [
      'category', 'subCategory', 'itemCode', 'itemName', 'itemType', 'vendor', 'Inventory UOM', 'closingUOM', 'procuredAt', 'issuedTo', 'taxRate', 'weight', 'yield', 'rate', 'finalRate', 'leadTime(days)', 'Discontinued'
    ],
    'packagingmasters': [
      'PackageName', 'TenantId', 'category', 'subCategory', 'InventoryCode', 'ItemName', 'Quantity per unit', 'Total qty of package', 'UnitUOM', 'Empty bottle weight', 'Full bottle weight', 'PackagePrice', 'Discontinued'
    ],
    'menu master': [
      'subCategory', 'category', 'menuItemCode', 'menuItemName', 'weight', 'rate', 'itemType', 'preparedAt', 'usedInWorkArea', 'usedAtOutlet', 'closingUOM', 'servingSize', 'Discontinued'
    ],
    'menu recipes': [
      'Category', 'Sub Category', 'menuItemCode', 'menuItemName', 'ingredientCode', 'ingredientName', 'ConsumptionUOM', 'InitialWeight', 'Yield', 'weightInUse', 'rate', 'finalRate', 'Discontinued'
    ],
    'Subrecipe Master': [
      'category', 'subCategory', 'menuItemCode', 'menuItemName', 'itemType', 'preparedAt', 'usedInWorkArea', 'usedAtOutlet', 'UOM', 'weightInUse', 'yield', 'rate', 'closingUOM', 'Discontinued'
    ],
    'Subrecipe Recipe': [
      'subRecipeCode', 'subRecipeName', 'ingredientCode', 'ingredientName', 'UOM', 'Initialweight', 'yield', 'weightInUse', 'rate', 'finalRate', 'Discontinued'
    ],
    'servingsize conversion': [
      'Serving Size', 'Ratio', 'Conversion Unit', 'Quantity'
    ],
    'users': [
      'tenantId', 'branchId', 'email', 'password', 'roles', 'mobile', 'firstName', 'lastName', 'Discontinued', 'multiBranchUser', 'workAreas'
    ],
    'branches': [
      'tenantId', 'tenantName', 'branchName', 'branchLocation', 'restaurantId', 'closingTimeFormat', 'restaurantClosingTime', 'storeId', 'abbreviated restaurantId', 'branchType', 'workArea'
    ],
    'Roles': ['role', 'module', 'Page', 'tenantId'],
    'vendors': [
      'vendorTenantId', 'vendorName', 'contactNo', 'contactName', 'email', 'Discontinued'
    ],
    'menu-to-workArea-mapping': [
      'restaurantId', 'storeId', 'menuItemCode', 'menuItemName', 'floorNo', 'section', 'workArea'
    ]
  }


  checkNumberValid(data) {
    if (typeof data === 'number') {
      return !isNaN(data);
    }
    return false;
  }

  checkZeroValid(data) {
    if (typeof data === 'number') {
        return !isNaN(data) && data > 0;
    }
    return false;
}


  checkStringValid(data) {
    const invalidStrings = ['NA', 'NaN', 'nan', '""', "''"];
    if (typeof data === 'string') {
      if (invalidStrings.includes(data)) {
        return false;
      }
      return true;
    }
    return false;
  }


  isValidString(value) {
    if (Array.isArray(value)) {
      const array = value.join(',');
      return !(array === null || array === undefined || array === '' || Array.isArray(array));
    }
    return !(value === null || value === undefined || value === '' || Array.isArray(value));
  }

  checkSheet(data, sheet) {
    const invalidEntries = [];
    if (sheet === 'inventory') {
      // data['Discontinued'] = 'nan'
      // data['closingUOM'] = ''
      const validations = [
        {
          key: 'Discontinued',
          condition: data['Discontinued'] && ['yes', 'no'].includes(data['Discontinued']),
          expectedValue: 'yes or no', // Explanation of the expected value
        },
        {
          key: 'Inventory UOM',
          condition: data['Inventory UOM'] && ['KG', 'LITRE', 'NOS', 'MTR'].includes(data['Inventory UOM']),
          expectedValue: 'KG, LITRE, NOS, or MTR', // Explanation
        },
        {
          key: 'closingUOM',
          condition: data['closingUOM'] && ['KG', 'LITRE', 'NOS', 'MTR', 'Open/KG'].includes(data['closingUOM']),
          expectedValue: 'KG, LITRE, NOS, MTR, or Open/KG',
        },
        {
          key: 'category',
          condition: this.checkStringValid(data['category']),
          expectedValue: 'A valid string without special characters (except &)',
        },
        {
          key: 'subCategory',
          condition: this.checkStringValid(data['subCategory']),
          expectedValue: 'A valid string without special characters (except &)',
        },
        {
          key: 'itemCode',
          condition: data['itemCode'] && !['NA', 'NaN', 'nan', ''].includes(data['itemCode']),
          expectedValue: 'A non-empty string that is not NA, NaN, or empty',
        },
        {
          key: 'itemName',
          condition: data['itemName'] && !['NA', 'NaN', 'nan', ''].includes(data['itemName']),
          expectedValue: 'A non-empty string that is not NA, NaN, or empty',
        },
        {
          key: 'taxRate',
          condition: this.checkNumberValid(data['taxRate']),
          expectedValue: 'A valid number greater than 0',
        },
        {
          key: 'weight',
          condition: this.checkNumberValid(data['weight']),
          expectedValue: 'A valid number greater than or equal to 0',
        },
        {
          key: 'yield',
          condition: this.checkNumberValid(data['yield']),
          expectedValue: 'A valid number greater than or equal to 0',
        },
        {
          key: 'rate',
          condition: this.checkNumberValid(data['rate']),
          expectedValue: 'A valid number greater than or equal to 0',
        },
        {
          key: 'finalRate',
          condition: this.checkNumberValid(data['finalRate']),
          expectedValue: 'A valid number greater than or equal to 0',
        },
        {
          key: 'leadTime(days)',
          condition: this.checkNumberValid(data['leadTime(days)']),
          expectedValue: 'A valid number greater than or equal to 0',
        },
        {
          key: 'vendor',
          condition: this.isValidString(data['vendor']),
          expectedValue: 'A valid string',
        },
        {
          key: 'procuredAt',
          condition: this.isValidString(data['procuredAt']),
          expectedValue: 'A valid string',
        },
        {
          key: 'issuedTo',
          condition: this.isValidString(data['issuedTo']),
          expectedValue: 'A valid string',
        },
      ];
      validations.forEach(({ key, condition, expectedValue }) => {
        if (!condition) {
          invalidEntries.push({
            key,
            value: data[key],
            expectedValue,
          });
        }
      });
    }else if(sheet === 'package'){
      const validations = [
        {
          key: 'InventoryCode',
          condition: data['InventoryCode'] && !['NA', 'NaN', 'nan', ''].includes(data['InventoryCode']),
          expectedValue: 'A non-empty string that is not NA, NaN, or empty',
          // if entry['InventoryCode'] in ['NA', 'NaN', 'nan', ''] or pandas.isnull(entry['InventoryCode']):
          // errorFile.write('packagingmasters|%d|InventoryCode|itemCode error\n' % (i+start))
        },
        {
          key: 'ItemName',
          condition: data['ItemName'] && !['NA', 'NaN', 'nan', ''].includes(data['ItemName']),
          expectedValue: 'A non-empty string that is not NA, NaN, or empty',
          // if entry['ItemName'] in ['NA', 'NaN', 'nan', ''] or pandas.isnull(entry['ItemName']):
          // errorFile.write('packagingmasters|%d|ItemName|packageName error\n' % (i+start))
        },
        {
          key: 'PackageName',
          condition: data['PackageName'] && !['NA', 'NaN', 'nan', ''].includes(data['PackageName']),
          expectedValue: 'A non-empty string that is not NA, NaN, or empty',
          // if entry['PackageName'] in ['NA', 'NaN', 'nan', ''] or pandas.isnull(entry['PackageName']):
          // errorFile.write('packagingmasters|%d|PackageName|packageName error\n' % (i+start))
        },
        {
          key: 'UnitUOM',
          condition: data['UnitUOM'] && ['KG', 'LITRE', 'NOS', 'MTR'].includes(data['UnitUOM']),
          expectedValue: 'KG, LITRE, NOS, or MTR', // Explanation
          // if entry['UnitUOM'] not in ['KG', 'LITRE', 'NOS', 'MTR'] :
          // errorFile.write('packagingmasters|%d|UnitUOM|UnitUOM shoud be either KG/LITRE/NOS/MTR \n' % (i+start))
        },
        {
          key: 'Discontinued',
          condition: data['Discontinued'] && ['yes', 'no'].includes(data['Discontinued']),
          expectedValue: 'yes or no', // Explanation of the expected value
          // if entry['Discontinued'] in ['y', 'Y', 'yes', 'Yes', 'YES']:
        },
        {
          key: 'Units/ package',
          condition: this.checkNumberValid(data['Units/ package']),
          expectedValue: 'A valid number greater than or equal to 0',
          // if datacheck(entry['Units/ package'], False) == False:
          // errorFile.write('packagingmasters|%d|Units/ package|Units/ package error,minimum price should be 1 Rs\n' % (i+start))
        },
        {
          key: 'Quantity per unit',
          condition: this.checkZeroValid(data['Quantity per unit']),
          expectedValue: 'A positive number greater than zero',
          // if datacheck(entry['Quantity per unit'], False) == False:
          // errorFile.write('packagingmasters|%d|Quantity per unit|Quantity per unit error,minimum price should be 1 Rs\n' % (i+start))
        },
        {
          key: 'Total qty of package',
          condition: this.checkZeroValid(data['Total qty of package']),
          expectedValue: 'A positive number greater than zero',
          // if datacheck(entry['Total qty of package'], False) == False:
          // errorFile.write('packagingmasters|%d|Total qty of package|Total qty of package error\n' % (i+start))
        },
        {
          key: 'PackagePrice',
          condition: this.checkNumberValid(data['PackagePrice']),
          expectedValue: 'A valid number greater than or equal to 0',
          // if datacheck(entry['PackagePrice'], False) == False:
          // errorFile.write('packagingmasters|%d|PackagePrice|PackagePrice error,minimum price should be 1 Rs\n' % (i+start))
        }
        // {
        //   key: 'category',
        //   condition: this.checkStringValid(data['category']),
        //   expectedValue: 'A valid string without special characters (except &)',
        //   // if entry['category'] == "" or pandas.isnull(entry['category']):
        //   // errorFile.write('packagingmasters|%d|category|mandatory-should be filled  \n' % (i+start))
        // },
        // {
        //   key: 'subCategory',
        //   condition: this.checkStringValid(data['subCategory']),
        //   expectedValue: 'A valid string without special characters (except &)',
        //   // if entry['subCategory'] == "" or pandas.isnull(entry['subCategory']):
        //   // errorFile.write('packagingmasters|%d|subCategory|mandatory-should be filled  \n' % (i+start))
        // },
       
      ];
      validations.forEach(({ key, condition, expectedValue }) => {
        if (!condition) {
          invalidEntries.push({
            key,
            value: data[key],
            expectedValue,
          });
        }
      });
    }
    return invalidEntries;
  }

}
