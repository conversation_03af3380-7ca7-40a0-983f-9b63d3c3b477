//use these two styles to create the picture box for profile etc
@use "./variables" as v;
.picture-box-global {
  width: 50px;
  height: 50px;
  position: relative;
  border-radius: v.$global-border-radius;
  overflow: hidden;
  img {
    inset: 0;
    object-fit: cover;
    object-position: center;
    position: absolute;
    width: 100%;
    height: 100%;
  }
  &.small {
    width: 50px;
    height: 50px;
  }
  @media screen and (min-width: 900px) {
    width: 80px;
    height: 80px;
    &.small {
      width: 50px;
      height: 50px;
    }
  }
}
