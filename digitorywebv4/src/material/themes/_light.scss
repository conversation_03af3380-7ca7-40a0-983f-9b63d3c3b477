@use "sass:map";
@use "@angular/material" as mat;

// Orange-based primary palette
$primary-palette:(
    50 : #fff5f0,
    100 : #ffe0cc,
    200 : #ffc999,
    300 : #ffb366,
    400 : #ff9d4d,
    500 : #ff8c42,
    600 : #e6732a,
    700 : #cc5a12,
    800 : #b34700,
    900 : #993d00,
    A100 : #ffe0cc,
    A200 : #ffb366,
    A400 : #ff8c42,
    A700 : #e6732a,
    contrast: (
        50 : #000000,
        100 : #000000,
        200 : #000000,
        300 : #000000,
        400 : #000000,
        500 : #ffffff,
        600 : #ffffff,
        700 : #ffffff,
        800 : #ffffff,
        900 : #ffffff,
        A100 : #000000,
        A200 : #000000,
        A400 : #ffffff,
        A700 : #ffffff,
    )
);;

// Sage green accent palette
$accent-palette:(
    50 : #f0f4ec,
    100 : #d9e5c7,
    200 : #c0d5a0,
    300 : #a4c085,
    400 : #94b575,
    500 : #87a96b,
    600 : #7a9960,
    700 : #6b8751,
    800 : #5c7542,
    900 : #4d6333,
    A100 : #d9e5c7,
    A200 : #a4c085,
    A400 : #87a96b,
    A700 : #6b8751,
    contrast: (
        50 : #000000,
        100 : #000000,
        200 : #000000,
        300 : #000000,
        400 : #000000,
        500 : #ffffff,
        600 : #ffffff,
        700 : #ffffff,
        800 : #ffffff,
        900 : #ffffff,
        A100 : #000000,
        A200 : #000000,
        A400 : #ffffff,
        A700 : #ffffff,
    )
);

$my-app-light-primary: mat.define-palette($primary-palette);
$my-app-light-accent: mat.define-palette($accent-palette, A200, A100, A400);

$my-app-light-warn: mat.define-palette(mat.$red-palette);

$my-app-light-theme: mat.define-light-theme(
  (
    color: (
      primary: $my-app-light-primary,
      accent: $my-app-light-accent,
      warn: $my-app-light-warn,
    ),
  )
);