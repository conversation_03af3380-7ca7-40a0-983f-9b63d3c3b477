"""
Database utility functions for chat history.
"""
import os
from typing import List, Optional
from sqlalchemy import text, create_engine
from dotenv import load_dotenv
from app.models.llm import ChatMessage

# Load environment variables
load_dotenv()
engine = create_engine(os.getenv("POSTGRES_URL"))

async def initialize_chat_tables():
    """
    Initialize the chat history tables if they don't exist.
    """
    try:
        # Try to find the SQL script in different locations
        script_paths = [
            "scripts/create_chat_history_table.sql",
            "digitoryjobsv4/scripts/create_chat_history_table.sql",
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "scripts/create_chat_history_table.sql")
        ]

        sql_script = None
        for path in script_paths:
            if os.path.exists(path):
                with open(path, "r") as f:
                    sql_script = f.read()
                break

        if sql_script is None:
            print(f"Warning: Could not find SQL script. Current directory: {os.getcwd()}")
            # Create the table directly if script not found
            sql_script = """
            -- Create chat_history table
            CREATE TABLE IF NOT EXISTS chat_history (
                id SERIAL PRIMARY KEY,
                tenant_id VARCHAR(255) NOT NULL,
                message_type VARCHAR(10) NOT NULL CHECK (message_type IN ('human', 'ai')),
                content TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );

            -- Create index for faster queries by tenant_id
            CREATE INDEX IF NOT EXISTS idx_tenant_id ON chat_history (tenant_id);
            """

        with engine.connect() as conn:
            conn.execute(text(sql_script))
            conn.commit()
            print("Chat history tables initialized successfully")
    except Exception as e:
        print(f"Error initializing chat history tables: {str(e)}")

async def save_message(tenant_id: str, content: str, message_type: str) -> int:
    """
    Save a message to the database.

    Args:
        tenant_id: The tenant ID
        content: The message content
        message_type: The message type ('human' or 'ai')

    Returns:
        The ID of the inserted message
    """
    query = text("""
        INSERT INTO chat_history (tenant_id, content, message_type)
        VALUES (:tenant_id, :content, :message_type)
        RETURNING id
    """)

    try:
        with engine.connect() as conn:
            result = conn.execute(
                query,
                {"tenant_id": tenant_id, "content": content, "message_type": message_type}
            )
            conn.commit()
            return result.scalar()
    except Exception as e:
        print(f"Error saving message to database: {str(e)}")
        return None

async def get_conversation_history(tenant_id: str) -> List[ChatMessage]:
    """
    Get the conversation history for a tenant.

    Args:
        tenant_id: The tenant ID

    Returns:
        A list of ChatMessage objects
    """
    query = text("""
        SELECT id, tenant_id, message_type, content, created_at
        FROM chat_history
        WHERE tenant_id = :tenant_id
        ORDER BY created_at ASC
    """)

    messages = []
    try:
        with engine.connect() as conn:
            result = conn.execute(query, {"tenant_id": tenant_id})

            for row in result:
                messages.append(ChatMessage(
                    id=row.id,
                    content=row.content,
                    type=row.message_type,
                    created_at=row.created_at
                ))

        return messages
    except Exception as e:
        print(f"Error retrieving conversation history: {str(e)}")
        return []

async def clear_conversation_history(tenant_id: str) -> bool:
    """
    Clear the conversation history for a tenant.

    Args:
        tenant_id: The tenant ID

    Returns:
        True if successful, False otherwise
    """
    query = text("""
        DELETE FROM chat_history
        WHERE tenant_id = :tenant_id
    """)

    try:
        with engine.connect() as conn:
            conn.execute(query, {"tenant_id": tenant_id})
            conn.commit()
            return True
    except Exception as e:
        print(f"Error clearing conversation history: {str(e)}")
        return False
