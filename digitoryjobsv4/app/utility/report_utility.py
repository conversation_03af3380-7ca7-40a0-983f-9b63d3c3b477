from app.database import grnsCol, branchesCol,indentlistsCol, Servingsizerecipes, Stockvalues,adjustinvCol,ibtsCol,tmpclosingsCol,menupriceCol,menuidmappingsCol,dailysalesdataCol,intrabranchtransfersCol,servingsizerecipesCol
from datetime import datetime,timedelta
import pandas as pd
import app.utility.conversion as conv
import numpy as np
from collections import Counter
import re
import math


def truncate_and_floor(number, precision):
    if np.isnan(number):
        return 0
    else:
        factor = 10 ** precision
        truncated_number = math.trunc(number * factor) / factor
        return math.floor(truncated_number * (10 ** precision)) / (10 ** precision)

def getLocation(tenantId):
    location ={}
    branches = branchesCol.find({'tenantId': tenantId},{'restaurantIdOld': 1, 'branchLocation' :1})
    for branch in branches:
        location[branch['restaurantIdOld']]=branch['branchLocation']
    return location

def getStockDict(restaurantId, stock_dict):
    if restaurantId not in stock_dict.keys():
        stock_dict[restaurantId] = []
        documents = Stockvalues.find({'restaurantId': restaurantId},{
            'itemCode': 1, 
            'category': 1, 
            'subCategory': 1,
            'taxRate':1, 
            'uom' :1,
            'packageName':1, 
            'packageQty' : 1, 
            'itemName' : 1, 
            'entryType' :1
        })
        for document in documents:
            entryType = document.get('entryType', 'N/A')
            packageName = document.get('packageName', 'N/A')
            item_code = document.get('itemCode')
            category = document['category']
            itemName = document.get('itemName', 'N/A')
            sub_category = document['subCategory']
            tax_rate = document.get('taxRate', 0)
            uom = document.get('uom', 'N/A')
            packageQty = document.get('packageQty', 1)
            if category and sub_category:
                stock_dict[restaurantId].append({
                    'itemCode' : item_code,
                    'category': category, 
                    'subCategory': sub_category,
                    'taxRate' :tax_rate,
                    'uom' : uom,
                    'packageQty' : packageQty,
                    'packageName' : packageName,
                    'entryType' : entryType,
                    'itemName': itemName, 
                })
    return stock_dict
def convert_datetime_to_date(dt_obj):
    if isinstance(dt_obj, datetime):
        return dt_obj.date()
    return None
def contains(input, filter):
    for x in input:
        if filter(x):
            return x
    return None
def split_camel_case(input_string):
    words = re.findall(r'[A-Z][a-z]*', input_string)
    return ' '.join(words)
def checkBottleWeight(data):
    if((data['emptyBottleWeight'] is None) or (data['emptyBottleWeight'] == 0) or (data['fullBottleWeight'] is None) or (data['fullBottleWeight'] == 0)):
        if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
            if data['otherPackages'] is None:
                data['otherPackages'] =0
            return data['otherPackages'] * 1000
        else:
            return 0
    else:
        return truncate_and_floor((float(data['cnvtdOtherPackages'] or 0)) , 2)

def getGrnDict(job, startTime, endTime, qtyWise=False):

    if 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'invoiceDate':
        baseDate = 'invoiceDate'
    elif 'selectedBaseDate' in job['details'] and job['details']['selectedBaseDate'] == 'grnDate':
        baseDate = 'grnDocumentDate'
    else: 
        baseDate = 'createTs'

    grnFilter = {
        'restaurantId': { '$in': job['details']['selectedRestaurants']},
        'grnType': 'po',
        baseDate: {'$gte': startTime, '$lte': endTime}
    }
    if len(job['details']['selectedVendors']) > 0 :
        grnFilter['vendorId'] = {'$in' : job['details']['selectedVendors']}

    grnList = list(grnsCol.find(grnFilter))
    purchaseDict = {}
    for grn in grnList:
        restaurantId = grn['restaurantId']
        if restaurantId not in purchaseDict:
            purchaseDict[restaurantId] = {}
        for item in grn['grnItems']:
            itemCode = item['itemCode']
            if qtyWise:
                for package in item['packages']:
                    searchKey = itemCode + '|' + 'package'.upper() +  '|' + package['packageName'].upper()
                    orderedQty = item['quantity'] if item['quantity'] is not None else 0
                    receivedQty = item['receivedQty'] if item['receivedQty'] is not None else 0
                    taxAmount = item['taxAmount'] if item['taxAmount'] is not None else 0
                    subTotal = item['subTotal'] if item['subTotal'] is not None else 0
                    totalPrice = item['totalPrice'] if item['totalPrice'] is not None else 0
                    if searchKey in purchaseDict[restaurantId]:
                        purchaseDict[restaurantId][searchKey]['ordered'] += float(orderedQty or 0)
                        purchaseDict[restaurantId][searchKey]['received'] += float(receivedQty or 0)
                        purchaseDict[restaurantId][searchKey]['taxAmount'] += float(taxAmount or 0)
                        purchaseDict[restaurantId][searchKey]['total(excl.tax)'] += float(subTotal or 0)
                        purchaseDict[restaurantId][searchKey]['total(incl.tax)'] += float(totalPrice or 0)
                    else:
                        purchaseDict[restaurantId][searchKey] = { 
                            "ordered" : 0, 
                            "received" : 0,
                            "taxAmount": 0,
                            "total(excl.tax)": 0,
                            "total(incl.tax)": 0
                        }
                        purchaseDict[restaurantId][searchKey]['ordered'] = float(orderedQty or 0)
                        purchaseDict[restaurantId][searchKey]['received'] = float(receivedQty or 0)
                        purchaseDict[restaurantId][searchKey]['taxAmount'] = float(taxAmount or 0)
                        purchaseDict[restaurantId][searchKey]['total(excl.tax)'] = float(subTotal or 0)
                        purchaseDict[restaurantId][searchKey]['total(incl.tax)'] = float(totalPrice or 0)
            else:
                searchKey = itemCode + '|' + item['uom'].upper()
                for package in item['packages']:
                    packageQty = package.get('packageQty', 1)
                    receivedQty = item['receivedQty'] if item['receivedQty'] is not None else 0
                    if searchKey in purchaseDict[restaurantId]:
                        purchaseDict[restaurantId][searchKey] += float((receivedQty * packageQty) or 0)
                    else:
                        purchaseDict[restaurantId][searchKey] = float((receivedQty * packageQty) or 0)
    return purchaseDict

def getSubRecipeValues(recipe, invDict, tenantId, restaurantId, quantity=0, modifierReceipes=[],autobarDict={},autobarFlag= False, subrecipeDict={}):
    if recipe is not None:
        for ingredient in recipe['Ingredients']:
            itemCode = ingredient['IngredientCode']
            if itemCode == recipe['menuItemCode']:
                continue
            searchKey = ""
            initWeight = 0
            if ingredient['ingredientUom'].lower() == 'gm':
                initWeight = conv.toKilo(ingredient['initialWeight'])
                searchKey = itemCode + '|' + "KG"
                if autobarFlag:
                    autobarSearchKey = recipe['menuItemCode'] + '|' + "KG"
            elif ingredient['ingredientUom'].lower() == 'ml':
                initWeight = conv.toLitre(ingredient['initialWeight'])
                searchKey = itemCode + '|' + "LITRE"
                if autobarFlag:
                    autobarSearchKey = recipe['menuItemCode'] + '|' + "LITRE"
            else:
                initWeight = ingredient['initialWeight']
                searchKey = itemCode + '|' + ingredient['ingredientUom']
                if autobarFlag:
                    autobarSearchKey = recipe['menuItemCode'] + '|' + ingredient['ingredientUom']
            isSubRecipe =servingsizerecipesCol.find_one({'tenantId': tenantId, 'menuItemCode': itemCode})
            if isSubRecipe:
                if subrecipeDict :
                    if searchKey in subrecipeDict[restaurantId]:
                        subrecipeDict[restaurantId][searchKey] += (quantity * initWeight)
                    else:
                        subrecipeDict[restaurantId][searchKey] = (quantity * initWeight)

                getSubRecipeValues(isSubRecipe, invDict, tenantId, restaurantId, quantity, modifierReceipes, autobarDict, autobarFlag, subrecipeDict)
            else:
                modifierCounter = Counter(modifierReceipes)
                if(("isModifier" in ingredient.keys()) and (ingredient['isModifier'].upper().strip() =="Y") and (ingredient['modifierName'] not in modifierCounter.keys())):
                    continue
                elif (("isModifier" in ingredient.keys()) and (ingredient['isModifier'].upper().strip() =="Y") and (ingredient['modifierName'] in modifierCounter.keys()) ):
                    if searchKey in invDict[restaurantId]:
                        invDict[restaurantId][searchKey] += (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                    else:
                        invDict[restaurantId][searchKey] = (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                    if autobarFlag:
                        if autobarSearchKey in autobarDict[restaurantId]:
                            autobarDict[restaurantId][autobarSearchKey] += (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                        else:
                            autobarDict[restaurantId][autobarSearchKey] = (float(modifierCounter[ingredient['modifierName']]) * initWeight)
                else:
                    if searchKey in invDict[restaurantId]:
                        invDict[restaurantId][searchKey] += (quantity * initWeight)
                    else:
                        invDict[restaurantId][searchKey] = (quantity * initWeight)
                    if autobarFlag:
                        if autobarSearchKey in autobarDict[restaurantId]:
                            autobarDict[restaurantId][autobarSearchKey] += (quantity * initWeight)
                        else:
                            autobarDict[restaurantId][autobarSearchKey] = (quantity * initWeight)


def getIndentDict(job, startDate, endDate, qtyWise=False, waWise=False):
    stockDict ={}
    queries = []
    if 'selectedBaseDateForIndent' in job['details'] and job['details']['selectedBaseDateForIndent'] == 'documentDate':
        queries.append({
            'restaurantId': {'$in': job['details']['selectedRestaurants']},
            'indentDocumentDate' : {'$gte': startDate, '$lte': endDate}
        })
    elif 'selectedBaseDateForIndent' in job['details'] and job['details']['selectedBaseDateForIndent'] == 'modDate':
        queries.append({
            'restaurantId': {'$in': job['details']['selectedRestaurants']},
            'modTs' : {'$gte': startDate, '$lte': endDate}
        })
    else:
        queries.append({
            'restaurantId': {'$in': job['details']['selectedRestaurants']},
            'createTs' : {'$gte': startDate, '$lte': endDate}
        })
    indentsDict = {}
    subrecipeDict ={}
    for query in queries :
        if len(job['details']['selectedWorkAreas']) > 0 :
            query['workArea'] = {'$in' : job['details']['selectedWorkAreas']}
        indentsList = list(indentlistsCol.find(query))
        for indent in indentsList:
            restaurantId = indent['restaurantId']
            workArea = indent['workArea']
            getStockDict(restaurantId, stockDict)
            if restaurantId not in indentsDict:
                indentsDict[restaurantId] = {}
            if restaurantId not in subrecipeDict:
                subrecipeDict[restaurantId] = {}
            for item in indent['indentItems']:
                itemCode = item['itemCode']
                item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
                item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
                item['issueQty'] = item.get('issueQty', 0) if item.get('issueQty') is not None else 0
                item['pendingQty'] = item.get('pendingQty', 0) if item.get('pendingQty') is not None else 0
                item['dispatchedQty'] = item['issueQty'] - item['pendingQty']
                unitPrice = float(item['price'] if 'price' in item.keys() else item['packages'][0]['packagePrice'])
                if qtyWise:
                    searchKey = itemCode + '|' + item['entryType'].upper() + '|' + item['packageName'].upper()
                    if waWise:
                        if searchKey not in indentsDict[restaurantId]:
                            indentsDict[restaurantId][searchKey] = {}
                        if workArea not in indentsDict[restaurantId][searchKey]:
                            indentsDict[restaurantId][searchKey][workArea] = {}
                        work_area_data = indentsDict[restaurantId][searchKey].setdefault(workArea, {
                            'requested': 0,
                            'issued': 0,
                            'unitPrice': 0
                        })

                        work_area_data['requested'] = work_area_data.get('requested', 0) + item['issueQty']
                        work_area_data['issued'] = work_area_data.get('issued', 0) + item['dispatchedQty']
                        work_area_data['unitPrice'] = work_area_data.get('unitPrice', 0) + (item['dispatchedQty'] * unitPrice)
                    else:
                        if searchKey in indentsDict[restaurantId]:
                            indentsDict[restaurantId][searchKey]['requested'] += item['issueQty']
                            indentsDict[restaurantId][searchKey]['issued'] += item['dispatchedQty']
                            indentsDict[restaurantId][searchKey]['unitPrice'] += (item['dispatchedQty'] * unitPrice)
                        else:
                            indentsDict[restaurantId][searchKey] = { 
                                "requested" : item['issueQty'], 
                                "issued"    : item['dispatchedQty'], 
                                'unitPrice' : (item['dispatchedQty'] * unitPrice)
                            }
                else:
                    packageQty = item.get('packageQty', item.get('packages', [{'packageQty': 1}])[0]['packageQty'])
                    sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
                    if sv:
                        uom = sv['uom']
                        searchKey = itemCode + '|' + uom.upper()
                    else:
                        print("CHECK")
                        continue
                    isSubRecipe = servingsizerecipesCol.find_one({'tenantId': job['tenantId'], 'menuItemCode': itemCode})
                    if isSubRecipe:
                        if searchKey in subrecipeDict[restaurantId]:
                            subrecipeDict[restaurantId][searchKey] += float((item['dispatchedQty'] * packageQty) or 0)
                        else:
                            subrecipeDict[restaurantId][searchKey] = float((item['dispatchedQty'] * packageQty) or 0)
                        getSubRecipeValues(isSubRecipe, indentsDict, job['tenantId'], restaurantId, item['dispatchedQty'])
                    else:
                        if searchKey in indentsDict[restaurantId]:
                            indentsDict[restaurantId][searchKey] += float((item['dispatchedQty'] * packageQty) or 0)
                        else:
                            indentsDict[restaurantId][searchKey] = float((item['dispatchedQty'] * packageQty) or 0)
    return [indentsDict, subrecipeDict]

def getIbtDict(job, startDate, endDate, qtyWise=False):
    stockDict ={}
    ibtSearchFilter = {
        'tenantId': job['tenantId'],
        'receivedDate': {'$exists' : True},
        'receivedDate' :{"$gte":startDate,"$lte":endDate},
    }
    ibtData = list(ibtsCol.find(ibtSearchFilter))
    ibtInDict = {}
    subrecipeDict ={}
    for ibt in ibtData:
        restaurantId = ibt['toBranch']['restaurantId']
        getStockDict(restaurantId, stockDict)
        if restaurantId not in ibtInDict:
            ibtInDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for item in ibt['items']:
            itemCode = item['itemCode']
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            receivedQty = (item['quantity'] - item['recPendingQty']) - sum(item['shortageHistory'])
            if qtyWise:
                searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
                if searchKey in ibtInDict[restaurantId]:
                    ibtInDict[restaurantId][searchKey] += receivedQty
                else:
                    ibtInDict[restaurantId][searchKey] = receivedQty
            else:
                packageQty = item.get('packageQty', 1)
                sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
                if sv:
                    uom = sv['uom']
                else:
                    print("CHECK")
                    continue
                searchKey = itemCode + '|' + uom.upper()                
                isSubRecipe = Servingsizerecipes.find_one({'tenantId': job['tenantId'], 'menuItemCode': itemCode})
                if isSubRecipe:
                    if searchKey in subrecipeDict[restaurantId]:
                        subrecipeDict[restaurantId][searchKey] += float((receivedQty * packageQty) or 0)
                    else:
                        subrecipeDict[restaurantId][searchKey] = float((receivedQty * packageQty) or 0)
                    getSubRecipeValues(isSubRecipe, ibtInDict, job['tenantId'], restaurantId, receivedQty)
                else:
                    if searchKey in ibtInDict[restaurantId]:
                        ibtInDict[restaurantId][searchKey] += float((receivedQty * packageQty) or 0)
                    else:
                        ibtInDict[restaurantId][searchKey] = float((receivedQty * packageQty) or 0)
    ibtSearchFilter = {
        'tenantId': job['tenantId'],
        'dispatchedDate': {'$exists' : True},
        'dispatchedDate' :{"$gte":startDate,"$lte":endDate},
    }
    ibtData = list(ibtsCol.find(ibtSearchFilter))
    ibtOutDict = {}
    subrecipeDict ={}
    for ibt in ibtData:
        restaurantId = ibt['fromBranch']['restaurantId']
        getStockDict(restaurantId, stockDict)
        if restaurantId not in ibtOutDict:
            ibtOutDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for item in ibt['items']:
            itemCode = item['itemCode']
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            dispatchedQty = item['quantity'] - item['pendingQty']
            if qtyWise:
                searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
                if searchKey in ibtOutDict[restaurantId]:
                    ibtOutDict[restaurantId][searchKey] += dispatchedQty
                else:
                    ibtOutDict[restaurantId][searchKey] = dispatchedQty
            else:
                packageQty = item.get('packageQty', 1)
                sv = next((x for x in stockDict[restaurantId] if x['itemCode'] == itemCode and x['packageName'] == item['packageName']), None)
                if sv:
                    uom = sv['uom']
                else:
                    print("CHECK")
                    continue
                searchKey = itemCode + '|' + uom.upper()
                isSubRecipe = Servingsizerecipes.find_one({'tenantId': job['tenantId'], 'menuItemCode': itemCode})
                if isSubRecipe:
                    if searchKey in subrecipeDict[restaurantId]:
                        subrecipeDict[restaurantId][searchKey] += float((dispatchedQty * packageQty) or 0)
                    else:
                        subrecipeDict[restaurantId][searchKey] = float((dispatchedQty * packageQty) or 0)
                    getSubRecipeValues(isSubRecipe, ibtOutDict, job['tenantId'], restaurantId, dispatchedQty)
                else:
                    if searchKey in ibtOutDict[restaurantId]:
                        ibtOutDict[restaurantId][searchKey] += float((dispatchedQty * packageQty) or 0)
                    else:
                        ibtOutDict[restaurantId][searchKey] = float((dispatchedQty * packageQty) or 0)
    return [ibtInDict, ibtOutDict]

def getSpoilageDictStore(job, startTime, endTime, qtyWise=False):
    adjInvFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'createTs': {'$gte': startTime, '$lte': endTime}
    }
    adjInvData = list(adjustinvCol.find(adjInvFilter))

    adjInvDict = {}
    for adjInv in adjInvData:
        restaurantId = adjInv['restaurantId']
        if restaurantId not in adjInvDict:
            adjInvDict[restaurantId] = {}
        for item in adjInv['adjustInvItems']:
            itemCode = item['itemCode']
            item['packageName'] = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            item['entryType'] = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            searchKey = itemCode + '|' + item['entryType'].upper() +  '|' + item['packageName'].upper()
            if item['adjustQty'] is None:
                item['adjustQty'] = 0
            elif item['adjustType'] == "Dec":
                item['adjustQty'] = - float(item['adjustQty'])
            if searchKey in adjInvDict[restaurantId]:
                adjInvDict[restaurantId][searchKey] += item['adjustQty'] 
            else:
                adjInvDict[restaurantId][searchKey] = item['adjustQty'] 
    return adjInvDict

def getUserDictStore(job, event, requestedCategory=0, qtyWise=False):
    if event == "closing":
        if "endDate" in job['details'] and job['details']['endDate']:
               startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
               endTime = datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
        else:
            startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
            endTime = datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
      
    if event == "opening":
        startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
        endTime = datetime.strptime(job['details']['startDate'],'%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
      
    userFilter = {
        'type' : 'storeClosing',
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime},
        'active': False
    }
    userList = list(tmpclosingsCol.find(userFilter))
    userList.reverse()
    userDict = {}
    for entry in userList:
        restaurantId = entry['restaurantId']
        if restaurantId not in userDict:
            userDict[restaurantId] = {}
        for workArea in entry['workAreas']:
            if workArea not in userDict[restaurantId]:
                userDict[restaurantId][workArea] = {}
            for category in entry['workAreas'][workArea]['category']:
                for subCategory in entry['workAreas'][workArea]['category'][category]['subCategory']:
                    if "items" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]:
                        for itemCode in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items']:
                            if "packagingSizes" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]:
                                for data in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]['packagingSizes']:
                                    searchKey = itemCode + '|' + data['uom'].upper() +'|'+ data['pkgName'].upper()
                                    if data['uom'].lower() == 'kg':
                                        weight = 1000
                                    elif data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                        weight = 1000
                                    elif data['uom'].lower() == 'nos':
                                        weight = 1
                                    else:
                                        continue
                                    if data['otherPackages'] is None:
                                        data['otherPackages'] = 0
                                    if searchKey not in userDict[restaurantId][workArea].keys():
                                        userDict[restaurantId][workArea][searchKey] = 0
                                        if requestedCategory == 0:
                                            userDict[restaurantId][workArea][searchKey] += truncate_and_floor(((data['orderedPackages'] * data['pkgQty'])* weight), 2)
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor(checkBottleWeight(data), 2)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor(((data['otherPackages'] * data['pkgQty'])* weight), 2)
                                        elif requestedCategory == 2:
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' :truncate_and_floor((checkBottleWeight(data)/1000), 2),
                                                    "package" : data['orderedPackages']
                                                }
                                            else:
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' : truncate_and_floor((data['otherPackages'] * data['pkgQty']), 2),
                                                    "package" : data['orderedPackages']
                                                } 
                                        else:
                                            userDict[restaurantId][workArea][searchKey] += truncate_and_floor((data['orderedPackages'] * data['pkgQty']), 2)
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor((checkBottleWeight(data)/1000), 2)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor((data['otherPackages'] * data['pkgQty']), 2)
                            else:
                                data = entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]
                                if data['closingStock'] is None:
                                    data['closingStock'] = 0
                                searchKey = itemCode + '|' + 'N/A' +'|'+ 'N/A'
                                if searchKey not in userDict[restaurantId][workArea].keys():
                                    userDict[restaurantId][workArea][searchKey] = 0
                                    if requestedCategory == 0:
                                        userDict[restaurantId][workArea][searchKey] += truncate_and_floor((data['closingStock'] * 1000), 2)
                                    elif requestedCategory == 2:
                                        userDict[restaurantId][workArea][searchKey] ={"open" : truncate_and_floor(data['closingStock'] , 2)} 
                                    else:
                                        userDict[restaurantId][workArea][searchKey] += truncate_and_floor(data['closingStock'] , 2)
    res={}
    if qtyWise:
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                for updatedSearchKey in userDict[restId][wa].keys():
                    if updatedSearchKey not in res[restId].keys():
                        res[restId][updatedSearchKey] = userDict[restId][wa][searchKey]
                    else:
                        res[restId][updatedSearchKey] += userDict[restId][wa][searchKey]
    elif requestedCategory == 2:
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                if wa not in res[restId].keys():
                    res[restId][wa] ={}
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    if itemCode not in res[restId].keys():
                        res[restId][wa][itemCode] = userDict[restId][wa][searchKey]['open']
                    else:
                        res[restId][wa][itemCode] += userDict[restId][wa][searchKey]['open']
        return [res, userDict]
    else:
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    updatedSearchKey = itemCode+"|" + uom.upper()
                    if updatedSearchKey not in res[restId].keys():
                        res[restId][updatedSearchKey] = userDict[restId][wa][searchKey]
                    else:
                        res[restId][updatedSearchKey] += userDict[restId][wa][searchKey]
    return res


def getProductionCost(job, month, year):
    query = {
        'tenantId': job['tenantId'],
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'month': month,
        'year' : year
    }
    menus = list(menupriceCol.find(query))
    menu_production_cost = {}
    for menu in menus:
        if menu['restaurantId'] not in menu_production_cost.keys():
            menu_production_cost[menu['restaurantId']] ={}
        search_key =  menu['menuItemCode'] + '|' + menu['servingSize']
        menu_production_cost[menu['restaurantId']][search_key] = {
            "modifier" : {mod["modifierName"]: (mod.get("withTaxPrice", 0)) for mod in menu['splitUp']['modifier'].values()},
            "non_modifier" : sum((ing.get("withTaxPrice", 0)) for ing in menu['splitUp']['nonModfier'].values())
        }
    return menu_production_cost

def getSellingPrice(job, month, year):
    menus = list(menuidmappingsCol.find({'tenantId': job['tenantId']}))
    menu_selling_price = {}
    key_search = year + '_'+ month
    for menu in menus:
        if 'price' not in menu.keys():
            continue
        for rId , value in menu['price'].items():
            if rId not in menu_selling_price.keys():
                menu_selling_price[rId] ={}
            if key_search in value.keys():
                search_key =  menu['itemCode'] + '|' + menu['servingSize']
                if value[key_search]['weighted_average'] <=0:
                    value[key_search]['weighted_average'] = 0.1
                menu_selling_price[rId][search_key] = {
                    "non_modifier" : value[key_search]['weighted_average'],
                    "modifier" : value[key_search]['overall_modifier_unit_costs']
                }
    return menu_selling_price

def getDailySales(job, startTime, endTime):
    salesSearchFilter = {
        'tenantId': job['tenantId'],
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime}
    }
    salesItems = list(dailysalesdataCol.find(salesSearchFilter))
    modified_sales_data = []
    for sales_item in salesItems:
        restaurant_id = sales_item.get('restaurantId', '')
        sales_by_work_area = sales_item.get('salesByWorkArea', {})
        for work_area, menu_items in sales_by_work_area.items():
            for menu_item, details in menu_items.items():
                quantity = details.get('quantity', 0)
                
                modifiers = details.get('modifierReceipes', [])
                if quantity != len(modifiers):
                    diff = quantity - len(modifiers)
                    if diff > 0:
                        modifiers.extend(['NAN'] * diff)

                modifier_quantity_dict = {}
                for modifier in modifiers:
                    modifier_quantity_dict[modifier] = 0
                for modifier in modifiers:
                    modifier_quantity_dict[modifier] += 1
                for modifier, modifier_quantity in modifier_quantity_dict.items():
                    modified_sales_data.append({
                        'Restaurant ID': restaurant_id,
                        'Work Area': work_area,
                        'Menu Item': menu_item,
                        'Quantity': modifier_quantity,
                        'Modifier': modifier
                    })
    df = pd.DataFrame(modified_sales_data)
    if len(df) > 0:
        df_grouped = df.groupby(['Restaurant ID', 'Work Area', 'Menu Item', 'Modifier'], as_index=False)['Quantity'].sum()
        return df_grouped
    else:
        return df

def get_menu_value(dictionary, restaurant_id, search_key, modifier_key=None):
    if restaurant_id in dictionary and search_key in dictionary[restaurant_id]:
        if modifier_key and modifier_key in dictionary[restaurant_id][search_key]['modifier']:
            return dictionary[restaurant_id][search_key]['non_modifier'], dictionary[restaurant_id][search_key]['modifier'][modifier_key]
        else:
            return dictionary[restaurant_id][search_key]['non_modifier'], 0
    else:
        return 0.1, 0

def get_pr_approval_status(element):
    if element.get('approvalDetail') and len(element['approvalDetail']) > 0:
        data = element['approvalDetail']
        if len(data) != 0:
            level_order = [item['level'] for item in data]
            status_with_role = []
            for current_level in level_order:
                matching_data = next((item for item in data if item['level'] == current_level), None)
                if matching_data:
                    level = matching_data['level']
                    status = matching_data['status']
                    role = matching_data['role']
                    
                    if status == "rejected":
                        status_with_role = [status.capitalize(), role]
                        break
                    elif status == "pending" and "rejected" not in status_with_role:
                        status_with_role = [status.capitalize(), role]
                    elif status == "approved" and "rejected" not in status_with_role and "pending" not in status_with_role:
                        status_with_role = [status.capitalize(), role]
            
            return status_with_role
    else:
        return ['Auto Approved', 'Auto'] 

def format_time_to_ampm(time_str):
    time_obj = datetime.strptime(time_str, "%H:%M:%S")
    return time_obj.strftime("%I:%M %p")

def getUserDictWorkArea(job, event, requestedCategory=0):
    if event == "closing":
        if "endDate" in job['details'] and job['details']['endDate']:
            startTime = datetime.strptime(job['details']['endDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
            endTime = datetime.strptime(job['details']['endDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
        else:
            startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0)
            endTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59)
    if event == "opening":
        startTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=0, minute=0, second=0) - timedelta(days=1)
        endTime = datetime.strptime(job['details']['startDate'], '%Y-%m-%dT%H:%M:%S.%f%z').replace(hour=23, minute=59, second=59) - timedelta(days=1)
    userFilter = {
        'type' : 'kitchenClosing',
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime},
        'active': False
    }
    orQueryForWorkAreas=[]
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['workAreas.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        userFilter['$or'] = orQueryForWorkAreas

    userList = list(tmpclosingsCol.find(userFilter))
    
    userList.reverse()
    userDict = {}
    for entry in userList:
        restaurantId = entry['restaurantId']
        if restaurantId not in userDict:
            userDict[restaurantId] = {}
        for workArea in entry['workAreas']:
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            if workArea not in userDict[restaurantId]:
                userDict[restaurantId][workArea] = {}
            for category in entry['workAreas'][workArea]['category']:
                for subCategory in entry['workAreas'][workArea]['category'][category]['subCategory']:
                    if "items" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]:
                        for itemCode in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items']:
                            if "packagingSizes" in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]:
                                for data in entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]['packagingSizes']:
                                    searchKey = itemCode + '|' + data['uom'].upper() +'|'+ data['pkgName'].upper()
                                    if data['uom'].lower() == 'kg':
                                        weight = 1000
                                    elif data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                        weight = 1000
                                    elif data['uom'].lower() == 'nos':
                                        weight = 1
                                    else:
                                        continue
                                    if data['otherPackages'] is None:
                                        data['otherPackages'] = 0
                                    if searchKey not in userDict[restaurantId][workArea].keys():
                                        userDict[restaurantId][workArea][searchKey] = 0
                                        if requestedCategory == 0:
                                            userDict[restaurantId][workArea][searchKey] += truncate_and_floor(((data['orderedPackages'] * data['pkgQty'])* weight), 2)
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor(checkBottleWeight(data), 2)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor(((data['otherPackages'] * data['pkgQty'])* weight), 2)
                                        elif requestedCategory == 2:
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' : truncate_and_floor((checkBottleWeight(data)/1000), 2),
                                                    "package" : data['orderedPackages']
                                                }
                                            else:
                                                userDict[restaurantId][workArea][searchKey] = {
                                                    'open' : truncate_and_floor(((data['otherPackages'] * data['pkgQty'])), 2),
                                                    "package" : data['orderedPackages']
                                                }
                                        else:
                                            userDict[restaurantId][workArea][searchKey] += truncate_and_floor((data['orderedPackages'] * data['pkgQty']), 2)
                                            if data['uom'].lower() == 'litre' or data['uom'].lower() == 'ltr':
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor((checkBottleWeight(data)/1000), 2)
                                            else:
                                                userDict[restaurantId][workArea][searchKey] += truncate_and_floor((data['otherPackages'] * data['pkgQty']), 2)
                            else:
                                data = entry['workAreas'][workArea]['category'][category]['subCategory'][subCategory]['items'][itemCode]
                                searchKey = itemCode + '|' + 'N/A' +'|'+ 'N/A'
                                if searchKey not in userDict[restaurantId][workArea].keys():
                                    userDict[restaurantId][workArea][searchKey] = 0
                                    if data['closingStock'] is None:
                                        data['closingStock'] = 0
                                    if requestedCategory == 0:
                                        userDict[restaurantId][workArea][searchKey] += truncate_and_floor((data['closingStock'] * 1000), 2)
                                    elif requestedCategory == 2:
                                        userDict[restaurantId][workArea][searchKey] = {
                                            "open" : truncate_and_floor(data['closingStock'] , 2) 
                                        }
                                    else:
                                        userDict[restaurantId][workArea][searchKey] += truncate_and_floor(data['closingStock'] , 2)

    if requestedCategory ==2:
        res={}
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                if wa not in res[restId].keys():
                    res[restId][wa] ={}
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    if itemCode not in res[restId].keys():
                        res[restId][wa][itemCode] = userDict[restId][wa][searchKey]['open']
                    else:
                        res[restId][wa][itemCode] += userDict[restId][wa][searchKey]['open']
        return res, userDict
    else:
        res={}
        for restId in userDict.keys():
            res[restId] ={}
            for wa in userDict[restId]:
                for searchKey in userDict[restId][wa].keys():
                    itemCode, uom , pkgName = searchKey.split("|")
                    updatedSearchKey = itemCode+"|" + uom.upper()
                    if updatedSearchKey not in res[restId].keys():
                        res[restId][updatedSearchKey] = userDict[restId][wa][searchKey]
                    else:
                        res[restId][updatedSearchKey] += userDict[restId][wa][searchKey]
        return res

def getSpoilageDictWorkArea(job, startTime, endTime):
    adjInvFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'workArea': {'$in': job['details']['selectedWorkAreas']},
        'createTs': {'$gte': startTime, '$lte': endTime}
    }
    adjInvData = list(adjustinvCol.find(adjInvFilter))
    adjInvDict = {}
    itemSvDict={}
    for adjInv in adjInvData:
        restaurantId = adjInv['restaurantId']
        if restaurantId not in adjInvDict:
            adjInvDict[restaurantId] = {}
        for item in adjInv['adjustInvItems']:
            itemCode = item['itemCode']
            entryType = item.get('entryType') if item.get('entryType') is not None else 'N/A'
            packageName = item.get('packageName') if item.get('packageName') is not None else 'N/A'
            caseFlag = 0
            if packageName !='N/A' and entryType !='N/A':
                key = itemCode + "|" + packageName
                caseFlag = 1
            elif entryType == "open":
                key = itemCode + "|" + entryType
                caseFlag = 2
            else:
                key = itemCode
                caseFlag = 3

            if key in itemSvDict:
                packageQty = itemSvDict[key]['packageQty']
                uom = itemSvDict[key]['uom']
            else:
                svEntry = None
                if caseFlag == 1:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': packageName},{"packageQty" : 1, 'uom' :1})
                elif caseFlag == 2:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'},{"packageQty" : 1, 'uom' :1})
                else:
                    svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode},{"packageQty" : 1, 'uom' :1})
                if svEntry:
                    packageQty = svEntry.get('packageQty', 1)
                    uom = svEntry.get('uom', 'N/A')
                itemSvDict[key] ={'packageQty' : packageQty, 'uom' :uom} 

            searchKey = itemCode + '|' + uom.upper() 
            if item['adjustQty'] is None:
                item['adjustQty'] = 0
            else:
                if item['adjustType'] == "Inc":
                    item['adjustQty'] = item['adjustQty']
                else:
                    item['adjustQty'] = -(item['adjustQty'])
            if searchKey in adjInvDict[restaurantId]:
                adjInvDict[restaurantId][searchKey] += float((item['adjustQty'] * packageQty) or 0)
            else:
                adjInvDict[restaurantId][searchKey] = float((item['adjustQty'] * packageQty) or 0)
    return adjInvDict

def getTheoreticalConsumption(job, startTime, endTime, autobar=False):
    salesSearchFilter = {
        'tenantId': job['tenantId'],
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime}
    }
    orQueryForWorkAreas=[]
    job['details']['selectedWorkAreas'].append("DEFAULT")
    for area in job['details']['selectedWorkAreas']:
        temp={}
        temp['salesByWorkArea.{}'.format(area.strip())] = {'$exists': True }
        orQueryForWorkAreas.append(temp)
    if len(orQueryForWorkAreas) > 0:
        salesSearchFilter['$or'] = orQueryForWorkAreas

    salesItems = list(dailysalesdataCol.find(salesSearchFilter))
    invDict = {}
    subrecipeDict = {}
    autobarDict = {}
    for entry in salesItems:
        restaurantId = entry['restaurantId']
        if restaurantId not in invDict.keys():
            invDict[restaurantId] = {}
        if restaurantId not in subrecipeDict.keys():
            subrecipeDict[restaurantId] = {}
        if restaurantId not in autobarDict.keys():
            autobarDict[restaurantId] = {}
        for workArea in entry['salesByWorkArea']:
            if workArea not in job['details']['selectedWorkAreas']:
                continue
            for itemKey, value in entry['salesByWorkArea'][workArea].items():
                menuItemCode, servingSize = itemKey.split("|")
                menuItemCode = menuItemCode.strip()
                servingSize = servingSize.strip()
                menu =servingsizerecipesCol.find_one({
                    'tenantId': job['tenantId'],
                    'menuItemCode': menuItemCode,
                    "isSubRecipe" : False,
                    'servingSize': servingSize
                })
                if menu is None:
                    continue
                getSubRecipeValues(menu, invDict, job['tenantId'], restaurantId, value['quantity'], value['modifierReceipes'],autobarDict, autobar, subrecipeDict)
    if autobar:
        return [invDict, autobarDict]
    else:
        return [invDict, subrecipeDict]

def getIntraBranchTransferDict(job, startTime, endTime):
    ibtSearchFilter = {
        'restaurantId': {'$in': job['details']['selectedRestaurants']},
        'date': {'$gte': startTime, '$lte': endTime}
    }

    ibtData = list(intrabranchtransfersCol.find(ibtSearchFilter))
    ibtInDict = {}
    itemSvDict={}
    subrecipeDict ={}
    for ibt in ibtData:
        restaurantId = ibt['restaurantId']
        tenantId = ibt['tenantId']
        workArea = ibt['destinationWorkArea']
        if workArea not in job['details']['selectedWorkAreas']:
            continue
        if restaurantId not in ibtInDict:
            ibtInDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for itemKey,quantity in ibt['items'].items():
            itemCode, packageName, entryType  = itemKey.split('|')
            isSubRecipe = servingsizerecipesCol.find_one({'tenantId': tenantId, 'menuItemCode': itemCode})
            if isSubRecipe:
                getSubRecipeValues(isSubRecipe, ibtInDict, tenantId, restaurantId, quantity)
            else:
                if entryType == "open":
                    key = itemCode + "|" + entryType
                else:
                    key = itemCode + "|" + packageName
                if key in itemSvDict:
                    packageQty = itemSvDict[key]['packageQty']
                    uom = itemSvDict[key]['uom']
                else:
                    svEntry = None
                    if packageName != "N/A" and entryType == "package":
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': packageName},{"packageQty" : 1, 'uom' :1})
                    else:
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'},{"packageQty" : 1, 'uom' :1})
                    if svEntry:
                        packageQty = svEntry.get('packageQty', 1)
                        uom = svEntry.get('uom', 'N/A')
                    itemSvDict[key] = {'packageQty' : packageQty , 'uom': uom }


                searchKey = itemCode + '|' + uom.upper()
                if searchKey in ibtInDict[restaurantId]:
                    ibtInDict[restaurantId][searchKey] += float((quantity * packageQty) or 0)
                else:
                    ibtInDict[restaurantId][searchKey] = float((quantity * packageQty) or 0)

    ibtOutDict = {}
    subrecipeDict ={}
    for ibt in ibtData:
        restaurantId = ibt['restaurantId']
        tenantId = ibt['tenantId']
        workArea = ibt['sourceWorkArea']
        if workArea not in job['details']['selectedWorkAreas']:
            continue
        if restaurantId not in ibtOutDict:
            ibtOutDict[restaurantId] = {}
        if restaurantId not in subrecipeDict:
            subrecipeDict[restaurantId] = {}
        for itemKey,quantity in ibt['items'].items():
            itemCode,packageName, entryType  = itemKey.split('|')
            isSubRecipe =servingsizerecipesCol.find_one({'tenantId': tenantId, 'menuItemCode': itemCode})
            if isSubRecipe:
                getSubRecipeValues(isSubRecipe, ibtOutDict, tenantId, restaurantId, quantity)
            else:
                if entryType == "open":
                    key = itemCode + "|" + entryType
                else:
                    key = itemCode + "|" + packageName
                if key in itemSvDict:
                    packageQty = itemSvDict[key]['packageQty']
                    uom = itemSvDict[key]['uom']
                else:
                    svEntry = None
                    if packageName != "N/A" and entryType == "package":
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'packageName': packageName},{"packageQty" : 1, 'uom' :1})
                    else:
                        svEntry = Stockvalues.find_one({'restaurantId': restaurantId, 'itemCode': itemCode, 'entryType':'open'},{"packageQty" : 1, 'uom' :1})
                    if svEntry:
                        packageQty = svEntry.get('packageQty', 1)
                        uom = svEntry.get('uom', 'N/A')
                    itemSvDict[key] = {'packageQty' : packageQty , 'uom': uom }

                searchKey = itemCode + '|' + uom.upper()
                if searchKey in ibtOutDict[restaurantId]:
                    ibtOutDict[restaurantId][searchKey] += float((quantity * packageQty) or 0)
                else:
                    ibtOutDict[restaurantId][searchKey] = float((quantity * packageQty) or 0)
    return [ibtInDict, ibtOutDict]

def consolidatedSalesData(salesData):
    salesDict = {}
    for entry in salesData:
        for saleData in entry['items']:
            itemCode = saleData['itemCode']
            itemName = saleData['itemName']
            servingSize = saleData['servingSize']
            quantity = saleData['quantity']
            restaurantId = entry['restaurantId']
            tenantId = entry['tenantId']
            tup = (itemCode, servingSize)
            if tup in salesDict:
                salesDict[tup]['actual'] += quantity
            else:
                restEntry = Stockvalues.find_one({"restaurantId": restaurantId, "itemCode": itemCode})
                if restEntry :
                    category = restEntry['category']
                    subCategory = restEntry['subCategory']
                else:
                    category = "N/A"
                    subCategory = "N/A"
                salesDict[tup] = {
                    'itemCode': itemCode + "|" + servingSize,
                    'actual': quantity,
                    'category': category,
                    'subCategory': subCategory,
                    'itemName': itemName
                }
    return salesDict
    
def calculateMetricsData(metricsData):
    consolidatedMetricsData = {}
    for entry in metricsData:
        for predictData in entry['items']:
            itemCode = predictData
            quantity = entry['items'][predictData]['predicted']
            tup = (itemCode)
            if tup in consolidatedMetricsData:
                consolidatedMetricsData[tup]['predicted'] += quantity
            else:
                consolidatedMetricsData[tup] = {
                    'itemCode': itemCode,
                    'predicted': quantity,
                }
    return consolidatedMetricsData
