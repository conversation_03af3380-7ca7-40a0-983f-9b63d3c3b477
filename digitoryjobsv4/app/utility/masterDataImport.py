from __future__ import print_function
import pickle
import os.path
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from datetime import datetime
import pandas as pd
import os
from collections import Counter
from app.database import roloposconfigsCol

import warnings
warnings.filterwarnings('ignore')
baseUrl = "http://142.93.216.12:3000"
def masterDataImport(client, SHEET_NAME,tenantId, columnCheck = False, readFull=True, baseUpdate=False, sessionId='0'):
	mdBaseDirectory = '../digitoryjobsv4/masterdata/base/'+client+'/'
	mdSessionDirectory = '../digitoryjobsv4/masterdata/session/'+client+'/'
	if not os.path.exists(mdBaseDirectory):
		os.makedirs(mdBaseDirectory)
	if not os.path.exists(mdSessionDirectory):
		os.makedirs(mdSessionDirectory)
	creds = None
	SCOPES = ['https://www.googleapis.com/auth/spreadsheets.readonly']
	roloPosData = roloposconfigsCol.find_one({"tenantId": tenantId})
	SPREADSHEET_ID = roloPosData.get('gSheet')

	if os.path.exists('token.pickle'):
		with open('token.pickle', 'rb') as token:
			creds = pickle.load(token)
	if not creds or not creds.valid:
		if creds and creds.expired and creds.refresh_token:
			creds.refresh(Request())
		else:
			flow = InstalledAppFlow.from_client_secrets_file('app/g_sheet_credentials.json', SCOPES)
			creds = flow.run_local_server(port=0)
		with open('token.pickle', 'wb') as token:
			pickle.dump(creds, token)

	service =None
	while service is None:
		try:
			service = build('sheets', 'v4', credentials=creds)
		except:
			import time
			time.sleep(10)
			service =None
			
	if columnCheck:
		missingSheets= []
		spreadsheet_metadata = service.spreadsheets().get(spreadsheetId=SPREADSHEET_ID).execute()
		sheet_names = [sheet['properties']['title'] for sheet in spreadsheet_metadata['sheets']]
		mandatory_sheet_names = [
			'inventory master',
			'packagingmasters',
			'menu master',
			'menu recipes',
			'Subrecipe Master',
			'Subrecipe Recipe',
			'servingsize conversion',
			'users',
			'branches',
			'Roles',
			'vendors',
			'menu-to-workArea-mapping'
		]
		for sheet_name in mandatory_sheet_names:
			if sheet_name not in sheet_names:
				missingSheets.append(sheet_name)
		return missingSheets
	else:
		basePath = mdBaseDirectory +SHEET_NAME +'.csv'
		keys_to_sum = ['y', 'Y', 'yes', 'Yes', 'YES']
		counter_data = Counter()
		df = pd.DataFrame()   
		isDataFound = False

		if int(sessionId):
			sessionPath = mdSessionDirectory + sessionId +'/'+SHEET_NAME +'.csv'
			if os.path.exists(sessionPath):
				df = pd.read_csv(sessionPath, dtype=str)
				if not df.empty:
					isDataFound = True
					print("PROCESSING FROM SESSION..", SHEET_NAME)
			else:
				if os.path.exists(basePath):
					df = pd.read_csv(basePath, dtype=str)
					if not df.empty:
						isDataFound = True
						print("PROCESSING FROM BASE..", SHEET_NAME)

		if not isDataFound:
			isDataFound = True 
			result = None
			while result is None:
				try:
					result = service.spreadsheets().values().get(spreadsheetId=SPREADSHEET_ID,range=SHEET_NAME).execute()
				except:
					import time
					time.sleep(10)
					result =None
			
			if not result.get('values', []): 
				return df
			else:
				df = pd.DataFrame(result.get('values')[1:], columns = result.get('values')[0])
			print("PROCESSING FROM GSHEET..", SHEET_NAME)
			
		# Defining the characters to remove

		if 'Discontinued' not in df.columns:
			df['Discontinued'] = "No"

		characters_to_remove = r'\r\n\t\\=#\?\[\]\*!\'\’\"'
		pattern = '[' + characters_to_remove + ']'
		df = df.replace(to_replace=pattern, value='', regex=True)
		df.fillna('', inplace=True)
		
		if baseUpdate and not df.empty:
			if 'modified' in df.columns:
				has_yes_value = any(df['modified'] == 'yes')
				if ((not has_yes_value) and int(sessionId) ):
					return
				df = df.drop(columns=['modified']) 
			df['modified'] = '-'
			df['row_uuid'] = df.reset_index().index + 1
			df.to_csv(basePath, encoding='utf-8', index=False)
			return
	
		if not baseUpdate:
			df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)
			df.dropna(how='all')
			if SHEET_NAME == "Subrecipe Master":
				df['portion'] = pd.to_numeric(df.get('portion', 1), errors='coerce')
			if SHEET_NAME == "menu recipes":
				df['portionCount'] = pd.to_numeric(df.get('portionCount', 0), errors='coerce')

			if SHEET_NAME == "packagingmasters":
				columns_to_check = ['Empty bottle weight', 'Full bottle weight', 'Total qty of package', 'Quantity per unit']
				for column in columns_to_check:
					df[column] = pd.to_numeric(df.get(column, 0), errors='coerce')
				df.fillna(0, inplace=True)

			if readFull:
				return df
			else:
				if 'modified' in df.columns: 
					counter_data = Counter(df['modified'].tolist())
				counter = 0
				eligibleFlag = False
				values_to_filter =[]
				for key in keys_to_sum:
					if key in counter_data:
						if key not in values_to_filter:
							values_to_filter.append(key)
						counter += counter_data[key]
						eligibleFlag = True
				if eligibleFlag and counter <= 100:
					filtered_df = df[df['modified'].isin(values_to_filter)]
					return filtered_df
				else:
					return df