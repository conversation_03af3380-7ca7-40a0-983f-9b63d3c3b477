def toKilo(value):
	try:
		res = round((float(value) / 1000.0), 6)
	except:
		res = None
	return res

def toGram(value):
	try:
		res = round((float(value) * 1000.0), 6)
	except:
		res = None
	return res

def toMl(value):
	try:
		res = round((float(value) * 1000.0), 6)
	except:
		res = None
	return res

def toLitre(value):
	try:
		res = round((float(value) / 1000.0), 6)
	except:
		res = None
	return res