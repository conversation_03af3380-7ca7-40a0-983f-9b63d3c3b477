from sqlalchemy import text
import os
import json
import requests
import concurrent.futures
from typing import List, Dict, Any
from pymongo import MongoClient
from dotenv import load_dotenv
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
import asyncio
from app.utility.restaurant_db import get_restaurant_data
os.makedirs('output', exist_ok=True)


load_dotenv()
BASE_URL = os.getenv("POS_API_BASE_URL")
client = MongoClient(os.getenv("DATABASE_URL"))
db = client[os.getenv("MONGO_INITDB_DATABASE")]
roloposconfig = db[os.getenv("CONFIG_COLLECTION_NAME")]
USE_MENU_BY_ID = False

# CREATE EXTENSION IF NOT EXISTS pg_trgm;
# CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
# CREATE INDEX IF NOT EXISTS idx_inventory_item_name_lower ON inventory_master (LOWER(item_name));
# CREATE INDEX IF NOT EXISTS idx_packaging_item_name_lower ON packaging_master (LOWER(item_name));
# CREATE INDEX IF NOT EXISTS idx_inventory_item_name_trgm ON inventory_master USING gin (LOWER(item_name) gin_trgm_ops);
# CREATE INDEX IF NOT EXISTS idx_inventory_item_name_metaphone ON inventory_master (metaphone(item_name, 10));

SEARCH_INGREDIENT_FUNCTION = text("""
CREATE OR REPLACE FUNCTION search_ingredient(ingredient_term TEXT)
RETURNS JSON AS $$
DECLARE
    result_json JSON;
BEGIN
    WITH search_params AS (
        SELECT
            LOWER(ingredient_term) AS search_term_lower,
            metaphone(ingredient_term, 10) AS search_metaphone,
            soundex(ingredient_term) AS search_soundex
    ),
    -- Primary exact matches
    primary_matches AS (
        SELECT
            im.*,
            1 AS match_level,
            CASE
                WHEN LOWER(im.item_name) = sp.search_term_lower THEN 1
                WHEN LOWER(im.item_name) = sp.search_term_lower || 's' THEN 2
                WHEN LOWER(im.item_name) || 's' = sp.search_term_lower THEN 2
                WHEN LOWER(im.item_name) LIKE sp.search_term_lower || ' %' THEN 3
                WHEN sp.search_term_lower LIKE LOWER(im.item_name) || ' %' THEN 3
                ELSE 4
            END AS match_priority,
            COALESCE(array_length(im.source, 1), 0) AS client_size_score,
            1.0 AS similarity_score
        FROM
            inventory_master im,
            search_params sp
        WHERE
            (LOWER(im.item_name) = sp.search_term_lower
            OR LOWER(im.item_name) = sp.search_term_lower || 's'
            OR LOWER(im.item_name) || 's' = sp.search_term_lower
            OR LOWER(im.item_name) LIKE sp.search_term_lower || ' %'
            OR sp.search_term_lower LIKE LOWER(im.item_name) || ' %')
            AND COALESCE(array_length(im.source, 1), 0) >= 3
    ),
    -- Phonetic and soundex matches
    phonetic_matches AS (
        SELECT
            im.*,
            2 AS match_level,
            ROW_NUMBER() OVER (ORDER BY similarity(LOWER(im.item_name), sp.search_term_lower) DESC) AS match_priority,
            COALESCE(array_length(im.source, 1), 0) AS client_size_score,
            similarity(LOWER(im.item_name), sp.search_term_lower) AS similarity_score
        FROM
            inventory_master im,
            search_params sp
        WHERE
            (metaphone(im.item_name, 10) = sp.search_metaphone
            OR soundex(im.item_name) = sp.search_soundex
            OR similarity(LOWER(im.item_name), sp.search_term_lower) > 0.4)
            AND COALESCE(array_length(im.source, 1), 0) >= 3
    ),
    -- Word-based fallback matches
    word_matches AS (
        SELECT
            im.*,
            3 AS match_level,
            ROW_NUMBER() OVER (ORDER BY word_similarity(LOWER(im.item_name), sp.search_term_lower) DESC) AS match_priority,
            COALESCE(array_length(im.source, 1), 0) AS client_size_score,
            word_similarity(LOWER(im.item_name), sp.search_term_lower) AS similarity_score
        FROM
            inventory_master im,
            search_params sp
        WHERE
            word_similarity(LOWER(im.item_name), sp.search_term_lower) > 0.6
            AND COALESCE(array_length(im.source, 1), 0) >= 3
        LIMIT 10
    ),
    -- Combine all matches
    combined_matches AS (
        SELECT * FROM primary_matches
        UNION ALL
        SELECT * FROM phonetic_matches
        UNION ALL
        SELECT * FROM word_matches
    ),
    matched_inventory AS (
        SELECT *
        FROM combined_matches
        ORDER BY
            match_level,
            similarity_score DESC,
            client_size_score DESC,
            match_priority,
            length(item_name) ASC
        LIMIT 1
    )
    -- Build JSON result with proper handling for no matches
    SELECT json_build_object(
        'inventory_match', (
            SELECT row_to_json(mi)
            FROM matched_inventory mi
        ),
        'packaging_matches', (
            CASE
                WHEN EXISTS (SELECT 1 FROM matched_inventory) THEN
                    (SELECT json_agg(row_to_json(pm))
                    FROM (
                        SELECT
                            pm.*,
                            1.0 AS item_similarity
                        FROM packaging_master pm
                        JOIN matched_inventory mi ON
                            LOWER(pm.item_name) = LOWER(mi.item_name)
                        WHERE
                            COALESCE(array_length(pm.source, 1), 0) >= 3
                        ORDER BY
                            pm.id
                        LIMIT 3
                    ) pm)
                ELSE NULL
            END
        ),
        'search_term', ingredient_term,
        'search_method_used', (
            SELECT
                CASE
                    WHEN EXISTS (SELECT 1 FROM primary_matches) THEN 'exact_match'
                    WHEN EXISTS (SELECT 1 FROM phonetic_matches) THEN 'phonetic_match'
                    WHEN EXISTS (SELECT 1 FROM word_matches) THEN 'word_similarity_match'
                    ELSE 'no_match'
                END
        )
    ) INTO result_json;

    RETURN result_json;
END;
$$ LANGUAGE plpgsql;
""")

class POSflow():
    """
    Workflow for fetching recipes from POS, predicting ingredients,
    matching with lookup_ingredient, and saving results to CSV.
    """

    def __init__(self, tenant_id, inventory_file, package_file, inventory_data, package_data, vendor_file=None, vendor_data=None):
        super().__init__()
        self.tenant_id = tenant_id
        self.inventory_file = inventory_file
        self.package_file = package_file
        self.vendor_file = vendor_file
        self.inventory_data = inventory_data
        self.package_data = package_data
        self.vendor_data = vendor_data or {}
        self.restaurant_data = None
        self.outlet_abbreviation_map = {}
        self.all_workareas = []
        self.outlet_states = set()

    def _fetch_recipes_from_api(self, counter = False) -> List[Dict[str, Any]]:
        """Fetch all recipe names and details from the POS API."""
        tenant = list(roloposconfig.find({'tenantId': self.tenant_id}))
        if not tenant:
            return []
        email, password = tenant[0].get('emailId'), tenant[0].get('password')        
        token_resp = requests.post(
            f"{BASE_URL}/login",
            headers={"Content-Type": "application/json", "App-Id": "inventory"},
            data=json.dumps({"emailID": email, "password": password})
        )

        if token_resp.status_code != 200:
            return []

        employee_data = token_resp.json().get('loggedInEmployee', {})
        if not employee_data:
            return []

        access_token = employee_data['token']
        account_id = employee_data['accountID']
        accounts = employee_data.get('accounts', [])
        all_menu_items = []
        
        if account_id == 0:
            with concurrent.futures.ThreadPoolExecutor() as executor:
                futures = [
                    executor.submit(
                        self._fetch_menu_items, 
                        access_token, 
                        account['id']
                    ) for account in accounts
                ]
                
                for future in concurrent.futures.as_completed(futures):
                    all_menu_items.extend(future.result())
        else:
            all_menu_items = self._fetch_menu_items(access_token, account_id)


        if counter:
            return len(all_menu_items)
        elif not USE_MENU_BY_ID:
            t = [ item['name'] for item in all_menu_items ] 
            return t
        else:
            detailed_menu_items = self._fetch_menu_details(access_token, account_id, all_menu_items)
            return detailed_menu_items
            

    def _fetch_menu_items(self, token: str, account_id: int) -> List[Dict[str, Any]]:
        """Fetch menu items for a specific account."""
        response = requests.get(
            f"{BASE_URL}/account/{account_id}/menuItems",
            headers={"Authorization": f"Bearer {token}"}
        )

        if response.status_code == 200:
            print("TOTAL MENU ITEMS .. ",len(response.json()))
            return response.json()
        return []

    def _fetch_menu_details(self, token: str, account_id: int, menu_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Fetch detailed information for each menu item."""
        detailed_items = []
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = {
                executor.submit(self._get_menu_item_by_id, token, account_id, item['id']): item['id']
                for item in menu_items
            }
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                if result:
                    detailed_items.append(
                        f"Name: {result['name']}, Cuisine: {result['cuisines'][0]['name'] if result['cuisines'] else 'Not available'}"
                    )
        return detailed_items

    def _get_menu_item_by_id(self, token: str, account_id: int, menu_id: int) -> Dict[str, Any]:
        """Fetch details of a specific menu item by ID."""
        response = requests.get(
            f"{BASE_URL}/account/{account_id}/menuItem/{menu_id}",
            headers={"Authorization": f"Bearer {token}"}
        )

        if response.status_code == 200:
            return response.json()
        return {}

    async def _load_restaurant_data(self):
        """Load restaurant data for the tenant and prepare outlet abbreviation mapping and workareas list."""
        if self.restaurant_data is None:
            self.restaurant_data = await get_restaurant_data(self.tenant_id)

            if self.restaurant_data and 'outletDetails' in self.restaurant_data:
                for outlet in self.restaurant_data['outletDetails']:
                    if 'outletAbbreviation' in outlet and outlet['outletAbbreviation']:
                        self.outlet_abbreviation_map[outlet['outletAbbreviation']] = outlet['outletAbbreviation']

                    if 'outletWorkAreas' in outlet and outlet['outletWorkAreas']:
                        for workarea in outlet['outletWorkAreas']:
                            if workarea and workarea not in self.all_workareas:
                                self.all_workareas.append(workarea.upper())

                    if 'outletAddress' in outlet and outlet['outletAddress']:
                        address = outlet['outletAddress']
                        from scripts.address_utils import extract_state_from_address
                        state = extract_state_from_address(address)
                        if state:
                            self.outlet_states.add(state)
                            print(f"Found state: {state} from address: {address}")

                if not self.outlet_states:
                    print(f"No states could be extracted from outlet addresses for tenant {self.tenant_id}")

    def map_procured_at(self, outlet_abbreviation):
        """Map outlet abbreviation to ProcuredAt field."""
        if not outlet_abbreviation:
            return ""

        if outlet_abbreviation in self.outlet_abbreviation_map:
            return self.outlet_abbreviation_map[outlet_abbreviation]

        return outlet_abbreviation

    def get_workareas_for_prompt(self):
        """Get all workareas as a formatted string for LLM prompts."""
        return "Available workareas in the restaurant: " + ", ".join(self.all_workareas)

    def _get_distinct_inventory_categories_from_df(self) -> list:
        """Get distinct categories from the inventory dataframe."""
        inventory_df = pd.DataFrame(self.inventory_data)
        categories = inventory_df['category'].dropna().unique().tolist()
        return categories

    def _populate_vendor_data(self) -> None:
        """Populate vendor data for all categories in a single database query."""

        query = text("""
        WITH vendor_base AS (
            SELECT
                v.trade_name,
                v.gstin,
                v.state,
                v.address,
                v.source,
                array_length(v.source, 1) AS source_count,
                v.top_categories
            FROM
                vendor_master v
            WHERE
                UPPER(v.state) IN (SELECT UPPER(s) FROM unnest(:states) AS s)
                AND array_length(v.source, 1) >= 1
        ),
        vendor_all_categories AS (
            SELECT
                vb.trade_name,
                vb.gstin,
                vb.state,
                vb.address,
                vb.source,
                vb.source_count,
                k.key AS category,
                (vb.top_categories->k.key)::int AS category_count
            FROM
                vendor_base vb,
                LATERAL jsonb_object_keys(vb.top_categories) AS k(key)
        ),
        vendor_filtered_categories AS (
            SELECT *
            FROM vendor_all_categories
            WHERE category IN (SELECT unnest(:categories))
        ),
        vendor_categories_ranked AS (
            SELECT
                *,
                ROW_NUMBER() OVER (
                    PARTITION BY trade_name
                    ORDER BY category_count DESC
                ) AS category_rank
            FROM vendor_filtered_categories
        ),
        top_vendor_categories AS (
            SELECT *
            FROM vendor_categories_ranked
            WHERE category_rank <= 2
        ),
        ranked_vendors AS (
            SELECT
                trade_name,
                gstin,
                state,
                address,
                source,
                category,
                category_count,
                source_count,
                ROW_NUMBER() OVER (
                    PARTITION BY category
                    ORDER BY source_count DESC, category_count DESC
                ) AS rank
            FROM
                top_vendor_categories
        )
        SELECT
            trade_name,
            gstin,
            state,
            address,
            category,
            category_count,
            source_count,
            source
        FROM
            ranked_vendors
        WHERE
            rank <= :vendor_limit
        ORDER BY
            category, rank
        """)


        from sqlalchemy import create_engine
        from dotenv import load_dotenv
        import os

        load_dotenv()
        engine = create_engine(os.getenv("POSTGRES_URL"))

        categories = [str(cat) for cat in self._get_distinct_inventory_categories_from_df()]
        states = [str(state) for state in self.outlet_states]
        params = {
            "categories": categories,
            "states": states,
            "vendor_limit": int(os.getenv("VENDOR_LIMIT", "10"))
        }

        with engine.connect() as conn:
            result = conn.execute(query, params)
            rows = result.fetchall()
            for row in rows:
                category = row[4]
                if category not in self.vendor_data:
                    self.vendor_data[category] = []

                self.vendor_data[category].append({
                    'trade_name': row[0],
                    'gstin': row[1],
                    'state': row[2],
                    'address': row[3],
                    'category': category,
                    'category_count': row[5],
                    'source_count': row[6],
                    'source': row[7] if len(row) > 7 else []
                })


    def _save_results_to_excel(self) -> None:
        """Save the matched inventory, packaging, and vendor data to Excel files."""
        asyncio.run(self._load_restaurant_data())

        # --- Inventory Data Handling ---
        if self.inventory_data:
            inventory_df = pd.DataFrame(self.inventory_data)
            inventory_df.drop_duplicates(subset=['item_name'], keep='last', inplace=True)
            inventory_master_mapping = {
                'ledger': 'Ledger',
                'source': 'Source',
                'category': 'Category',
                'sub_category': 'SubCategory',
                'classification': 'Classification',
                'item_name': 'ItemName',
                'inventory_uom': 'Inventory UOM',
                'closing_uom': 'ClosingUOM',
                'tax_rate': 'TaxRate',
                'weight': 'Weight',
                'yield': 'Yield',
                'lead_time_days': 'LeadTime (days)',
                'issuedTo': 'IssuedTo'
            }

            inventory_df.rename(columns=inventory_master_mapping, inplace=True)

            if not self.vendor_data:
                self._populate_vendor_data()

            category_to_vendors = {}
            for category, vendors in self.vendor_data.items():
                if vendors:
                    sorted_vendors = sorted(vendors,
                                          key=lambda x: (x.get('source_count', 0), x.get('category_count', 0)),
                                          reverse=True)
                    vendor_names = [v['trade_name'] for v in sorted_vendors]
                    category_to_vendors[category.upper()] = vendor_names

            def get_vendors_for_category(category):
                if not category or not isinstance(category, str):
                    return ''
                vendors = category_to_vendors.get(category.upper(), [])
                return ','.join(vendors) if vendors else ''

            inventory_df['Vendor'] = inventory_df['Category'].apply(get_vendors_for_category)

            all_abbreviations = ','.join(self.outlet_abbreviation_map.keys()) if self.outlet_abbreviation_map else ''
            inventory_df['ProcuredAt'] = all_abbreviations
            inventory_df['ItemType'] = 'Inventory'
            inventory_df['Rate'] = 1
            inventory_df['FinalRate'] = 1
            inventory_df['Recovery'] = inventory_df['Weight'] * inventory_df['Yield']
            inventory_df['Discontinued'] = 'NO'
            inventory_df['ItemCode'] = 'D' + (inventory_df.index + 1000).astype(str)
            inventory_column_order = [
                'Source','Ledger', 'Category', 'SubCategory', 'ItemType','ItemCode',  'ItemName', 'Classification',
                'Inventory UOM', 'ClosingUOM', 'LeadTime (days)', 'TaxRate', 'Weight', 'Yield', 'Recovery',  'Rate', 'FinalRate',
                'Vendor', 'IssuedTo', 'ProcuredAt', 'Discontinued'
            ]
            inventory_df = inventory_df[inventory_column_order]

            item_name_to_code = inventory_df.set_index('ItemName')['ItemCode'].to_dict()
            item_name_to_category = inventory_df.set_index('ItemName')['Category'].to_dict()
            item_name_to_sub_category = inventory_df.set_index('ItemName')['SubCategory'].to_dict()
            inventory_df.to_excel(self.inventory_file, index=False, sheet_name='Inventory')

            workbook = load_workbook(self.inventory_file)
            sheet = workbook['Inventory']
            rate_col = inventory_df.columns.get_loc('Rate') + 1
            recovery_col = inventory_df.columns.get_loc('Recovery') + 1
            final_rate_col = inventory_df.columns.get_loc('FinalRate') + 1

            for row in range(2, len(inventory_df) + 2):
                sheet[f'{get_column_letter(final_rate_col)}{row}'] = (
                    f'=IFERROR({get_column_letter(rate_col)}{row}/{get_column_letter(recovery_col)}{row}, 0)'
                )

            workbook.save(self.inventory_file)

        # --- Packaging Data Handling ---
        if self.package_data:
            package_df = pd.DataFrame(self.package_data)
            package_df.drop_duplicates(subset=['item_name', 'quantity_per_unit'], keep='last', inplace=True)
            package_master_mapping = {
                'item_name': 'ItemName',
                'package_name': 'PackageName',
                'brand': 'brand',
                'unit_per_package': 'Units/ package',
                'quantity_per_unit': 'Quantity per Unit',
                'unit_uom': 'UnitUOM',
                'empty_bottle_weight': 'Empty Bottle Weight',
                'full_bottle_weight': 'Full Bottle Weight'
            }

            package_df.rename(columns=package_master_mapping, inplace=True)
            package_df['InventoryCode'] = package_df['ItemName'].map(item_name_to_code).fillna('')
            package_df['Category'] = package_df['ItemName'].map(item_name_to_category).fillna('')
            package_df['SubCategory'] = package_df['ItemName'].map(item_name_to_sub_category).fillna('')
            package_df['Discontinued'] = 'NO'
            package_df['parLevel'] = 0
            package_df['PackagePrice'] = 1
            package_df['Total Qty of Package'] = 0

            package_column_order = [
                 'Category', 'SubCategory', 'InventoryCode', 'ItemName', 'PackageName', 'brand', 'UnitUOM',
                'Units/ package', 'Quantity per Unit', 'Total Qty of Package',
                'Empty Bottle Weight', 'Full Bottle Weight','PackagePrice' ,'parLevel', 'Discontinued'
            ]
            package_df = package_df[package_column_order]

            package_df.to_excel(self.package_file, index=False, sheet_name='Package')
            workbook = load_workbook(self.package_file)
            sheet = workbook['Package']
            total_qty_col = package_df.columns.get_loc('Total Qty of Package') + 1

            for row in range(2, len(package_df) + 2):
                sheet[f'{get_column_letter(total_qty_col)}{row}'] = (
                    f'=IFERROR({get_column_letter(total_qty_col - 2)}{row} * '
                    f'{get_column_letter(total_qty_col - 1)}{row}, 0)'
                )

            workbook.save(self.package_file)

        # --- Vendor Data Handling ---
        if self.vendor_data:
            all_vendors = {}
            
            for category, vendors in self.vendor_data.items():
                if not vendors:
                    continue
                    
                for vendor in vendors:
                    vendor_key = (vendor.get('trade_name', ''), vendor.get('gstin', ''))
                    
                    if vendor_key not in all_vendors:
                        all_vendors[vendor_key] = vendor.copy()
                        all_vendors[vendor_key]['categories'] = [category]
                    else:
                        all_vendors[vendor_key]['categories'].append(category)
                        
                        if 'source' in vendor and 'source' in all_vendors[vendor_key]:
                            if isinstance(vendor['source'], list) and isinstance(all_vendors[vendor_key]['source'], list):
                                all_vendors[vendor_key]['source'] = list(set(all_vendors[vendor_key]['source'] + vendor['source']))
                                all_vendors[vendor_key]['source_count'] = len(all_vendors[vendor_key]['source'])
            
            consolidated_vendors = list(all_vendors.values())
            
            df = pd.DataFrame(consolidated_vendors)
            
            df['category'] = df['categories'].apply(lambda x: ', '.join(x) if isinstance(x, list) else '')
            df.drop('categories', axis=1, inplace=True)
            
            if 'source' in df.columns:
                df['source_list'] = df['source'].apply(lambda x: ', '.join(x) if isinstance(x, list) else '')
            
            df = df.sort_values(by=['source_count'], ascending=[False])
            
            column_mapping = {
                'trade_name': 'Vendor Name',
                'gstin': 'GSTIN',
                'state': 'State',
                'address': 'Address',
                'category': 'Category',
                'source_count': 'Total Clients',
                'source_list': 'Client Sources'
            }
            
            df.rename(columns=column_mapping, inplace=True)
            
            display_columns = ['Vendor Name', 'GSTIN', 'State', 'Address', 'Category', 'Total Clients']
            if 'Client Sources' in df.columns:
                display_columns.append('Client Sources')
            display_columns = [col for col in display_columns if col in df.columns]
            df = df[display_columns]
            
            with pd.ExcelWriter(self.vendor_file) as writer:
                df.to_excel(writer, sheet_name='Vendors', index=False)