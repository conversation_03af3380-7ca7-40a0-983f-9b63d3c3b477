from app.utility.masterDataImport import masterDataImport
from app.database import branchesCol,productionsourcecol,servingsizerecipesCol,Stockvalues,restinvcol,categoriesCol
from datetime import datetime
from app.utility.helper import errorValuesFilter
import pandas as pd
import copy


class menuRecipe():
	def __init__(self, tenant_id,masterdata_file,session_id) :
		self.tenantId = tenant_id
		self.masterdata_file = masterdata_file
		self.session_id = session_id

		############################## DATA READ ##########################
		self.menumaster = masterDataImport(self.masterdata_file,'menu master',tenant_id, readFull=False, sessionId=self.session_id)
		self.menurecipe = masterDataImport(self.masterdata_file,'menu recipes',tenant_id, sessionId=self.session_id)
		self.branchesdf = masterDataImport(self.masterdata_file,'branches',tenant_id, sessionId=self.session_id)
		self.sizemappingdf = masterDataImport(masterdata_file,'servingsize conversion',tenant_id, sessionId=self.session_id)

		######################### PRE PROCESSING ##########################
		self.servingsizedict = {}
		for i in range(len(self.menumaster)):
			if self.menumaster['Discontinued'].iloc[i] in ['y','Y','yes','Yes']:
				continue
			sizes = self.menumaster['servingSize'].iloc[i].split(',')
			sizes = list(set(sizes))
			self.servingsizedict[self.menumaster['menuItemCode'].iloc[i].upper()] = [x.strip(' ') for x in sizes]

		self.ckmapping = {}
		for x in range(len(self.branchesdf)):
			branch = self.branchesdf.iloc[x]
			if branch['branchType'] == 'central kitchen':
				restaurantId = branch['restaurantId']
				abbreviation = branch['abbreviated restaurantId']
				self.ckmapping[abbreviation] = restaurantId

		self.sizemapping = {}
		for i in range(len(self.sizemappingdf)):
			serving_size = self.sizemappingdf['Serving Size'].iloc[i]
			checkFlag = 0
			unit = self.sizemappingdf['Conversion Unit'].iloc[i]
			if unit == '%':
				checkFlag =1
				ratio = float(self.sizemappingdf['Ratio'].iloc[i])/100
			else:
				ratio = float(self.sizemappingdf['Ratio'].iloc[i])
			self.sizemapping[serving_size] = {'unit' : unit,'ratio' : ratio}
		

		self.restmapping = {}
		self.workAreaDict = {}
		for x in range(len(self.branchesdf)):
			branch = self.branchesdf.iloc[x]
			restaurantId = branch['restaurantId']
			abbreviation = branch['abbreviated restaurantId']
			self.restmapping[abbreviation] = restaurantId
			workAreas = str(branch['workArea']).split(',')
			workAreas = [i.strip(' ') for i in workAreas]
			workAreas = list(set(workAreas))
			self.workAreaDict[branch['restaurantId']] = workAreas


		################### TRIGGERING OTHER FUNCTIONS ###################
		self.updateRecipes()
		self.createStockvalues()
		self.updateRestInv()
		self.createproductionsource()
		# self.createCategories()

	def updateRecipes(self) :
		self.recipedict = {}
		for i in range(len(self.menurecipe)):
			if self.menurecipe['Discontinued'].iloc[i] in ['y','Y','yes','Yes']:
				continue
			key = self.menurecipe.iloc[i]['menuItemCode'].upper()
			uom = self.menurecipe['ConsumptionUOM'].iloc[i]
			initialWeight = self.menurecipe['InitialWeight'].iloc[i]
			ingredientYield = self.menurecipe.iloc[i]['Yield'] if pd.isnull(self.menurecipe.iloc[i]['Yield']) == False and self.menurecipe.iloc[i]['Yield'] not in ['NA','',None] else 1.0

			if (key) not in self.recipedict:
				temp ={
					"IngredientCode" : self.menurecipe.iloc[i]['ingredientCode'].upper(),
					"IngredientName" : self.menurecipe.iloc[i]['ingredientName'],
					"initialWeight" : initialWeight,
					"ingredientUom" : uom,
					"ingredientYield" : errorValuesFilter(ingredientYield,1),
					'rate' : errorValuesFilter(self.menurecipe.iloc[i]['rate'], 0.0),
					'finalRate' : errorValuesFilter(self.menurecipe.iloc[i]['finalRate'], 0.0)
				}
				if 'isModifier' in self.menurecipe.iloc[i].keys():
					if self.menurecipe.iloc[i]['isModifier'].upper().strip() == "YES":
						self.menurecipe.iloc[i]['isModifier'] ="Y"
					if self.menurecipe.iloc[i]['isModifier'].upper().strip() == "NO":
						self.menurecipe.iloc[i]['isModifier'] ="N"
					temp["isModifier"]= self.menurecipe.iloc[i]['isModifier'].upper()
				else:
					temp["isModifier"]= "N"
				if 'modifierName' in self.menurecipe.iloc[i].keys():
					temp["modifierName"]= self.menurecipe.iloc[i]['modifierName'].strip()
				else:
					temp["modifierName"]="NAN"
				self.recipedict[key] = {
					'tenantId' : self.tenantId,
					'Ingredients' : [temp],
					'menuItemCode' : self.menurecipe.iloc[i]['menuItemCode'].upper(),
					'menuItemName' : self.menurecipe.iloc[i]['menuItemName'],
					'tenantId' : self.tenantId,
					'isSubRecipe': False
				}
			else:
				temp={
					"IngredientCode" : self.menurecipe.iloc[i]['ingredientCode'].upper(),
					"IngredientName" : self.menurecipe.iloc[i]['ingredientName'],
					"initialWeight" : initialWeight,
					"ingredientUom" : uom,
					"ingredientYield" : errorValuesFilter(ingredientYield,1),
					'rate' : errorValuesFilter(self.menurecipe.iloc[i]['rate'], 0.0),
					'finalRate' : errorValuesFilter(self.menurecipe.iloc[i]['finalRate'], 0.0)
				}
				if 'isModifier' in self.menurecipe.iloc[i].keys():
					if self.menurecipe.iloc[i]['isModifier'].upper().strip() == "YES":
						self.menurecipe.iloc[i]['isModifier'] ="Y"
					if self.menurecipe.iloc[i]['isModifier'].upper().strip() == "NO":
						self.menurecipe.iloc[i]['isModifier'] ="N"
					temp["isModifier"]= self.menurecipe.iloc[i]['isModifier'].upper()
				else:
					temp["isModifier"]= "N"
				if 'modifierName' in self.menurecipe.iloc[i].keys():
					temp["modifierName"]= self.menurecipe.iloc[i]['modifierName'].strip()
				else:
					temp["modifierName"]="NAN"
				self.recipedict[key]['Ingredients'].append(temp)

		overAll=[]
		for key in self.recipedict.keys():
			if key not in self.servingsizedict:
				continue
			for servingsize in self.servingsizedict[key]:
				ratio = self.sizemapping[servingsize]['ratio']
				item = copy.deepcopy(self.recipedict[key])
				item['servingSizeRatio'] = float(ratio)		
				for ingredient in item['Ingredients']:
					try:
						ingredient['initialWeight'] = float(ingredient['initialWeight'])
					except:
						ingredient['initialWeight'] = 0
					ingredient['initialWeight'] = float(ingredient['initialWeight']) * ratio
					ingredient['WeightInUse'] = ingredient['initialWeight'] * float(errorValuesFilter(ingredient['ingredientYield'],1))
				item['servingSize'] = servingsize
				overAll.append(item)
		if len(self.menumaster) >0 and len(self.menumaster) <=100:
			servingsizerecipesCol.delete_many({
				'tenantId' : self.tenantId, 
				"isSubRecipe" : False,
				"menuItemCode" :{"$in" : self.menumaster['menuItemCode'].tolist()}
			})
			servingsizerecipesCol.insert_many(overAll)
		elif len(self.menumaster) >0 and len(overAll) > 0:
			# print("#########",overAll)
			servingsizerecipesCol.delete_many({'tenantId' : self.tenantId, "isSubRecipe" : False})
			servingsizerecipesCol.insert_many(overAll)

	def createStockvalues(self) :
		allitems =[]
		for i in range(len(self.menumaster)):
			item = self.menumaster.iloc[i]
			if item['Discontinued'] in ['y', 'Y', 'yes', 'Yes']:
				status = 'discontinued'
			else:
				status = 'active'
			preparedAt = str(item['preparedAt']).split(',')
			preparedAt = [i.strip(' ') for i in preparedAt]
			preparedAt = list(set(preparedAt))
			usedAtOutlets = str(item['usedAtOutlet']).split(',')
			usedAtOutlets = [i.strip(' ') for i in usedAtOutlets]
			usedAtOutlets = list(set(usedAtOutlets))
			restaurants = preparedAt + usedAtOutlets
			restaurants = [self.restmapping[i] for i in restaurants if i in self.restmapping]
			restaurants = set(restaurants)
			for restaurantId in restaurants:
				workAreas = [i.strip(' ') for i in item['usedInWorkArea'].split(',')]
				workAreas = list(set(workAreas))
				itemWorkAreas = {}
				for workArea in workAreas:
					if workArea not in self.workAreaDict[restaurantId]:
						continue
					else:
						itemWorkAreas[workArea] = 0.0
				entry = {
					'tenantId': self.tenantId,
					'restaurantId': restaurantId,
					'category': str(item.get('category','N/A')).upper(),
					'subCategory': str(item.get('subCategory', 'N/A')).upper(),
					'itemCode': item['menuItemCode'].upper(),
					'itemName': item['menuItemName'],
					'inStock': 0.0,
					'workArea': itemWorkAreas,
					'ItemType': 'Menu',
					'uom': item['closingUOM'],
					'menuGroup': {'name': str(item.get('subCategory', 'N/A')).upper()},
					'status' : status
				}
				allitems.append(entry)
		if len(self.menumaster) > 0 and len(self.menumaster) <= 100 and len(allitems) > 0: 
			self.updateValue(allitems, 'Menu', self.menumaster['menuItemCode'].tolist(), updateFull =False)
		elif len(self.menumaster) > 0 and len(allitems) > 0: 
			self.updateValue(allitems, 'Menu')

	def updateRestInv(self) :
		allitems =[]
		for x in range(len(self.menumaster)):
			if self.menumaster['Discontinued'].iloc[x] in ['y','Y','yes','Yes']:
				continue
			preparedAt = str(self.menumaster.iloc[x]['preparedAt']).split(',')
			preparedAt = [i.strip(' ') for i in preparedAt]
			preparedAt = list(set(preparedAt))
			usedAtOutlets = str(self.menumaster.iloc[x]['usedAtOutlet']).split(',')
			usedAtOutlets = [i.strip(' ') for i in usedAtOutlets]
			usedAtOutlets = list(set(usedAtOutlets))

			POSName = self.menumaster.iloc[x]['menuItemName']
			for restaurant in usedAtOutlets:
				itemCode = self.menumaster.iloc[x]['menuItemCode'].upper()
				item = {
					'itemCode' : self.menumaster.iloc[x]['menuItemCode'].upper(),
					'itemName' : self.menumaster.iloc[x]['menuItemName'],
					'POSName' : POSName,
					'tenantId' : self.tenantId,
					'category' : str(self.menumaster.iloc[x].get('category', 'N/A')).upper(),
					'subCategory' : str(self.menumaster.iloc[x].get('subCategory', 'N/A')).upper(),
					'ItemType' : self.menumaster.iloc[x]['itemType'],
					'uom' : 'NOS',
					'closingUom' : self.menumaster.iloc[x]['closingUOM'],
					'status':{},
					'statusHistory' : [{
						'desc' : 'created',
						'field' : 'None',
						'val' : 0,
						'dateTs' : datetime.now()
					}],
					'createTs' : datetime.now(),
					'inKitchen' : 0.0,
					'inStock' : 0.0,
					'source' : {},
					'optimumStock': 9999999.0,
				}
				if pd.isnull(self.menumaster['closingUOM'].iloc[x]) or str(self.menumaster['closingUOM'].iloc[x]) in ['nan','','NA','#N/A']:
					item['consideredForClosing'] = False
				else:
					item['consideredForClosing'] = True

				item['hasReceipe'] = True
				item['isSubReceipe'] = False
				item['menuGroup'] = {'name' : str(self.menumaster.iloc[x].get('subCategory', 'N/A')).upper()}
				if restaurant in self.restmapping:
					item['restaurantId'] = self.restmapping[restaurant]
				else:
					continue
				if restaurant in preparedAt:
					item['source']['local'] = True
					item['source']['threshold'] = 10000.0
				else:
					item['source']['local'] = False
					item['source']['threshold'] = 0.0
				workAreas = [i.strip(' ') for i in self.menumaster.iloc[x]['usedInWorkArea'].split(',')]
				workAreas = list(set(workAreas))
				item['workArea'] = {}
				for workArea in workAreas:
					if workArea not in self.workAreaDict[item['restaurantId']]:
						continue
					item['workArea'][workArea] = 0.0
				item['leadTime'] = 1
				item['stockCover'] = 1
				item['grnThreshold'] = {
					'extra' : 10,
					'deficit' : 10
				}
				allitems.append(item)

			for outlet in preparedAt:
				itemCode = self.menumaster.iloc[x]['menuItemCode'].upper()
				item = {
					'itemCode' : self.menumaster.iloc[x]['menuItemCode'].upper(),
					'itemName' : self.menumaster.iloc[x]['menuItemName'],
					'POSName' : POSName,
					'tenantId' : self.tenantId,
					'category' : self.menumaster.iloc[x]['category'].upper(),
					'subCategory' : str(self.menumaster.iloc[x].get('subCategory', 'N/A')).upper(),
					'ItemType' : self.menumaster.iloc[x]['itemType'],
					'uom' : 'NOS',
					'closingUom' : self.menumaster.iloc[x]['closingUOM'],
					'status':{},
					'statusHistory' : [{
						'desc' : 'created',
						'field' : 'None',
						'val' : 0,
						'dateTs' : datetime.now()
					}],
					'createTs' : datetime.now(),
					'inKitchen' : 0.0,
					'inStock' : 0.0,
					'source' : {},
					'optimumStock': 9999999.0,
				}
				if str(self.menumaster['closingUOM'].iloc[x]) == 'nan':
					item['consideredForClosing'] = False
				else:
					item['consideredForClosing'] = True
				item['hasReceipe'] = True
				item['isSubReceipe'] = False
				item['menuGroup'] = {'name' : str(self.menumaster.iloc[x].get('subCategory', 'N/A')).upper()}

				if outlet in self.restmapping:
					item['restaurantId'] = self.restmapping[outlet]
				else:
					continue
				if outlet in usedAtOutlets:
					continue
				else:
					item['source']['local'] = True
					item['source']['threshold'] = 10000.0
				workAreas = [i.strip(' ')for i in self.menumaster.iloc[x]['usedInWorkArea'].split(',')]
				workAreas = list(set(workAreas))
				item['workArea'] = {}
				for workArea in workAreas:
					if workArea not in self.workAreaDict[item['restaurantId']]:
						continue
					item['workArea'][workArea] = 0.0
				item['leadTime'] = 1
				item['stockCover'] = 1
				item['grnThreshold'] = {
					'extra' : 10,
					'deficit' : 10
				}
				allitems.append(item)	
		if len(self.menumaster) >0 and len(self.menumaster) <=100 and len(allitems) > 0: 
			restinvcol.delete_many({'tenantId' : self.tenantId, "ItemType": "Menu", "itemCode" :{"$in" : self.menumaster['menuItemCode'].tolist()}})
			restinvcol.insert_many(allitems)
		elif len(self.menumaster) >0 and len(allitems) > 0: 
			restinvcol.delete_many({'tenantId' : self.tenantId, "ItemType": "Menu"})
			restinvcol.insert_many(allitems)

	def createproductionsource(self) : 
		overAll =[]
		for x in range(len(self.menumaster)):
			restaurants = str(self.menumaster.iloc[x]['preparedAt']).split(',')
			restaurants = [i.strip(' ') for i in restaurants]
			restaurants = list(set(restaurants))
			for restaurant in restaurants:
				if restaurant in self.ckmapping:
					item = {
						'itemCode' : self.menumaster.iloc[x]['menuItemCode'],
						'itemName' : self.menumaster.iloc[x]['menuItemName'],
						'tenantId' : self.tenantId,
						'restaurantId' : self.ckmapping[restaurant],
						'priority' : 1,
						'flag' : True,
						'percentage' : 100,
						'createTs' : datetime.now(),
						'modTs' : datetime.now(),
						'threshold' : 10000,
					}
					overAll.append(item)
		if len(overAll) > 0:
			if (len(overAll) > 0 and (len(overAll) <=100)) and len(overAll) > 0: 
				items = [x['itemCode'] for x in overAll]
				productionsourcecol.delete_many({'tenantId' : self.tenantId, "itemCode" :{"$in" : items}})
				productionsourcecol.insert_many(overAll)
			elif len(overAll)>0 and len(overAll) > 0: 
				productionsourcecol.delete_many({'tenantId' : self.tenantId})
				productionsourcecol.insert_many(overAll)

	def createCategories(self) :
		menuDict = {}
		for i in range(len(self.menumaster)):
			item = self.menumaster.iloc[i]
			category = str(item.get('category','N/A')).upper()
			subCategory = str(item.get('subCategory', 'N/A')).upper()
			restaurants = str(item['preparedAt']).split(',') + str(item['usedAtOutlet']).split(',')
			restaurants = [i.strip(' ') for i in restaurants]
			restaurants = list(set(restaurants))
			workAreas = [i.strip(' ') for i in item['usedInWorkArea'].split(',')]
			workAreas = list(set(workAreas))
			if subCategory not in menuDict:
				menuDict[subCategory] = {
					'tenantId' : self.tenantId,
					'type' : 'menu',
					'category' : category,
					'subCategory' : subCategory,
					'restaurants' : set(restaurants),
					'workAreas' : set(workAreas),
					'createTs' : datetime.now()
				}
			else:
				for restaurant in restaurants:
					menuDict[subCategory]['restaurants'].add(restaurant)
				for workArea in workAreas:
					menuDict[subCategory]['workAreas'].add(workArea)
		totalList = list(menuDict.values())
		overAll=[]
		for entry in totalList:
			entry['restaurants'] = [self.branchesdf[i] for i in entry['restaurants']  if i in self.branchesdf]
			entry['workAreas'] = list(entry['workAreas'])
			overAll.append(entry)
		categoriesCol.delete_many({'tenantId': self.tenantId})
		categoriesCol.insert_many(overAll)

	def updateValue(self,allitems, itemType, itemList =[], updateFull =True):	
		if updateFull:
			stockvalueslist = Stockvalues.find({'tenantId': self.tenantId, "ItemType" : itemType})
		else:
			stockvalueslist = Stockvalues.find({'tenantId' : self.tenantId, "ItemType": itemType, "itemCode" :{"$in" : itemList}})
		stockvaluesdict = {}
		for entry in stockvalueslist:
			restaurantId = entry['restaurantId']
			itemCode = entry['itemCode'].upper()
			if restaurantId not in stockvaluesdict:
				stockvaluesdict[restaurantId] = {}
			if 'entryType' in entry.keys():
				entryType = entry['entryType']
				if entryType.lower() == 'open':
					stockvaluesdict[restaurantId][itemCode+'|'+entryType] = entry
				else: 
					stockvaluesdict[restaurantId][itemCode +'|'+entryType+'|'+entry['packageName']] = entry
			else:
				stockvaluesdict[restaurantId][itemCode] = entry
		overAll = []
		for entry in allitems:
			restaurantId = entry['restaurantId']
			itemCode = entry['itemCode'].upper()
			if 'entryType' in entry.keys():
				entryType = entry['entryType']
				if entryType.lower() == 'open':
					searchKey = itemCode+'|'+entryType
				else:
					packageName = entry['packageName']
					searchKey = itemCode+'|'+entryType+'|'+packageName
				if restaurantId in stockvaluesdict.keys():
					if searchKey in stockvaluesdict[restaurantId]:
						prevEntry = stockvaluesdict[restaurantId][searchKey]
						entry['inStock'] = prevEntry['inStock']
						if 'price' in prevEntry.keys():
							entry['price'] = prevEntry['price']
						if 'servingSize' in prevEntry.keys():
							entry['servingSize'] = prevEntry['servingSize']
						if 'servingSizeRatio' in prevEntry.keys():
							entry['servingSizeRatio'] = prevEntry['servingSizeRatio']
						if 'lastGrnPrice' in prevEntry.keys():
							entry['lastGrnPrice'] = prevEntry['lastGrnPrice']
						if 'costSplitUp' in prevEntry.keys():
							entry['costSplitUp'] = prevEntry['costSplitUp']
						if 'withTaxPrice' in prevEntry.keys():
							entry['withTaxPrice'] = prevEntry['withTaxPrice']
						if 'taxRate' in prevEntry.keys():
							entry['taxRate'] = prevEntry['taxRate']
						workAreaTemp={}
						for workArea in entry['workArea'].keys():
							if workArea in prevEntry['workArea'].keys():
								workAreaTemp[workArea] = prevEntry['workArea'][workArea]
							else:
								workAreaTemp[workArea] = 0
						entry['workArea'] = workAreaTemp
			else:
				if restaurantId in stockvaluesdict.keys():
					if itemCode in stockvaluesdict[restaurantId]:
						prevEntry = stockvaluesdict[restaurantId][itemCode]
						if 'servingSize' in prevEntry.keys():
							entry['servingSize'] = prevEntry['servingSize']
						if 'servingSizeRatio' in prevEntry.keys():
							entry['servingSizeRatio'] = prevEntry['servingSizeRatio']
						if 'lastGrnPrice' in prevEntry.keys():
							entry['lastGrnPrice'] = prevEntry['lastGrnPrice']
						if 'price' in prevEntry.keys():
							entry['price'] = prevEntry['price']
						if 'costSplitUp' in prevEntry.keys():
							entry['costSplitUp'] = prevEntry['costSplitUp']
						if 'withTaxPrice' in prevEntry.keys():
							entry['withTaxPrice'] = prevEntry['withTaxPrice']
						if 'taxRate' in prevEntry.keys():
							entry['taxRate'] = prevEntry['taxRate']

						entry['inStock'] = prevEntry['inStock']
						workAreaTemp={}
						for workArea in entry['workArea'].keys():
							if workArea in prevEntry['workArea'].keys():
								workAreaTemp[workArea] = prevEntry['workArea'][workArea]
							else:
								workAreaTemp[workArea] = 0
						entry['workArea'] = workAreaTemp
			overAll.append(entry)
		if updateFull:
			Stockvalues.delete_many({'tenantId': self.tenantId, "ItemType": itemType})
			Stockvalues.insert_many(overAll)
		else:
			Stockvalues.delete_many({'tenantId' : self.tenantId, "ItemType": itemType, "itemCode" :{"$in" : itemList}})
			Stockvalues.insert_many(overAll)
