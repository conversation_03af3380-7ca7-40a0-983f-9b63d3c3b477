system_prompt = '''Act as an expert in creating delicious and nutritious recipes that are easy to make and use readily available ingredients in any household. '''

user_prompt = '''Using the provided {menu_master} and {menu_recipes} data, please create an enticing recipe card for the item specified in {menu_master}. Your recipe card should:

1. Use only the ingredients listed in {menu_recipes} for this item.
2. Include the exact quantities specified in {menu_recipes}.
3. Provide clear, step-by-step instructions for preparation.
4. Suggest possible variations using only the listed ingredients.
5. Include a brief, appetizing description of the item and its flavor profile.
6. Estimate the calorie content based on the ingredients used.
7. Use better markdown standards across the response and also add new lines, if required

Please format your response as follows:

**🍹 [Item Name from {menu_master}]
**📋 Category: [Category from {menu_master}]
**🔖 Subcategory: [Subcategory from {menu_master}]

**✨ Description: 
[1-2 sentences describing the item, its flavors, and appeal]

**🥄 Ingredients: [Qty, tab, UOM, tab, ingredient name]
- [List ingredients with exact quantities from {menu_recipes}]

**👨‍🍳 Instructions: 
1. [Step-by-step preparation method]
2. ...

**💡 Variations: 
- [2-3 suggestions using only listed ingredients]

**🍽️ Serving Info: 
- Prepared at: [PreparedAt from {menu_master}]
- Served at: [UsedAtOutlet from {menu_master}]
- Serving size: [ServingSize from {menu_master}]

**🔢 Nutrition Facts: 
- Estimated calories: [Calculate based on ingredients] (based on the ingredients used)
- show calories split on each ingredient level
- Show Protein, Carbohydrates, Fat everything related to
- [Any other relevant nutritional info]

Best of luck for your [Item Name]! 🎉'''