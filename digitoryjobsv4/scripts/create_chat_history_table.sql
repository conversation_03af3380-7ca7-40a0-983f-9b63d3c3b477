-- Create chat_history table
CREATE TABLE IF NOT EXISTS chat_history (
    id SERIAL PRIMARY KEY,
    tenant_id VARCHAR(255) NOT NULL,
    message_type VARCHAR(10) NOT NULL CHECK (message_type IN ('human', 'ai')),
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries by tenant_id
CREATE INDEX IF NOT EXISTS idx_tenant_id ON chat_history (tenant_id);

-- We don't need SQL functions as we'll use direct SQL queries in the Python code
