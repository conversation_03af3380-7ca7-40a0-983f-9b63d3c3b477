from sqlalchemy import create_engine, text, Table, MetaData
import time
import re
import csv
import json
import os
from io import StringIO
from pymongo import MongoClient
from dotenv import load_dotenv
import pandas as pd
from utility import get_vendors
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from address_utils import extract_state_from_address

load_dotenv()
postgres_client = create_engine(os.getenv("POSTGRES_URL"))
mongo_client = MongoClient(os.getenv("DATABASE_URL"))
db = mongo_client[os.getenv("MONGO_INITDB_DATABASE")]
masterdataupdateconfigs = db['masterdataupdateconfigs']
roloposconfigs = db['roloposconfigs']
restaurantinventories = db['restaurantinventories']
tenants = db['tenants']


def psql_insert_copy(table, conn, keys, data_iter):
    """Insert data into PostgreSQL using COPY with deduplication handling and source array merging"""
    dbapi_conn = conn.connection
    with dbapi_conn.cursor() as cur:
        s_buf = StringIO()
        writer = csv.writer(s_buf)
        writer.writerows(data_iter)
        s_buf.seek(0)

        columns = ', '.join(f'"{k}"' for k in keys)
        table_name = f"{table.schema}.{table.name}" if table.schema else table.name
        temp_table = f"{table_name}_temp"

        # Create temporary table with same structure as original table
        cur.execute(f"""
            CREATE TEMP TABLE {temp_table} (LIKE {table_name} INCLUDING DEFAULTS INCLUDING CONSTRAINTS)
            ON COMMIT DROP;
        """)

        # Use COPY to load data into the temp table
        cur.copy_expert(f'COPY {temp_table} ({columns}) FROM STDIN WITH CSV', s_buf)

        if table_name == "inventory_master":
            dedup_cols = ["item_name"]
        elif table_name == "vendor_master":
            dedup_cols = ["gstin", "trade_name"]
        else:
            dedup_cols = ["item_name", "quantity_per_unit"]

        update_cols = [col for col in keys if col not in dedup_cols]

        if "source" in keys:
            set_values = ', '.join([
                f'{col} = EXCLUDED.{col}' if col != "source" else
                f'source = CASE WHEN {table_name}.source IS NULL THEN EXCLUDED.source ELSE (SELECT array_agg(DISTINCT unnest) FROM unnest({table_name}.source || EXCLUDED.source)) END'
                for col in update_cols
            ])
        else:
            set_values = ', '.join([f'{col} = EXCLUDED.{col}' for col in update_cols])

        # Insert with deduplication and conflict handling
        cur.execute(f"""
            INSERT INTO {table_name} ({columns})
            SELECT DISTINCT ON ({', '.join(dedup_cols)}) {columns}
            FROM {temp_table}
            ON CONFLICT ({', '.join(dedup_cols)}) DO UPDATE
            SET {set_values if set_values else 'NOTHING'};
        """)

        # ✅ Commit the transaction at the driver level
        dbapi_conn.commit()

def standardize_strings(df, columns=None):
    """Convert string columns to uppercase and strip spaces"""
    columns = columns or df.select_dtypes(include=['object']).columns
    for col in columns:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip().str.upper()
    return df

def convert_numeric_columns(df, columns, default_values=None):
    """Convert columns to numeric with optional default values"""
    default_values = default_values or {}
    for col in columns:
        if col in df.columns:
            default = default_values.get(col, 0.0)
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(default)
    return df

def validate_value_in_set(df, column, valid_values, default_value):
    """Ensure values in column are in the valid set, otherwise use default"""
    if column in df.columns:
        df[column] = df[column].apply(lambda x: x if x in valid_values else default_value)
    return df

def set_default_if_empty(df, column, default_value_column):
    """Set value from another column if the target column is empty"""
    if column in df.columns and default_value_column in df.columns:
        df[column] = df.apply(
            lambda row: row[default_value_column] if row[column] in ['', 'NAN', 'NONE', 'NULL'] else row[column],
            axis=1
        )
    return df

def drop_duplicates_by_keys(df, key_columns):
    """Remove duplicate rows based on specified key columns"""
    return df.drop_duplicates(subset=key_columns, keep='last')

def clean_item_names(df):
    """
    Clean item_name by removing package information, MRP values, and standardizing format.
    """
    df['original_item_name'] = df['item_name']

    # Special characters to remove - REMOVE PARENTHESES FROM THIS LIST
    characters_to_remove = r'\r\n\t\\=#\?\[\]\*!\'\'\"\`\,'
    pattern = '[' + characters_to_remove + ']'

    # Remove special characters from item_name
    df['item_name'] = df['item_name'].replace(to_replace=pattern, value='', regex=True)

    # Updated patterns to match and clean package-related information
    package_patterns = [
        # Handle MRP values with or without space
        r'\bMRP\s*\d+[\.]?\d*\b',  # MRP followed by number (with or without space)
        r'\bMRP\d+[\.]?\d*\b',     # MRP directly attached to number

        # Handle measurements with no space between number and unit
        r'\b(\d+[\.]?\d*)(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES)\b',

        # Handle measurements with space between number and unit
        r'\b(\d+[\.]?\d*)\s+(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES)\b',

        # Handle measurements with unit followed by package type
        r'\b(\d+[\.]?\d*)\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES)(\s+)(KEG|CAN|BOTTLE|SACHET|TIN|JAR|CARTON|PACK)\b',

        # Handle just numbers followed by packaging types
        r'\b(\d+[\.]?\d*)\s+(KEG|CAN|BOTTLE|SACHET|TIN|JAR|CARTON|PACK)\b',

        # Sizes and packaging formats not inside parentheses
        r'(?<!\()\b(\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES|CAN|KEG|BOTTLE|SACHET|TIN|JAR|CARTON|PACK))\b(?!\))',

        # Sizes at end of string
        r'\b(\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES|CAN|KEG|BOTTLE|SACHET|TIN|JAR|CARTON|PACK))$',

        # Sizes in middle of the string
        r'\b(\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES|CAN|KEG|BOTTLE|SACHET|TIN|JAR|CARTON|PACK))\s+',

        # Piece counts at beginning, end, and middle
        r'^\d+[\.]?\d*\s*(PCS|NOS|PIECE|PIECES)\s+',
        r'\s+\d+[\.]?\d*\s*(PCS|NOS|PIECE|PIECES)$',
        r'\s+\d+[\.]?\d*\s*(PCS|NOS|PIECE|PIECES)\s+',

        # Handle "500ML/BOX" or "100G/PKT"
        r'\b\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES)\s*/\s*(BOX|PKT|PACKET|PACK|CARTON)\b',

        # Handle ranges like "10-20ML" or "5~10KG"
        r'\b\d+[\.]?\d*\s*[-~]\s*\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS)\b',

        # Handle fractional quantities like "1/2 KG" or "1/4 L"
        r'\b\d+\s*/\s*\d+\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC)\b',

        # Handle package info with colons like "Size: 500ML" or "Qty: 5PCS"
        r'(SIZE|QTY|QUANTITY|WEIGHT|VOLUME|CAPACITY|PACK)\s*:\s*\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES|CAN|KEG|BOTTLE)',

        # Handle symbols like "500ML-" or "200G/"
        r'\b\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES)\s*[-/]',

        # Handle special formats with leading symbols like "-500ML" or "/100G"
        r'[-/]\s*\d+[\.]?\d*\s*(ML|M|L|G|KG|GM|GMS|BTL|LTRS|GRAM|MRP|GRM|KGS|OZ|LTR|LITRE|LITER|CC|NOS|PCS|PIECE|PIECES)\b',

        # Remove trailing and leading symbols carefully - BUT DON'T TOUCH PARENTHESES WITH CONTENT
        r'[/-]+\s*$',
        r'^\s*[/-]+',

        # Explicit CC handling
        r'(?<!\()\b(\d+[\.]?\d*\s*CC)\b(?!\))',

        # Lowercase or mixed case unit formats
        r'\b\d+[\.]?\d*\s*(ml|m|l|g|kg|gm|gms|btl|gram|mrp|grm|kgs|oz|litre|liter|cc|nos|pcs|piece|pieces|can|keg|bottle|sachet|tin|jar|carton|pack)\b',

        # Additional packaging terms
        r'\b\d+[\.]?\d*\s*(BUNDLE|SET|BOX|CASE|UNIT|TABLET|CAPSULE|TAB|CAP|VIAL|AMPOULE|AMP|TUBE|POUCH|BAG)\b'
    ]

    # Apply the patterns to clean the item names
    for pattern in package_patterns:
        df['item_name'] = df['item_name'].str.replace(pattern, '', regex=True)

    # Clean up extra spaces, hyphens, but PRESERVE PARENTHESES WITH CONTENT
    df['item_name'] = (
        df['item_name']
        .str.strip()
        .str.replace(r'\s+', ' ', regex=True)         # Normalize spaces
        .str.replace(r'\s*-\s*$', '', regex=True)     # Remove trailing hyphens
        .str.replace(r'\(\s*\)', '', regex=True)      # Remove only empty parentheses
    )

    return df

def transform_inventory_master(df, source):
    """Transform inventory master data with enhanced validations, item name cleaning and digiID assignment"""
    rename_mapping = {
        'category': 'category',
        'subCategory': 'sub_category',
        'itemName': 'item_name',
        'Inventory UOM': 'inventory_uom',
        'closingUOM': 'closing_uom',
        'taxRate': 'tax_rate',
        'weight': 'weight',
        'yield': 'yield',
        'leadTime(days)': 'lead_time_days',
        'classification': 'classification',
        'Ledger': 'ledger',
        'HSN_SAC': 'hsn_sac',
        'tenantId': 'source'
    }

    numeric_columns = {
        'tax_rate': 0.0,
        'weight': 0.0,
        'yield':0.0,
        'lead_time_days': 1.0
    }

    valid_uoms = ['KG', 'LITRE', 'NOS', 'MTR']
    valid_classifications = ['NON-STOCKABLE', 'STOCKABLE']
    df_filtered = df.rename(columns=rename_mapping).reindex(columns=rename_mapping.values(), fill_value='')
    df_filtered = standardize_strings(df_filtered)
    df_filtered = clean_item_names(df_filtered)
    df_filtered = convert_numeric_columns(df_filtered, numeric_columns.keys(), numeric_columns)
    df_filtered = validate_value_in_set(df_filtered, 'inventory_uom', valid_uoms, 'KG')
    df_filtered = validate_value_in_set(df_filtered, 'closing_uom', valid_uoms, 'Open/KG')
    df_filtered = validate_value_in_set(df_filtered, 'classification', valid_classifications, 'STOCKABLE')
    df_filtered = set_default_if_empty(df_filtered, 'ledger', 'category')
    df_filtered['source'] = df_filtered.apply(lambda row: [source], axis=1)
    df_filtered = drop_duplicates_by_keys(df_filtered, ['item_name'])
    return df_filtered

def transform_packaging_master(df, source):
    """Transform packaging master data with enhanced validations and use standardized item names"""
    rename_mapping = {
        'ItemName': 'item_name',
        'brand': 'brand',
        'PackageName': 'package_name',
        'UPC/EAN codes': 'upc_ean_code',
        'Units/ package': 'unit_per_package',
        'Quantity per unit': 'quantity_per_unit',
        'UnitUOM': 'unit_uom',
        'Full bottle weight': 'full_bottle_weight',
        'Empty bottle weight': 'empty_bottle_weight',
        'PackagePrice': 'package_price'
    }

    numeric_columns = {
        'unit_per_package': 0.0,
        'quantity_per_unit': 0.0,
        'full_bottle_weight': 0.0,
        'empty_bottle_weight': 0.0,
        'package_price': 0.0
    }

    valid_uoms = ['KG', 'LITRE', 'NOS', 'MTR']
    df_filtered = df.rename(columns=rename_mapping).reindex(columns=rename_mapping.values(), fill_value=None)
    df_filtered = standardize_strings(df_filtered)
    df_filtered = clean_item_names(df_filtered)
    df_filtered = convert_numeric_columns(df_filtered, numeric_columns.keys(), numeric_columns)
    df_filtered = validate_value_in_set(df_filtered, 'unit_uom', valid_uoms, 'KG')
    df_filtered = df_filtered[df_filtered['quantity_per_unit'] < 25]
    df_filtered['source'] = df_filtered.apply(lambda row: [source], axis=1)
    df_filtered = drop_duplicates_by_keys(df_filtered, ['item_name', 'quantity_per_unit'])
    return df_filtered

def create_tables(engine, vendor=False):
    """Create tables with updated constraints for validation"""
    with engine.begin() as conn:

        if vendor:
            conn.execute(text("DROP TABLE IF EXISTS vendor_master"))
            conn.execute(text('''
                CREATE TABLE vendor_master (
                    id SERIAL PRIMARY KEY,
                    gstin VARCHAR(255),               -- GST ID (can be NULL)
                    trade_name VARCHAR(255) NOT NULL, -- Trade name (must not be NULL)
                    pan VARCHAR(255),                 -- PAN number
                    dealer_type VARCHAR(50),          -- Dealer type
                    entity_type VARCHAR(50),          -- Entity type
                    status VARCHAR(50),               -- Status
                    state VARCHAR(50),                -- State
                    address TEXT,                     -- Address
                    source VARCHAR[] NOT NULL,        -- Source (array of values)
                    top_categories JSONB,             -- Top categories as JSONB
                    UNIQUE (gstin, trade_name) -- Composite unique constraint
                )
            '''))

        else:
            conn.execute(text("DROP TABLE IF EXISTS packaging_master"))
            conn.execute(text("DROP TABLE IF EXISTS inventory_master"))

            conn.execute(text('''
                CREATE TABLE inventory_master (
                    id SERIAL PRIMARY KEY,
                    digi_id INTEGER,
                    category VARCHAR,
                    sub_category VARCHAR NOT NULL,
                    item_name VARCHAR NOT NULL UNIQUE,
                    original_item_name VARCHAR,
                    inventory_uom VARCHAR NOT NULL DEFAULT 'KG' CHECK (inventory_uom IN ('KG', 'LITRE', 'NOS', 'MTR')),
                    closing_uom VARCHAR NOT NULL DEFAULT 'KG' CHECK (closing_uom IN ('KG', 'LITRE', 'NOS', 'MTR', 'Open/KG')),
                    tax_rate FLOAT,
                    yield FLOAT,
                    weight FLOAT,
                    lead_time_days FLOAT DEFAULT 1,
                    classification VARCHAR NOT NULL CHECK (classification IN ('NON-STOCKABLE', 'STOCKABLE')) DEFAULT 'STOCKABLE',
                    ledger VARCHAR,
                    hsn_sac VARCHAR,
                    source VARCHAR[] NOT NULL
                )
            '''))

            conn.execute(text('''
                CREATE TABLE packaging_master (
                    id SERIAL PRIMARY KEY,
                    upc_ean_code VARCHAR,
                    package_name VARCHAR NOT NULL,
                    item_name VARCHAR NOT NULL,
                    original_item_name VARCHAR,
                    unit_per_package FLOAT NOT NULL,
                    quantity_per_unit FLOAT NOT NULL,
                    unit_uom VARCHAR NOT NULL DEFAULT 'KG' CHECK (unit_uom IN ('KG', 'LITRE', 'NOS', 'MTR')),
                    full_bottle_weight FLOAT,
                    empty_bottle_weight FLOAT,
                    package_price FLOAT,
                    brand VARCHAR,
                    UNIQUE(item_name, quantity_per_unit),
                    source VARCHAR[] NOT NULL
                )
            '''))

def process_data(engine, client, source, table_name, sheet_info):
    """Process a single data file with error handling and statistics"""

    print(f"\nProcessing {client} - {table_name}...")
    file_path= f"../masterdata/base/{client}/{sheet_info['filename']}"
    if os.path.exists(file_path):
        df = pd.read_csv(file_path)
        initial_count = len(df)

        df_transformed = sheet_info['transform_func'](df, source)
        df_transformed['source'] = df_transformed['source'].apply(lambda x: "{" + ",".join(f'"{item}"' for item in x) + "}")

        final_count = len(df_transformed)

        if not df_transformed.empty:
            df_transformed.to_sql(
                name=table_name,
                con=engine,
                if_exists="append",
                index=False,
                method=psql_insert_copy
            )

        stats = {
            "initial_rows": initial_count,
            "processed_rows": final_count,
            "filtered_rows": initial_count - final_count
        }

        print(f"Successfully loaded {client} - {table_name}: {final_count} rows inserted")
        return stats
    else:
        print(f"File does not exist: {file_path}")
        return {
            "initial_rows": 0,
            "processed_rows": 0,
            "filtered_rows": 0
        }


def process_vendors(engine, tenant_ids=[]):
    """Aggregate vendors from MongoDB and insert into PostgreSQL with extracted GST details and cleaned addresses."""

    create_tables(engine, vendor=True)
    vendors = list(tenants.aggregate(get_vendors(tenant_ids)))

    if not vendors:
        print("No vendors found.")
        return

    metadata = MetaData()
    vendor_table = Table('vendor_master', metadata, autoload_with=engine)

    batch_size = 1000
    batch = []

    def standardize_address(address_str):
        """Simple function to standardize addresses without using an agent"""

        if not address_str or not isinstance(address_str, str):
            return ""

        address_str = address_str.strip()
        if not address_str or address_str.lower() in ('none', 'null', 'undefined', 'n/a', 'na', '-', 'unknown'):
            return ""

        address_str = address_str.upper()
        address_str = re.sub(r'NO:\s*', '', address_str)
        city_state_mapping = {
            r'\bBLR\b': 'BENGALURU',
            r'\bBOM\b': 'MUMBAI',
            r'\bDEL\b': 'DELHI',
            r'\bTN\b': 'TAMIL NADU',
            r'\bKA\b': 'KARNATAKA',
            r'\bMH\b': 'MAHARASHTRA',
            r'\bAP\b': 'ANDHRA PRADESH',
            r'\bUP\b': 'UTTAR PRADESH',
            r'\bMP\b': 'MADHYA PRADESH',
            r'\bWB\b': 'WEST BENGAL',
            r'\bCHN\b': 'CHENNAI',
            r'\bHYD\b': 'HYDERABAD',
            r'\bKOL\b': 'KOLKATA',
            r'\bPUN\b': 'PUNE'
        }

        for abbr, full_form in city_state_mapping.items():
            address_str = re.sub(abbr, full_form, address_str)

        address_str = re.sub(r'\s+', ' ', address_str).strip()
        if not address_str or len(address_str) < 5:
            return ""

        return address_str

    for vendor in vendors:
        addresses = vendor.get("addresses", {})
        address_str = ""

        if addresses and isinstance(addresses, dict) and addresses:
            address_str = max(addresses.values(), key=lambda x: len(x.strip() if isinstance(x, str) else ""), default="")

        standardized_address = standardize_address(address_str)
        vendor["extracted_address"] = standardized_address if standardized_address else None


    state_codes = {
        '01': 'Jammu and Kashmir',
        '02': 'Himachal Pradesh',
        '03': 'Punjab',
        '04': 'Chandigarh',
        '05': 'Uttarakhand',
        '06': 'Haryana',
        '07': 'Delhi',
        '08': 'Rajasthan',
        '09': 'Uttar Pradesh',
        '10': 'Bihar',
        '11': 'Sikkim',
        '12': 'Arunachal Pradesh',
        '13': 'Nagaland',
        '14': 'Manipur',
        '15': 'Mizoram',
        '16': 'Tripura',
        '17': 'Meghalaya',
        '18': 'Assam',
        '19': 'West Bengal',
        '20': 'Jharkhand',
        '21': 'Odisha',
        '22': 'Chhattisgarh',
        '23': 'Madhya Pradesh',
        '24': 'Gujarat',
        '26': 'Dadra and Nagar Haveli and Daman and Diu',
        '27': 'Maharashtra',
        '28': 'Andhra Pradesh',
        '29': 'Karnataka',
        '30': 'Goa',
        '31': 'Lakshadweep',
        '32': 'Kerala',
        '33': 'Tamil Nadu',
        '34': 'Puducherry',
        '35': 'Andaman and Nicobar Islands',
        '36': 'Telangana',
        '37': 'Andhra Pradesh (New)',
        '38': 'Ladakh',
        '97': 'Other Territory'
    }

    entity_types = {
        '0': 'Government Department',
        '1': 'Private Limited Company',
        '2': 'Public Limited Company',
        '3': 'Public Sector Undertaking',
        '4': 'Unlimited Company',
        '5': 'Limited Liability Partnership',
        '6': 'Partnership Firm',
        '7': 'Proprietorship',
        '8': 'Branch/Division',
        '9': 'Others'
    }

    def validate_pan(pan):
        pattern = r'^[A-Z]{5}[0-9]{4}[A-Z]$'
        return bool(re.match(pattern, pan))

    def extract_gst_details(gstin):
        if not gstin or len(gstin) != 15:
            return {'error': 'Invalid GSTIN length'}

        state_code = gstin[:2]
        pan = gstin[2:12]
        entity_code = gstin[12]
        check_character = gstin[14]
        state_name = state_codes.get(state_code)

        if not validate_pan(pan):
            return {'error': 'Invalid PAN format'}

        entity_type = entity_types.get(entity_code, 'Other Entity')

        if gstin[13] != 'Z':
            return {'error': 'Invalid 14th character'}

        dealer_type = "Regular" if entity_code not in ['0', '3'] else "Government"
        status = "Active" if check_character.isalnum() else "Inactive"

        return {
            'pan': pan.upper() if pan else '',
            'state': state_name.upper() if state_name else '',
            'entity_type': entity_type.upper() if entity_type else '',
            'dealer_type': dealer_type.upper() if dealer_type else '',
            'status': status.upper()
        }

    def process_vendor(vendor):
        gstin = vendor.get('gstNo')
        address = vendor.get('extracted_address', '')
        data = {
            'gstin': gstin.upper() if gstin else '',
            'trade_name': vendor.get('vendorName', '').upper(),
            'pan': '',
            'dealer_type': '',
            'entity_type': '',
            'status': '',
            'state': '',
            'address': address,
            'source': '{' + ','.join(map(str, vendor.get('servingTenantIds', []))) + '}' if vendor.get('servingTenantIds') else None,
            'top_categories': json.dumps(vendor.get('topCategories', {})).upper() if vendor.get('topCategories') else '{}'
        }

        if gstin:
            gst_details = extract_gst_details(gstin)
            if gst_details:
                data.update({
                    'pan': gst_details.get('pan', ''),
                    'dealer_type': gst_details.get('dealer_type', ''),
                    'entity_type': gst_details.get('entity_type', ''),
                    'status': gst_details.get('status', ''),
                    'state': gst_details.get('state', '')
                })

        if not data['state'] and address:
            extracted_state = extract_state_from_address(address, use_geopy=False)
            if extracted_state:
                data['state'] = extracted_state.upper()
                if not data['dealer_type']:
                    data['dealer_type'] = 'REGULAR'
                if not data['status']:
                    data['status'] = 'ACTIVE'

        return data

    def insert_batch(conn):
        if not batch:
            return
        keys = batch[0].keys()
        data_iter = (tuple(d[k] for k in keys) for d in batch)
        psql_insert_copy(vendor_table, conn, keys, data_iter)
        print(f"Inserted {len(batch)} vendors into PostgreSQL.")
        batch.clear()

    counter = 0

    with engine.begin() as conn, ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(process_vendor, vendor) for vendor in vendors]

        for future in as_completed(futures):
            data = future.result()
            batch.append(data)
            if len(batch) >= batch_size:
                counter += 1
                print(f"Processing batch {counter}")
                insert_batch(conn)
        if batch:
            insert_batch(conn)

    print(f"Total vendors inserted: {len(vendors)}")

def main():
    config = masterdataupdateconfigs.find_one({"type": "masterdataUpdate"})
    sources = {
        client["full"]: client["tenantId"]
        for client in sorted(config['clients'], key=lambda x: (x.get('order', None) is None, x.get('order', None) or 0))
    }

    sheets = {
        "inventory_master": {
            "filename": "inventory master.csv",
            "transform_func": transform_inventory_master
        },
        "packaging_master": {
            "filename": "packagingmasters.csv",
            "transform_func": transform_packaging_master
        }
    }

    start_time = time.time()

    print("\nProcessing vendor master for all clients...")
    tenant_ids =[]
    for client, source in sources.items():
        active = roloposconfigs.find_one({"status.account": True, 'tenantId': source})
        if active:
            tenant_ids.append(source)
    process_vendors(postgres_client, tenant_ids)

    create_tables(postgres_client)
    print("\nProcessing inventory master for all clients...")
    for client, source in sources.items():
        active = roloposconfigs.find_one({"status.account": True, 'tenantId': source})
        if not active:
            continue
        process_data(postgres_client, client, source, "inventory_master", sheets["inventory_master"])

    print("\nProcessing packaging master for all clients...")
    for client, source in sources.items():
        active = roloposconfigs.find_one({"status.account": True, 'tenantId': source})
        if not active:
            continue
        process_data(postgres_client, client, source, "packaging_master", sheets["packaging_master"])

    print(f"\nTotal processing time: {time.time() - start_time:.2f} seconds")

if __name__ == "__main__":
    main()